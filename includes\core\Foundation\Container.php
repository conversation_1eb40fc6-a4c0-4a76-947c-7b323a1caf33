<?php
declare(strict_types=1);

namespace NTWP\Core\Foundation;

/**
 * Notion 依赖注入容器类
 *
 * 实现简单的依赖注入容器，提升代码的可测试性和可维护性
 * 分离数据访问层和业务逻辑层
 *
 * @since      2.0.0-beta.1
 * @version    2.0.0-beta.2
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-Loong/Notion-to-WordPress
 */

// 如果直接访问此文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

class Container {
    
    /**
     * 服务实例存储
     * @var array
     */
    private static $instances = [];
    
    /**
     * 服务定义存储
     * @var array
     */
    private static $definitions = [];
    
    /**
     * 单例模式标记
     * @var array
     */
    private static $singletons = [];
    
    /**
     * 绑定服务到容器
     *
     * @since 2.0.0-beta.2
     * @param string $abstract 抽象名称
     * @param callable|string|object $concrete 具体实现
     * @param bool $singleton 是否为单例
     */
    public static function bind(string $abstract, $concrete, bool $singleton = false): void {
        self::$definitions[$abstract] = $concrete;

        if ($singleton) {
            self::$singletons[$abstract] = true;
        }
    }

    /**
     * 注册单例服务
     *
     * @since 2.0.0-beta.2
     * @param string $abstract 抽象名称
     * @param callable|string|object $concrete 具体实现
     */
    public static function singleton(string $abstract, $concrete): void {
        self::bind($abstract, $concrete, true);
    }

    /**
     * 解析服务实例
     *
     * @since 2.0.0-beta.2
     * @param string $abstract 抽象名称
     * @return mixed 服务实例
     * @throws \Exception 当服务未注册时
     */
    public static function resolve(string $abstract) {
        // 检查是否已有实例（单例模式）
        if (isset(self::$instances[$abstract])) {
            return self::$instances[$abstract];
        }

        // 检查是否已注册
        if (!isset(self::$definitions[$abstract])) {
            throw new \Exception("Service '{$abstract}' is not registered in the container.");
        }

        $concrete = self::$definitions[$abstract];
        $instance = self::build($concrete);

        // 如果是单例，缓存实例
        if (isset(self::$singletons[$abstract])) {
            self::$instances[$abstract] = $instance;
        }

        return $instance;
    }

    /**
     * 构建服务实例
     *
     * @since 2.0.0-beta.2
     * @param mixed $concrete 具体实现
     * @return mixed 构建的实例
     */
    private static function build($concrete) {
        // 如果是闭包，直接调用
        if ($concrete instanceof \Closure) {
            return $concrete();
        }

        // 如果是字符串类名，实例化
        if (is_string($concrete)) {
            if (class_exists($concrete)) {
                return new $concrete();
            }
            return $concrete; // 返回字符串本身（用于静态类）
        }

        // 如果已经是对象，直接返回
        if (is_object($concrete)) {
            return $concrete;
        }

        throw new \Exception("Cannot build service from concrete type: " . gettype($concrete));
    }

    /**
     * 检查服务是否已注册
     *
     * @since 2.0.0-beta.2
     * @param string $abstract 抽象名称
     * @return bool 是否已注册
     */
    public static function bound(string $abstract): bool {
        return isset(self::$definitions[$abstract]);
    }

    /**
     * 移除服务注册
     *
     * @since 2.0.0-beta.2
     * @param string $abstract 抽象名称
     */
    public static function forget(string $abstract): void {
        unset(self::$definitions[$abstract]);
        unset(self::$instances[$abstract]);
        unset(self::$singletons[$abstract]);
    }

    /**
     * 清空所有服务
     *
     * @since 2.0.0-beta.2
     */
    public static function flush(): void {
        self::$definitions = [];
        self::$instances = [];
        self::$singletons = [];
    }

    /**
     * 注册服务（向后兼容）
     *
     * @deprecated 2.0.0-beta.2 使用 bind() 替代
     * @since 2.0.0-beta.1
     * @param string $name 服务名称
     * @param callable|string $definition 服务定义
     * @param bool $singleton 是否为单例
     */
    public static function register(string $name, $definition, bool $singleton = true): void {
        self::$definitions[$name] = $definition;
        self::$singletons[$name] = $singleton;
    }
    
    /**
     * 获取服务实例
     *
     * @since 2.0.0-beta.1
     * @param string $name 服务名称
     * @return mixed 服务实例
     * @throws Exception 当服务未注册时
     */
    public static function get(string $name): mixed {
        // 检查是否为单例且已实例化
        if (self::$singletons[$name] ?? false) {
            if (isset(self::$instances[$name])) {
                return self::$instances[$name];
            }
        }
        
        // 检查服务是否已注册
        if (!isset(self::$definitions[$name])) {
            throw \NTWP\Core\Foundation\Exception::validation("Service '{$name}' is not registered");
        }
        
        $definition = self::$definitions[$name];
        
        // 创建实例（增强异常处理）
        try {
            if (is_callable($definition)) {
                $instance = $definition();
            } elseif (is_string($definition) && class_exists($definition)) {
                $instance = new $definition();
            } else {
                throw \NTWP\Core\Foundation\Exception::validation("Invalid service definition for '{$name}': " . gettype($definition));
            }
        } catch (Throwable $e) {
            // 提供更详细的错误信息
            $error_message = sprintf(
                "Failed to create service '%s': %s in %s:%d",
                $name,
                $e->getMessage(),
                $e->getFile(),
                $e->getLine()
            );
            
            // 记录错误（如果日志系统可用）
            if (class_exists('NTWP\Core\Foundation\Logger')) {
                \NTWP\Core\Foundation\Logger::errorLog($error_message, 'Dependency Container');
            }
            
            throw \NTWP\Core\Foundation\Exception::from($e, 'CONTAINER_ERROR', ['service_name' => $name]);
        }
        
        // 存储单例实例
        if (self::$singletons[$name] ?? false) {
            self::$instances[$name] = $instance;
        }
        
        return $instance;
    }
    
    /**
     * 检查服务是否已注册
     *
     * @since 2.0.0-beta.1
     * @param string $name 服务名称
     * @return bool 是否已注册
     */
    public static function has(string $name): bool {
        return isset(self::$definitions[$name]);
    }
    
    /**
     * 移除服务
     *
     * @since 2.0.0-beta.1
     * @param string $name 服务名称
     */
    public static function remove(string $name): void {
        unset(self::$definitions[$name], self::$instances[$name], self::$singletons[$name]);
    }
    
    /**
     * 清空所有服务
     *
     * @since 2.0.0-beta.1
     */
    public static function clear(): void {
        self::$definitions = [];
        self::$instances = [];
        self::$singletons = [];
    }
    
    /**
     * 初始化核心服务
     *
     * @deprecated 2.0.0-beta.2 使用 ServiceProvider::register() 替代
     * @since 2.0.0-beta.1
     */
    public static function initCoreServices(): void {
        // 🚨 已废弃：此方法已被 ServiceProvider::register() 替代
        // 为了向后兼容保留，但不再使用

        if (class_exists('\\NTWP\\Core\\Foundation\\Logger')) {
            \NTWP\Core\Foundation\Logger::warningLog(
                'Container::init_core_services() 已废弃，请使用 ServiceProvider::register()',
                'Container'
            );
        }

        // 如果ServiceProvider还未注册，则调用它
        if (class_exists('\\NTWP\\Core\\Foundation\\ServiceProvider')) {
            \NTWP\Core\Foundation\ServiceProvider::register();
            return;
        }

        // 向后兼容：如果ServiceProvider不存在，使用旧的注册方式
        // === 新架构服务 ===

        // 注册统一缓存服务 (替代Smart_Cache)
        self::register('cache', function() {
            return new \NTWP\Infrastructure\Cache\CacheManager();
        });

        // 注册统一并发管理服务 (替代Unified_Concurrency_Manager)
        self::register('concurrency', function() {
            return new \NTWP\Infrastructure\Concurrency\ConcurrencyManager();
        });

        // 注册内存监控服务 (替代Memory_Manager)
        self::register('memory', function() {
            return new \NTWP\Infrastructure\Memory\MemoryManager();
        });

        // 注册流处理服务 (Memory_Manager拆分)
        self::register('stream_processor', function() {
            return new \NTWP\Core\Network\StreamProcessor();
        });

        // 注册批量优化服务 (Memory_Manager拆分)
        self::register('batch_optimizer', function() {
            return new \NTWP\Core\Performance\BatchOptimizer();
        });

        // 注册垃圾回收服务 (Memory_Manager拆分)
        self::register('garbage_collector', function() {
            return new \NTWP\Infrastructure\Memory\GarbageCollector();
        });

        // === 保留的核心服务 ===

        // 注册统一数据库管理服务 (替代Database_Helper、Database_Index_Manager、Database_Index_Optimizer)
        self::register('database', function() {
            return new \NTWP\Infrastructure\Database\DatabaseManager();
        });

        // 注册数据库管理器服务
        self::register('database_manager', function() {
            return new \NTWP\Infrastructure\Database\DatabaseManager();
        });

        // 注册日志服务
        self::register('logger', function() {
            return new \NTWP\Core\Foundation\Logger();
        });

        // 注册网络管理服务 (待后续整合到ConcurrencyManager)
        self::register('network', function() {
            return new \NTWP\Infrastructure\Concurrency\ConcurrencyManager();
        });
        
        // 注册任务调度服务（单例模式）
        self::register('scheduler', function() {
            return \NTWP\Core\Task\AsyncTaskScheduler::getInstance();
        });
        
        // 注册API合并服务
        self::register('api_merger', function() {
            return new \NTWP\Utils\SmartApiMerger();
        });

        // === 向后兼容适配器 ===

        // 注意：SmartCache适配器已移除，直接使用CacheManager
        // 原smart_cache_adapter已废弃，请使用 'cache' 服务

        // Memory_Manager适配器 (委托给新的专职类)
        self::register('memory_manager_adapter', function() {
            return new \NTWP\Infrastructure\Memory\MemoryManager(); // 使用适配器类
        });

        // Unified_Concurrency_Manager适配器 (委托给ConcurrencyManager)
        // 注意：根据映射关系，Unified_Concurrency_Manager已删除，功能合并到ConcurrencyManager
        self::register('unified_concurrency_adapter', function() {
            return self::resolve('concurrency'); // 委托给ConcurrencyManager
        });

        // Database_Helper适配器 (委托给DatabaseManager)
        self::register('database_helper_adapter', function() {
            return self::resolve('database_manager');
        });

        // Database_Index_Manager适配器 (委托给DatabaseManager)
        self::register('database_index_manager_adapter', function() {
            return self::resolve('database_manager');
        });

        // Database_Index_Optimizer适配器 (委托给DatabaseManager)
        self::register('database_index_optimizer_adapter', function() {
            return self::resolve('database_manager');
        });

        // === 内容处理服务 ===

        // ContentProcessingService (新的统一内容处理服务)
        self::register('content_processing_service', function() {
            return new \NTWP\Services\Content\ContentConverter();
        });

        // Content_Converter适配器 (委托给ContentProcessingService)
        self::register('content_converter_adapter', function() {
            return new \NTWP\Services\Content\ContentConverter();
        });
    }
    
    /**
     * 获取服务列表
     *
     * @since 2.0.0-beta.1
     * @return array 服务列表
     */
    public static function getServices(): array {
        return array_keys(self::$definitions);
    }
    
    /**
     * 获取服务状态
     *
     * @since 2.0.0-beta.1
     * @return array 服务状态信息
     */
    public static function getStatus(): array {
        $status = [
            'total_services' => count(self::$definitions),
            'instantiated_services' => count(self::$instances),
            'singleton_services' => count(array_filter(self::$singletons)),
            'services' => []
        ];
        
        foreach (self::$definitions as $name => $definition) {
            $status['services'][$name] = [
                'registered' => true,
                'instantiated' => isset(self::$instances[$name]),
                'singleton' => self::$singletons[$name] ?? false,
                'type' => is_callable($definition) ? 'callable' : 'class'
            ];
        }
        
        return $status;
    }
}

/**
 * 服务接口基类
 * 
 * 为所有服务提供统一的接口规范
 */
interface Service_Interface {
    
    /**
     * 初始化服务
     */
    public function init(): void;
    
    /**
     * 获取服务状态
     */
    public function getStatus(): array;
}

/**
 * 抽象服务基类
 * 
 * 为服务实现提供通用功能
 */
abstract class Abstract_Service implements Service_Interface {
    
    /**
     * 服务是否已初始化
     * @var bool
     */
    protected $initialized = false;
    
    /**
     * 服务配置
     * @var array
     */
    protected $config = [];
    
    /**
     * 构造函数
     *
     * @param array $config 服务配置
     */
    public function __construct(array $config = []) {
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }
    
    /**
     * 获取默认配置
     *
     * @return array 默认配置
     */
    protected function getDefaultConfig(): array {
        return [];
    }
    
    /**
     * 检查服务是否已初始化
     *
     * @return bool 是否已初始化
     */
    public function isInitialized(): bool {
        return $this->initialized;
    }
    
    /**
     * 获取配置值
     *
     * @param string $key 配置键
     * @param mixed $default 默认值
     * @return mixed 配置值
     */
    protected function getConfig(string $key, $default = null) {
        return $this->config[$key] ?? $default;
    }
    
    /**
     * 设置配置值
     *
     * @param string $key 配置键
     * @param mixed $value 配置值
     */
    protected function setConfig(string $key, $value): void {
        $this->config[$key] = $value;
    }
    
    /**
     * 获取服务状态（默认实现）
     *
     * @return array 服务状态
     */
    public function getStatus(): array {
        return [
            'initialized' => $this->initialized,
            'config' => $this->config,
            'class' => get_class($this)
        ];
    }
}
