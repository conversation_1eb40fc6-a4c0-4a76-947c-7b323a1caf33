/**
 * 标签页管理器组件
 */

// React imports handled by JSX transform
import { useAppContext, useActiveTab } from '../context/AppContext';
import { useI18n, useLocalStorage } from '../hooks/useWordPress';
import type { TabType } from '../types';
import { ApiSettingsTab } from './ApiSettingsTab';
import { FieldMappingTab } from './FieldMappingTab';
import { PerformanceConfigTab } from './PerformanceConfigTab';
import { PerformanceTab } from './PerformanceTab';
import { OtherSettingsTab } from './OtherSettingsTab';
import { DatabaseTab } from './DatabaseTab';
import { DebugTab } from './DebugTab';
import { HelpTab } from './HelpTab';
import { AboutAuthorTab } from './AboutAuthorTab';

// 标签页配置
const TAB_CONFIG: Array<{
  id: TabType;
  label: string;
  icon: string;
}> = [
  { id: 'api-settings', label: '🔄 同步设置', icon: 'admin-settings' },
  { id: 'field-mapping', label: '🔗 字段映射', icon: 'admin-links' },
  { id: 'database-view', label: '🗄️ 数据库视图', icon: 'database' },
  { id: 'performance-config', label: '⚡ 性能配置', icon: 'performance' },
  { id: 'performance', label: '📊 性能监控', icon: 'chart-bar' },
  { id: 'other-settings', label: '⚙️ 其他设置', icon: 'admin-generic' },
  { id: 'debug', label: '🐞 调试工具', icon: 'admin-tools' },
  { id: 'help', label: '📖 使用帮助', icon: 'editor-help' },
  { id: 'about-author', label: '👨‍💻 关于作者', icon: 'admin-users' },
];

// 侧边栏组件
function Sidebar() {
  const { actions } = useAppContext();
  const activeTab = useActiveTab();
  const { __ } = useI18n();
  const [, setStoredTab] = useLocalStorage('notion_wp_active_tab', 'api-settings');

  const handleTabClick = (tabId: TabType) => {
    actions.setActiveTab(tabId);
    setStoredTab(tabId);
  };

  return (
    <div className="notion-wp-sidebar">
      <div className="notion-wp-menu">
        {TAB_CONFIG.map(tab => (
          <button
            key={tab.id}
            className={`notion-wp-menu-item ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => handleTabClick(tab.id)}
            data-tab={tab.id}
          >
            {__(tab.label, tab.label)}
          </button>
        ))}
      </div>
    </div>
  );
}

// 内容区域组件
function ContentArea() {
  const activeTab = useActiveTab();

  // 动态导入标签页内容组件
  const renderTabContent = () => {
    switch (activeTab) {
      case 'api-settings':
        return <ApiSettingsTab />;
      case 'field-mapping':
        return <FieldMappingTab />;
      case 'database-view':
        return <DatabaseTab />;
      case 'performance-config':
        return <PerformanceConfigTab />;
      case 'performance':
        return <PerformanceTab />;
      case 'other-settings':
        return <OtherSettingsTab />;
      case 'debug':
        return <DebugTab />;
      case 'help':
        return <HelpTab />;
      case 'about-author':
        return <AboutAuthorTab />;
      default:
        return <ApiSettingsTab />;
    }
  };

  return (
    <div className="notion-wp-content">
      <form id="notion-to-wordpress-settings-form" method="post" action="admin-post.php">
        <input type="hidden" name="action" value="notion_to_wordpress_options" />
        {/* WordPress nonce 会在PHP端处理 */}
        
        <div className="notion-wp-tab-content active">
          {renderTabContent()}
        </div>
      </form>
    </div>
  );
}

// 主标签页管理器
export function TabManager() {
  return (
    <>
      <Sidebar />
      <ContentArea />
    </>
  );
}

// 所有组件都已实现
