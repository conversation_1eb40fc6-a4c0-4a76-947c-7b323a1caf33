<?php
declare(strict_types=1);

namespace NTWP\Core\Performance;

/**
 * Notion 性能监控器类
 * 
 * 专门处理插件的性能监控功能，包括同步速度统计、资源使用监控、
 * 性能指标收集等。帮助用户了解优化效果和系统性能状况。
 * 
 * @since      2.0.0-beta.1
 * @version    2.0.0-beta.2
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-Loong/Notion-to-WordPress
 */

// 如果直接访问此文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

class PerformanceMonitor {
    
    /**
     * 性能统计数据
     *
     * @access private
     * @var array
     */
    private static array $stats = [];
    
    /**
     * 计时器
     *
     * @access private
     * @var array
     */
    private static array $timers = [];
    
    /**
     * 内存使用记录
     *
     * @access private
     * @var array
     */
    private static array $memory_usage = [];
    
    /**
     * 开始计时
     *
     * @since 2.0.0-beta.1
     * @param string $name 计时器名称
     */
    public static function startTimer(string $name): void {
        self::$timers[$name] = [
            'start' => microtime(true),
            'memory_start' => memory_get_usage(true)
        ];
    }
    
    /**
     * 结束计时
     *
     * @since 2.0.0-beta.1
     * @param string $name 计时器名称
     * @return float 执行时间（秒）
     */
    public static function endTimer(string $name): float {
        if (!isset(self::$timers[$name])) {
            return 0.0;
        }
        
        $end_time = microtime(true);
        $end_memory = memory_get_usage(true);
        
        $duration = $end_time - self::$timers[$name]['start'];
        $memory_used = $end_memory - self::$timers[$name]['memory_start'];
        
        // 记录统计数据
        if (!isset(self::$stats[$name])) {
            self::$stats[$name] = [
                'count' => 0,
                'total_time' => 0,
                'min_time' => PHP_FLOAT_MAX,
                'max_time' => 0,
                'total_memory' => 0,
                'min_memory' => PHP_INT_MAX,
                'max_memory' => 0
            ];
        }
        
        self::$stats[$name]['count']++;
        self::$stats[$name]['total_time'] += $duration;
        self::$stats[$name]['min_time'] = min(self::$stats[$name]['min_time'], $duration);
        self::$stats[$name]['max_time'] = max(self::$stats[$name]['max_time'], $duration);
        self::$stats[$name]['total_memory'] += $memory_used;
        self::$stats[$name]['min_memory'] = min(self::$stats[$name]['min_memory'], $memory_used);
        self::$stats[$name]['max_memory'] = max(self::$stats[$name]['max_memory'], $memory_used);
        
        unset(self::$timers[$name]);
        
        return $duration;
    }
    
    /**
     * 记录数据库操作统计
     *
     * @since 2.0.0-beta.1
     * @param string $operation 操作类型
     * @param int $affected_rows 影响的行数
     * @param float $duration 执行时间
     */
    public static function recordDbOperation(string $operation, int $affected_rows, float $duration): void {
        $key = 'db_' . $operation;

        if (!isset(self::$stats[$key])) {
            self::$stats[$key] = [
                'count' => 0,
                'total_rows' => 0,
                'total_time' => 0,
                'min_time' => PHP_FLOAT_MAX,
                'max_time' => 0
            ];
        }

        self::$stats[$key]['count']++;
        self::$stats[$key]['total_rows'] += $affected_rows;
        self::$stats[$key]['total_time'] += $duration;
        self::$stats[$key]['min_time'] = min(self::$stats[$key]['min_time'], $duration);
        self::$stats[$key]['max_time'] = max(self::$stats[$key]['max_time'], $duration);
    }

    /**
     * 记录自定义指标统计
     *
     * @since 2.0.0-beta.1
     * @param string $metric_name 指标名称
     * @param mixed $value 指标数值
     */
    public static function recordCustomMetric(string $metric_name, $value): void {
        $key = 'custom_' . $metric_name;

        // 确保数值类型
        $numeric_value = is_numeric($value) ? (float)$value : 0.0;

        if (!isset(self::$stats[$key])) {
            self::$stats[$key] = [
                'count' => 0,
                'total_value' => 0.0,
                'min_value' => PHP_FLOAT_MAX,
                'max_value' => 0.0,
                'last_value' => 0.0
            ];
        }

        self::$stats[$key]['count']++;
        self::$stats[$key]['total_value'] += $numeric_value;
        self::$stats[$key]['min_value'] = min(self::$stats[$key]['min_value'], $numeric_value);
        self::$stats[$key]['max_value'] = max(self::$stats[$key]['max_value'], $numeric_value);
        self::$stats[$key]['last_value'] = $numeric_value;
    }
    
    /**
     * 获取性能统计报告
     *
     * @since 2.0.0-beta.1
     * @return array 性能统计数据
     */
    public static function getPerformanceReport(): array {
        $report = [
            'summary' => [
                'total_operations' => 0,
                'total_time' => 0,
                'peak_memory' => memory_get_peak_usage(true),
                'current_memory' => memory_get_usage(true)
            ],
            'timers' => [],
            'api_calls' => [],
            'db_operations' => [],
            'custom_metrics' => []
        ];
        
        foreach (self::$stats as $name => $data) {
            $report['summary']['total_operations'] += $data['count'];
            $report['summary']['total_time'] += $data['total_time'];
            
            $avg_time = $data['count'] > 0 ? $data['total_time'] / $data['count'] : 0;
            
            $formatted_data = [
                'name' => $name,
                'count' => $data['count'],
                'total_time' => round($data['total_time'], 4),
                'avg_time' => round($avg_time, 4),
                'min_time' => $data['min_time'] === PHP_FLOAT_MAX ? 0 : round($data['min_time'], 4),
                'max_time' => round($data['max_time'], 4)
            ];
            
            if (strpos($name, 'api_') === 0) {
                $formatted_data['success_rate'] = $data['count'] > 0
                    ? round(($data['success_count'] / $data['count']) * 100, 2)
                    : 0;
                $report['api_calls'][] = $formatted_data;
            } elseif (strpos($name, 'db_') === 0) {
                $formatted_data['total_rows'] = $data['total_rows'];
                $formatted_data['avg_rows'] = $data['count'] > 0
                    ? round($data['total_rows'] / $data['count'], 2)
                    : 0;
                $report['db_operations'][] = $formatted_data;
            } elseif (strpos($name, 'custom_') === 0) {
                $formatted_data['total_value'] = round($data['total_value'], 4);
                $formatted_data['avg_value'] = $data['count'] > 0
                    ? round($data['total_value'] / $data['count'], 4)
                    : 0;
                $formatted_data['min_value'] = $data['min_value'] === PHP_FLOAT_MAX ? 0 : round($data['min_value'], 4);
                $formatted_data['max_value'] = round($data['max_value'], 4);
                $formatted_data['last_value'] = round($data['last_value'], 4);
                $report['custom_metrics'][] = $formatted_data;
            } else {
                if (isset($data['total_memory'])) {
                    $formatted_data['total_memory'] = $data['total_memory'];
                    $formatted_data['avg_memory'] = $data['count'] > 0
                        ? round($data['total_memory'] / $data['count'])
                        : 0;
                    $formatted_data['min_memory'] = $data['min_memory'] === PHP_INT_MAX ? 0 : $data['min_memory'];
                    $formatted_data['max_memory'] = $data['max_memory'];
                }
                $report['timers'][] = $formatted_data;
            }
        }
        
        return $report;
    }
    
    /**
     * 获取原始性能指标数据
     *
     * @since 2.0.0-beta.1
     * @return array 原始性能指标数据
     */
    public static function getMetrics(): array {
        $metrics = [];

        // 提取自定义指标的最新值
        foreach (self::$stats as $name => $data) {
            if (strpos($name, 'custom_') === 0) {
                $metric_name = substr($name, 7); // 移除 'custom_' 前缀
                $metrics[$metric_name] = $data['last_value'] ?? 0;
            }
        }

        return $metrics;
    }

    /**
     * 重置性能统计
     *
     * @since 2.0.0-beta.1
     */
    public static function resetStats(): void {
        self::$stats = [];
        self::$timers = [];
        self::$memory_usage = [];
    }
    
    /**
     * 获取当前配置的性能参数
     *
     * @since 2.0.0-beta.1
     * @return array 性能配置参数
     */
    public static function getPerformanceConfig(): array {
        $options = get_option('notion_to_wordpress_options', []);
        
        return [
            'api_page_size' => $options['api_page_size'] ?? 100,
            'concurrent_requests' => $options['concurrent_requests'] ?? 5,
            'batch_size' => $options['batch_size'] ?? 20,
            'log_buffer_size' => $options['log_buffer_size'] ?? 50,
            'enable_performance_mode' => $options['enable_performance_mode'] ?? 1,
            'log_buffer_status' => class_exists('NTWP\Core\Foundation\Logger')
                ? \NTWP\Core\Foundation\Logger::getBufferStatus()
                : ['buffer_size' => 0, 'current_count' => 0, 'usage_percentage' => 0]
        ];
    }
    
    /**
     * 格式化字节数为可读格式
     *
     * @since 2.0.0-beta.1
     * @param int $bytes 字节数
     * @return string 格式化后的字符串
     */
    public static function formatBytes(int $bytes): string {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen((string)$bytes) - 1) / 3);

        return sprintf("%.2f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }

    // ==================== 🚀 第三阶段架构优化：增强性能监控 ====================

    /**
     * 记录API调用性能（增强版）
     *
     * @since 2.0.0-beta.2
     * @param string $endpoint API端点
     * @param float $duration 调用时长（秒）
     * @param bool $success 是否成功
     * @param array $context 上下文信息
     */    public static function recordApiCall(string $endpoint, float $duration, bool $success, array $context = []): void {

        $record = [
            'endpoint' => $endpoint,
            'duration' => round($duration * 1000, 2), // 转换为毫秒
            'success' => $success,
            'timestamp' => time(),
            'memory_usage' => memory_get_usage(true),
            'context' => $context
        ];

        if (!isset(self::$stats['api_calls'])) {
            self::$stats['api_calls'] = [];
        }

        self::$stats['api_calls'][] = $record;

        // 记录到日志（如果启用详细日志）
        if (defined('WP_DEBUG') && WP_DEBUG && class_exists('\\NTWP\\Core\\Foundation\\Logger')) {
            $status = $success ? '成功' : '失败';
            \NTWP\Core\Foundation\Logger::debugLog(
                "API调用 {$endpoint}: {$duration}ms ({$status})",
                'Performance Monitor'
            );
        }
    }

    /**
     * 记录错误信息（增强版）
     *
     * @since 2.0.0-beta.2
     * @param string $type 错误类型
     * @param string $message 错误消息
     * @param array $context 上下文信息
     */
    public static function recordError(string $type, string $message, array $context = []): void {
        $record = [
            'type' => $type,
            'message' => $message,
            'context' => $context,
            'timestamp' => time(),
            'memory_usage' => memory_get_usage(true)
        ];

        if (!isset(self::$stats['errors'])) {
            self::$stats['errors'] = [];
        }

        self::$stats['errors'][] = $record;
    }

    /**
     * 获取综合性能报告（增强版）
     *
     * @since 2.0.0-beta.2
     * @return array 综合性能报告
     */
    public static function getComprehensiveReport(): array {
        $api_calls = self::$stats['api_calls'] ?? [];
        $errors = self::$stats['errors'] ?? [];
        $timers = self::$timers ?? [];

        // API性能分析
        $api_analysis = [];
        if (!empty($api_calls)) {
            $durations = array_column($api_calls, 'duration');
            $successful_calls = array_filter($api_calls, fn($call) => $call['success']);

            $api_analysis = [
                'total_calls' => count($api_calls),
                'successful_calls' => count($successful_calls),
                'success_rate' => round((count($successful_calls) / count($api_calls)) * 100, 2),
                'avg_duration' => round(array_sum($durations) / count($durations), 2),
                'min_duration' => min($durations),
                'max_duration' => max($durations),
                'slow_calls' => count(array_filter($api_calls, fn($call) => $call['duration'] > 1000))
            ];
        }

        // 内存分析
        $memory_analysis = [
            'current_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'peak_usage_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'memory_records' => count(self::$memory_usage)
        ];

        // 错误分析
        $error_analysis = [];
        if (!empty($errors)) {
            $error_types = array_count_values(array_column($errors, 'type'));
            $error_analysis = [
                'total_errors' => count($errors),
                'error_types' => $error_types,
                'recent_errors' => array_slice($errors, -5)
            ];
        }

        // 生成优化建议
        $recommendations = self::generateOptimizationRecommendations($api_analysis, $memory_analysis, $error_analysis);

        return [
            'summary' => [
                'monitoring_duration' => time() - (self::$stats['start_time'] ?? time()),
                'total_timers' => count($timers),
                'total_api_calls' => count($api_calls),
                'total_errors' => count($errors)
            ],
            'api_performance' => $api_analysis,
            'memory_usage' => $memory_analysis,
            'error_analysis' => $error_analysis,
            'recommendations' => $recommendations,
            'raw_stats' => self::$stats
        ];
    }

    /**
     * 生成优化建议
     *
     * @since 2.0.0-beta.2
     * @param array $api_analysis API分析数据
     * @param array $memory_analysis 内存分析数据
     * @param array $error_analysis 错误分析数据
     * @return array 优化建议
     */
    private static function generateOptimizationRecommendations(array $api_analysis, array $memory_analysis, array $error_analysis): array {
        $recommendations = [];

        // API性能建议
        if (!empty($api_analysis)) {
            if ($api_analysis['avg_duration'] > 500) {
                $recommendations[] = [
                    'type' => 'api_performance',
                    'priority' => 'high',
                    'message' => "API调用平均耗时较长 ({$api_analysis['avg_duration']}ms)，建议启用缓存或优化请求"
                ];
            }

            if ($api_analysis['success_rate'] < 95) {
                $recommendations[] = [
                    'type' => 'api_reliability',
                    'priority' => 'high',
                    'message' => "API成功率较低 ({$api_analysis['success_rate']}%)，建议检查网络连接和API配置"
                ];
            }

            if ($api_analysis['slow_calls'] > 0) {
                $recommendations[] = [
                    'type' => 'api_optimization',
                    'priority' => 'medium',
                    'message' => "检测到 {$api_analysis['slow_calls']} 个慢API调用，建议优化或启用缓存"
                ];
            }
        }

        // 内存使用建议
        if ($memory_analysis['current_usage_mb'] > 64) {
            $recommendations[] = [
                'type' => 'memory_usage',
                'priority' => 'medium',
                'message' => "当前内存使用较高 ({$memory_analysis['current_usage_mb']}MB)，建议优化内存使用"
            ];
        }

        if ($memory_analysis['peak_usage_mb'] > 128) {
            $recommendations[] = [
                'type' => 'memory_peak',
                'priority' => 'high',
                'message' => "峰值内存使用过高 ({$memory_analysis['peak_usage_mb']}MB)，可能存在内存泄漏"
            ];
        }

        // 错误处理建议
        if (!empty($error_analysis) && $error_analysis['total_errors'] > 5) {
            $recommendations[] = [
                'type' => 'error_handling',
                'priority' => 'high',
                'message' => "错误数量较多 ({$error_analysis['total_errors']})，建议检查错误日志并修复问题"
            ];
        }

        return $recommendations;
    }

    /**
     * 初始化增强性能监控
     *
     * @since 2.0.0-beta.2
     */
    public static function initEnhancedMonitoring(): void {
        if (!isset(self::$stats['start_time'])) {
            self::$stats['start_time'] = time();
        }

        // 注册WordPress钩子
        add_action('wp_footer', [self::class, 'outputPerformanceDebugInfo'], 999);
        add_action('admin_footer', [self::class, 'outputPerformanceDebugInfo'], 999);
    }

    /**
     * 输出性能调试信息
     *
     * @since 2.0.0-beta.2
     */
    public static function outputPerformanceDebugInfo(): void {
        if (!defined('WP_DEBUG') || !WP_DEBUG) {
            return;
        }

        $report = self::getComprehensiveReport();
        $summary = $report['summary'];

        echo '<!-- Notion-to-WordPress Enhanced Performance Report: ';
        echo 'API Calls: ' . $summary['total_api_calls'] . ', ';
        echo 'Errors: ' . $summary['total_errors'] . ', ';
        echo 'Memory: ' . $report['memory_usage']['current_usage_mb'] . 'MB ';
        echo '-->';
    }

    public static function getMemoryUsage(): array {
        $current = memory_get_usage(true);
        $peak = memory_get_peak_usage(true);
        $limit_string = ini_get('memory_limit');
        if (is_string($limit_string)) {
            $limit = wp_convert_hr_to_bytes($limit_string);
        } else {
            $limit = -1;
        }
        
        return [
            'current' => $current,
            'peak' => $peak,
            'limit' => $limit,
            'current_mb' => round($current / 1024 / 1024, 2),
            'peak_mb' => round($peak / 1024 / 1024, 2),
            'limit_mb' => $limit > 0 ? round($limit / 1024 / 1024, 2) : -1,
            'usage_percentage' => $limit > 0 ? round(($current / $limit) * 100, 2) : 0,
            'peak_percentage' => $limit > 0 ? round(($peak / $limit) * 100, 2) : 0
        ];
    }
}
