/**
 * 关于作者标签页组件
 */

import { useI18n } from '../hooks/useWordPress';

export function AboutAuthorTab() {
  const { __ } = useI18n();

  // 获取插件目录URL
  const pluginUrl = (window as typeof window & { notionToWp?: { pluginUrl: string } }).notionToWp?.pluginUrl || '';
  const avatarUrl = pluginUrl + 'assets/avatar.svg';

  return (
    <div className="notion-wp-settings-section">
      <h2>{__('关于作者', '关于作者')}</h2>

      <div className="author-info">
        <div className="author-avatar">
          <img
            src={avatarUrl}
            alt="Frank-Loong"
            onError={(e) => {
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
        </div>
        <div className="author-details">
          <h3><PERSON><PERSON><PERSON><PERSON></h3>
          <p className="author-title">{__('科技爱好者 & AI玩家', '科技爱好者 & AI玩家')}</p>
          <p className="author-description">
            {__('对互联网、计算机等科技行业充满热情，擅长 AI 工具的使用与调教。', '对互联网、计算机等科技行业充满热情，擅长 AI 工具的使用与调教。')}
            {__('此插件在强大的 AI 编程助手 Cursor 和 Augment 的协助下完成，现在将这个有趣的项目分享给大家。', '此插件在强大的 AI 编程助手 Cursor 和 Augment 的协助下完成，现在将这个有趣的项目分享给大家。')}
          </p>
          <div className="author-links">
            <a href="https://frankloong.com" target="_blank" className="author-link">
              <span className="link-icon">🌐</span>
              {__('个人网站', '个人网站')}
            </a>
            <a href="mailto:<EMAIL>" className="author-link">
              <span className="link-icon">📧</span>
              {__('联系邮箱', '联系邮箱')}
            </a>
            <a href="https://github.com/Frank-Loong/Notion-to-WordPress" target="_blank" className="author-link">
              <span className="link-icon">💻</span>
              GitHub
            </a>
          </div>
        </div>
      </div>

      <div className="plugin-info">
        <h4>{__('插件信息', '插件信息')}</h4>
        <div className="info-grid">
          <div className="info-item">
            <span className="info-label">{__('版本：', '版本：')}</span>
            <span className="info-value">2.0.0-beta.2</span>
          </div>
          <div className="info-item">
            <span className="info-label">{__('许可证：', '许可证：')}</span>
            <span className="info-value">GPL v3</span>
          </div>
          <div className="info-item">
            <span className="info-label">{__('兼容性：', '兼容性：')}</span>
            <span className="info-value">WordPress 5.0+</span>
          </div>
        </div>
      </div>

      <div className="acknowledgments">
        <h4>{__('致谢与参考', '致谢与参考')}</h4>
        <p>{__('本项目的开发过程中参考了以下优秀的开源项目：', '本项目的开发过程中参考了以下优秀的开源项目：')}</p>
        <div className="reference-projects">
          <div className="reference-item">
            <a href="https://github.com/tangly1024/NotionNext" target="_blank">NotionNext</a>
            <p>{__('基于 Notion 的强大静态博客系统', '基于 Notion 的强大静态博客系统')}</p>
          </div>
          <div className="reference-item">
            <a href="https://github.com/LetTTGACO/elog" target="_blank">Elog</a>
            <p>{__('支持多平台的开源博客写作客户端', '支持多平台的开源博客写作客户端')}</p>
          </div>
          <div className="reference-item">
            <a href="https://github.com/pchang78/notion-content" target="_blank">notion-content</a>
            <p>{__('Notion 内容管理解决方案', 'Notion 内容管理解决方案')}</p>
          </div>
        </div>
        <p className="acknowledgments-footer">
          <em>{__('感谢这些项目及其维护者对开源社区的贡献！', '感谢这些项目及其维护者对开源社区的贡献！')}</em>
        </p>
      </div>

    </div>
  );
}
