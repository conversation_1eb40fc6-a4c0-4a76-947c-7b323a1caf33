/**
 * 虚拟化表格视图React组件
 * 
 * 为大数据量场景提供性能优化的表格视图
 */

import React, { useState, useMemo, useCallback, useRef } from 'react';
import type { TableViewProps, DatabaseRecord, PropertyConfig } from '../types';

// 虚拟滚动配置
interface VirtualScrollConfig {
  itemHeight: number;
  containerHeight: number;
  overscan: number; // 预渲染的额外行数
}

// 虚拟化表格Props
interface VirtualizedTableViewProps extends TableViewProps {
  virtualScrollConfig?: Partial<VirtualScrollConfig>;
}

// 默认虚拟滚动配置
const DEFAULT_VIRTUAL_CONFIG: VirtualScrollConfig = {
  itemHeight: 48, // 每行高度
  containerHeight: 400, // 容器高度
  overscan: 5, // 预渲染5行
};

/**
 * 虚拟化表格视图组件
 */
export function VirtualizedTableView({
  records,
  databaseInfo,
  options,
  sortConfig,
  onSort,
  onRecordClick,
  virtualScrollConfig = {},
  className = '',
  children,
  ...props
}: VirtualizedTableViewProps) {
  const config = useMemo(() => ({ ...DEFAULT_VIRTUAL_CONFIG, ...virtualScrollConfig }), [virtualScrollConfig]);
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // 获取可见属性（复用TableView的逻辑）
  const visibleProperties = useMemo(() => {
    if (!databaseInfo?.properties) return [];
    
    const properties: PropertyConfig[] = [];
    const showProperties = options?.showProperties || [];
    const hideProperties = options?.hideProperties || [];

    Object.entries(databaseInfo.properties).forEach(([name, config]: [string, { type?: string; [key: string]: unknown }]) => {
      const shouldShow = showProperties.length === 0 || showProperties.includes(name);
      const shouldHide = hideProperties.includes(name);

      if (shouldShow && !shouldHide) {
        properties.push({
          name,
          type: config.type || 'text',
          visible: true,
          width: getPropertyWidth(config.type || 'text'),
          format: getPropertyFormat(config.type || 'text'),
        });
      }
    });

    return properties;
  }, [databaseInfo, options]);

  // 排序记录（复用TableView的逻辑）
  const sortedRecords = useMemo(() => {
    if (!sortConfig) return records;

    return [...records].sort((a, b) => {
      const aValue = a.properties[sortConfig.property];
      const bValue = b.properties[sortConfig.property];
      
      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;
      
      let comparison = 0;
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        comparison = aValue.localeCompare(bValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue;
      } else {
        comparison = String(aValue).localeCompare(String(bValue));
      }
      
      return sortConfig.direction === 'asc' ? comparison : -comparison;
    });
  }, [records, sortConfig]);

  // 计算可见范围
  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTop / config.itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(config.containerHeight / config.itemHeight) + config.overscan,
      sortedRecords.length
    );
    
    return {
      start: Math.max(0, startIndex - config.overscan),
      end: endIndex,
    };
  }, [scrollTop, config, sortedRecords.length]);

  // 可见记录
  const visibleRecords = useMemo(() => {
    return sortedRecords.slice(visibleRange.start, visibleRange.end);
  }, [sortedRecords, visibleRange]);

  // 处理滚动
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  // 处理排序
  const handleSort = useCallback((property: string) => {
    onSort?.(property);
  }, [onSort]);

  // 总高度
  const totalHeight = sortedRecords.length * config.itemHeight;

  // 偏移量
  const offsetY = visibleRange.start * config.itemHeight;

  if (records.length === 0) {
    return (
      <div className={`notion-database-view-table virtualized ${className}`} {...props}>
        <div className="empty-table">
          <div className="empty-icon">📋</div>
          <div className="empty-message">暂无数据</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`notion-database-view-table virtualized ${className}`} {...props}>
      <div className="notion-database-table">
        {/* 表头 */}
        <div className="notion-table-header">
          {visibleProperties.map((property) => (
            <div
              key={property.name}
              className={`notion-table-header-cell ${sortConfig?.property === property.name ? 'sorted' : ''}`}
              style={{ width: property.width }}
              onClick={() => handleSort(property.name)}
              role="button"
              tabIndex={0}
            >
              <span className="header-text">{property.name}</span>
              {sortConfig?.property === property.name && (
                <span className="sort-indicator">
                  {sortConfig.direction === 'asc' ? '↑' : '↓'}
                </span>
              )}
            </div>
          ))}
        </div>

        {/* 虚拟滚动容器 */}
        <div
          ref={containerRef}
          className="notion-table-body virtualized-container"
          style={{ height: config.containerHeight, overflow: 'auto' }}
          onScroll={handleScroll}
        >
          {/* 总高度占位符 */}
          <div style={{ height: totalHeight, position: 'relative' }}>
            {/* 可见行容器 */}
            <div
              style={{
                transform: `translateY(${offsetY}px)`,
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
              }}
            >
              {visibleRecords.map((record, index) => {
                const actualIndex = visibleRange.start + index;
                return (
                  <VirtualizedTableRow
                    key={record.id}
                    record={record}
                    properties={visibleProperties}
                    height={config.itemHeight}
                    onClick={onRecordClick}
                    index={actualIndex}
                  />
                );
              })}
            </div>
          </div>
        </div>
      </div>
      {children}
    </div>
  );
}

/**
 * 虚拟化表格行组件
 */
interface VirtualizedTableRowProps {
  record: DatabaseRecord;
  properties: PropertyConfig[];
  height: number;
  onClick?: (record: DatabaseRecord) => void;
  index: number;
}

function VirtualizedTableRow({ record, properties, height, onClick, index }: VirtualizedTableRowProps) {
  const handleClick = useCallback(() => {
    onClick?.(record);
  }, [record, onClick]);

  return (
    <div
      className={`notion-table-row ${onClick ? 'notion-table-row-interactive' : ''}`}
      style={{ height, display: 'flex', alignItems: 'center' }}
      onClick={handleClick}
      data-record-id={record.id}
      data-index={index}
    >
      {properties.map((property) => (
        <VirtualizedTableCell
          key={property.name}
          record={record}
          property={property}
        />
      ))}
    </div>
  );
}

/**
 * 虚拟化表格单元格组件
 */
interface VirtualizedTableCellProps {
  record: DatabaseRecord;
  property: PropertyConfig;
}

function VirtualizedTableCell({ record, property }: VirtualizedTableCellProps) {
  const value = record.properties[property.name];
  
  // 简化的格式化函数（性能优化）
  const formatValue = useCallback((val: unknown): string => {
    if (val === null || val === undefined) return '';
    
    switch (property.type) {
      case 'title':
        return (val as { title?: Array<{ plain_text?: string }> })?.title?.[0]?.plain_text || String(val);
      case 'number':
        return typeof val === 'number' ? val.toLocaleString() : String(val);
      case 'checkbox':
        return val ? '✓' : '✗';
      case 'date':
        return (val as { start?: string })?.start ? new Date((val as { start: string }).start).toLocaleDateString() : String(val);
      default:
        return String(val);
    }
  }, [property.type]);

  const formattedValue = formatValue(value);

  return (
    <div 
      className="notion-table-cell"
      style={{ width: property.width }}
      title={formattedValue}
    >
      <span className={`cell-content cell-type-${property.type}`}>
        {formattedValue}
      </span>
    </div>
  );
}

// 辅助函数（复用TableView的逻辑）
function getPropertyWidth(type: string): string {
  switch (type) {
    case 'checkbox': return '60px';
    case 'number': return '100px';
    case 'date': return '120px';
    case 'select': return '150px';
    case 'title': return '200px';
    default: return '150px';
  }
}

function getPropertyFormat(type: string): string {
  return type;
}

export default VirtualizedTableView;
