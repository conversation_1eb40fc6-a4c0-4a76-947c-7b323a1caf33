/**
 * WordPress集成Hooks
 */

import { useCallback, useEffect, useState } from 'react';
import type { WordPressGlobals, ApiResponse } from '../types';

// 获取WordPress全局对象
function getWordPressGlobals(): WordPressGlobals | null {
  return (window as any).notionToWp || null;
}

// WordPress AJAX Hook
export function useWordPressAjax() {
  const wp = getWordPressGlobals();

  const request = useCallback(async <T = any>(
    action: string,
    data: Record<string, any> = {},
    options: {
      method?: 'GET' | 'POST';
      timeout?: number;
    } = {}
  ): Promise<ApiResponse<T>> => {
    if (!wp) {
      throw new Error('WordPress globals not available');
    }

    const { method = 'POST', timeout = 30000 } = options;

    const formData = new FormData();
    formData.append('action', action);
    formData.append('nonce', wp.nonce);
    
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, typeof value === 'object' ? JSON.stringify(value) : String(value));
      }
    });

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(wp.ajaxUrl, {
        method,
        body: method === 'POST' ? formData : undefined,
        signal: controller.signal,
        credentials: 'same-origin',
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Request timeout');
        }
        throw error;
      }
      
      throw new Error('Unknown error occurred');
    }
  }, [wp]);

  return { request };
}

// WordPress设置Hook
export function useWordPressSettings() {
  const { request } = useWordPressAjax();

  const saveSettings = useCallback(async (settings: Record<string, any>) => {
    return request('notion_to_wordpress_save_settings', settings);
  }, [request]);

  const loadSettings = useCallback(async () => {
    return request('notion_to_wordpress_load_settings');
  }, [request]);

  const testConnection = useCallback(async (apiKey: string, databaseId: string) => {
    return request('notion_to_wordpress_test_connection', {
      api_key: apiKey,
      database_id: databaseId,
    });
  }, [request]);

  return {
    saveSettings,
    loadSettings,
    testConnection,
  };
}

// 同步操作Hook
export function useSyncOperations() {
  const { request } = useWordPressAjax();

  const startSync = useCallback(async (type: 'manual' | 'full') => {
    return request('notion_to_wordpress_start_sync', { sync_type: type });
  }, [request]);

  const getSyncStatus = useCallback(async () => {
    return request('notion_to_wordpress_get_sync_status');
  }, [request]);

  const stopSync = useCallback(async () => {
    return request('notion_to_wordpress_stop_sync');
  }, [request]);

  return {
    startSync,
    getSyncStatus,
    stopSync,
  };
}

// 统计数据Hook
export function useStatsData() {
  const { request } = useWordPressAjax();

  const getStats = useCallback(async () => {
    return request('notion_to_wordpress_get_stats');
  }, [request]);

  const refreshStats = useCallback(async () => {
    return request('notion_to_wordpress_refresh_stats');
  }, [request]);

  return {
    getStats,
    refreshStats,
  };
}

// 国际化Hook
export function useI18n() {
  const wp = getWordPressGlobals();
  
  const __ = useCallback((text: string, fallback?: string): string => {
    if (!wp?.i18n) {
      return fallback || text;
    }
    return wp.i18n[text] || fallback || text;
  }, [wp]);

  const _n = useCallback((single: string, plural: string, count: number): string => {
    return count === 1 ? __(single) : __(plural);
  }, [__]);

  return { __, _n };
}

// 用户权限Hook
export function useUserCapabilities() {
  const wp = getWordPressGlobals();
  
  const can = useCallback((capability: string): boolean => {
    if (!wp?.currentUser?.capabilities) {
      return false;
    }
    return wp.currentUser.capabilities.includes(capability);
  }, [wp]);

  const hasRole = useCallback((role: string): boolean => {
    if (!wp?.currentUser?.roles) {
      return false;
    }
    return wp.currentUser.roles.includes(role);
  }, [wp]);

  return {
    can,
    hasRole,
    currentUser: wp?.currentUser,
  };
}

// 调试模式Hook
export function useDebugMode() {
  const wp = getWordPressGlobals();
  const [debugMode, setDebugMode] = useState(wp?.debug_mode || false);

  useEffect(() => {
    if (wp) {
      setDebugMode(wp.debug_mode || false);
    }
  }, [wp]);

  const log = useCallback((...args: any[]) => {
    if (debugMode) {
      console.log('[Notion-to-WordPress Debug]', ...args);
    }
  }, [debugMode]);

  const warn = useCallback((...args: any[]) => {
    if (debugMode) {
      console.warn('[Notion-to-WordPress Debug]', ...args);
    }
  }, [debugMode]);

  const error = useCallback((...args: any[]) => {
    if (debugMode) {
      console.error('[Notion-to-WordPress Debug]', ...args);
    }
  }, [debugMode]);

  return {
    debugMode,
    log,
    warn,
    error,
  };
}

// 剪贴板Hook
export function useClipboard() {
  const [copied, setCopied] = useState(false);

  const copy = useCallback(async (text: string): Promise<boolean> => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        textArea.remove();
      }
      
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      return true;
    } catch (error) {
      console.error('Failed to copy text:', error);
      return false;
    }
  }, []);

  return { copy, copied };
}

// 本地存储Hook
export function useLocalStorage<T>(key: string, defaultValue: T) {
  const [value, setValue] = useState<T>(() => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch {
      return defaultValue;
    }
  });

  const setStoredValue = useCallback((newValue: T | ((prev: T) => T)) => {
    try {
      const valueToStore = newValue instanceof Function ? newValue(value) : newValue;
      setValue(valueToStore);
      localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
    }
  }, [key, value]);

  return [value, setStoredValue] as const;
}
