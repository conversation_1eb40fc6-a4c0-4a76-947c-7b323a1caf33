/**
 * React管理界面入口文件
 *
 * 这个文件负责初始化React应用并替代传统的PHP模板系统
 *
 * @since 2.0.0
 * @version 2.0.0-beta.2
 */

// 导入React相关
import React from 'react';
import { createRoot } from 'react-dom/client';
import { App } from './react/App';

// 导入核心模块
import { ready } from '../shared/utils/dom';
import { eventBus, emit } from '../shared/core/EventBus';

// 所有功能已迁移到React组件

// 导入样式
import '../styles/admin/admin.scss';

/**
 * React管理界面应用类
 */
class ReactAdminApp {
  private initialized = false;
  private reactRoot: any = null;

  /**
   * 初始化React应用
   */
  init(): void {
    if (this.initialized) {
      return;
    }

    console.log('🚀 Notion to WordPress React Admin App initializing...');

    // 设置事件总线调试模式
    if (window.notionToWp?.debug_mode) {
      eventBus.setDebug(true);
    }

    // 初始化React应用
    this.initializeReactApp();

    // 初始化向后兼容层
    this.initializeLegacyCompatibility();

    // 绑定全局事件
    this.bindGlobalEvents();

    this.initialized = true;
    emit('react-admin:initialized');

    console.log('✅ Notion to WordPress React Admin App initialized');
  }

  /**
   * 初始化React应用
   */
  private initializeReactApp(): void {
    // 查找React挂载点
    const mountPoint = document.getElementById('notion-to-wordpress-react-root');
    
    if (!mountPoint) {
      console.error('React mount point not found');
      return;
    }

    try {
      // 创建React根节点
      this.reactRoot = createRoot(mountPoint);
      
      // 渲染React应用
      this.reactRoot.render(React.createElement(App));
      
      // React应用已经默认显示，隐藏备用PHP表单
      const phpForm = document.getElementById('notion-to-wordpress-settings-form');
      if (phpForm) {
        phpForm.style.display = 'none';
      }

      console.log('✅ React app mounted successfully');
    } catch (error) {
      console.error('Failed to mount React app:', error);
      
      // 如果React挂载失败，显示备用PHP表单
      console.error('React app failed to mount, falling back to PHP form');

      // 隐藏React挂载点
      mountPoint.style.display = 'none';

      // 显示备用PHP表单
      const phpForm = document.getElementById('notion-to-wordpress-settings-form');
      if (phpForm) {
        phpForm.style.display = 'block';
      }

      // 在React挂载点显示错误信息
      mountPoint.innerHTML = `
        <div class="notice notice-warning">
          <p><strong>正在使用传统界面</strong></p>
          <p>React应用加载失败，已切换到备用界面。</p>
          <p>错误信息：${error instanceof Error ? error.message : '未知错误'}</p>
        </div>
      `;
    }
  }

  /**
   * 初始化向后兼容层
   */
  private initializeLegacyCompatibility(): void {
    try {
      // 传统的AdminInteractions已被React应用替代
      // 这里可以添加其他兼容性代码
      console.log('✅ Legacy compatibility layer initialized (React mode)');
    } catch (error) {
      console.warn('Failed to initialize legacy compatibility:', error);
    }
  }

  /**
   * 绑定全局事件
   */
  private bindGlobalEvents(): void {
    // 监听WordPress页面卸载事件
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });

    // 监听React应用事件
    eventBus.on('react-app:error', (error) => {
      console.error('React app error:', error);
      this.handleReactError(error);
    });

    // 监听设置保存事件
    eventBus.on('settings:save', (settings) => {
      console.log('Settings saved:', settings);
    });

    // 监听同步状态变化
    eventBus.on('sync:status-change', (status) => {
      console.log('Sync status changed:', status);
    });
  }

  /**
   * 处理React错误
   */
  private handleReactError(error: any): void {
    const mountPoint = document.getElementById('notion-to-wordpress-react-root');
    if (mountPoint) {
      mountPoint.innerHTML = `
        <div class="notice notice-error">
          <p><strong>应用运行时错误</strong></p>
          <p>请刷新页面重试。如果问题持续存在，请联系管理员。</p>
          <details>
            <summary>错误详情</summary>
            <pre>${error instanceof Error ? error.stack : JSON.stringify(error, null, 2)}</pre>
          </details>
        </div>
      `;
    }
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.reactRoot) {
      try {
        this.reactRoot.unmount();
        this.reactRoot = null;
      } catch (error) {
        console.warn('Failed to unmount React app:', error);
      }
    }

    // Legacy interactions cleanup已移除，React应用自行管理
  }

  /**
   * 销毁应用
   */
  destroy(): void {
    this.cleanup();
    this.initialized = false;
    emit('react-admin:destroyed');
  }
}

/**
 * 创建全局应用实例
 */
const reactAdminApp = new ReactAdminApp();

/**
 * 导出到全局作用域
 */
declare global {
  interface Window {
    NotionWpReactAdmin: ReactAdminApp;
  }
}

window.NotionWpReactAdmin = reactAdminApp;

/**
 * DOM准备就绪后初始化
 */
console.log('🚀 Notion to WordPress React Admin App loading...');
ready(() => {
  console.log('🚀 DOM ready, initializing React Admin...');
  try {
    reactAdminApp.init();
    console.log('✅ React Admin initialized successfully');
  } catch (error) {
    console.error('❌ React Admin initialization failed:', error);
  }
});

// 导出应用实例
export default reactAdminApp;
