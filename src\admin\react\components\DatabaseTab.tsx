/**
 * 数据库视图标签页组件
 */

import { useState } from 'react';
import { useSettings, useAppActions } from '../context/AppContext';
import { useUserCapabilities } from '../hooks/useWordPress';
import { DatabaseView } from './database/DatabaseView';
import type { ViewType } from './database/types';

// 数据库设置指导组件
function DatabaseSetupGuide() {

  return (
    <div className="notion-wp-settings-section">
      <div className="notion-wp-setup-guide">
        <div className="setup-guide-header">
          <h2>🗄️ 数据库视图</h2>
          <p className="description">
            在这里您可以查看和管理Notion数据库中的记录。
          </p>
        </div>

        <div className="setup-guide-content">
          <div className="notice notice-warning">
            <h3>⚠️ 需要配置数据库连接</h3>
            <p>要使用数据库视图功能，请先完成以下配置：</p>
            <ol>
              <li>在 <strong>🔄 同步设置</strong> 标签页中配置Notion API密钥</li>
              <li>设置要查看的Notion数据库ID</li>
              <li>测试连接确保配置正确</li>
            </ol>
            <p>配置完成后，您就可以在这里查看数据库记录了。</p>
          </div>

          <div className="setup-guide-features">
            <h3>🌟 功能特性</h3>
            <div className="features-grid">
              <div className="feature-item">
                <div className="feature-icon">📊</div>
                <div className="feature-content">
                  <h4>表格视图</h4>
                  <p>以表格形式查看所有记录，支持排序和搜索</p>
                </div>
              </div>
              <div className="feature-item">
                <div className="feature-icon">🖼️</div>
                <div className="feature-content">
                  <h4>画廊视图</h4>
                  <p>以卡片形式展示记录，适合包含图片的内容</p>
                </div>
              </div>
              <div className="feature-item">
                <div className="feature-icon">📋</div>
                <div className="feature-content">
                  <h4>看板视图</h4>
                  <p>按状态分组显示，适合项目管理和任务跟踪</p>
                </div>
              </div>
              <div className="feature-item">
                <div className="feature-icon">🔍</div>
                <div className="feature-content">
                  <h4>搜索过滤</h4>
                  <p>快速搜索和过滤记录，找到您需要的内容</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// 数据库视图错误组件
function DatabaseViewError({ error }: { error: string }) {

  return (
    <div className="notion-wp-settings-section">
      <div className="notion-wp-error-state">
        <div className="error-header">
          <h2>❌ 数据库连接错误</h2>
        </div>
        <div className="error-content">
          <div className="notice notice-error">
            <p><strong>无法连接到数据库：</strong></p>
            <p>{error}</p>
          </div>
          <div className="error-actions">
            <p>请检查以下设置：</p>
            <ul>
              <li>Notion API密钥是否正确</li>
              <li>数据库ID是否有效</li>
              <li>数据库权限是否已授予</li>
              <li>网络连接是否正常</li>
            </ul>
            <p>
              如需帮助，请查看 <strong>📖 使用帮助</strong> 标签页中的详细说明。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// 权限不足组件
function DatabasePermissionDenied() {

  return (
    <div className="notion-wp-settings-section">
      <div className="notion-wp-error-state">
        <div className="error-header">
          <h2>🔒 权限不足</h2>
        </div>
        <div className="error-content">
          <div className="notice notice-error">
            <p><strong>您没有访问数据库视图的权限。</strong></p>
            <p>此功能需要管理员权限才能使用。</p>
          </div>
          <div className="error-actions">
            <p>请联系网站管理员获取相应权限，或者：</p>
            <ul>
              <li>确认您的用户角色具有管理权限</li>
              <li>检查插件权限设置</li>
              <li>联系系统管理员</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

// 主数据库标签页组件
export function DatabaseTab() {
  const settings = useSettings();
  const { showToast } = useAppActions();
  const { can } = useUserCapabilities();

  const [viewType, setViewType] = useState<ViewType>('table');
  const [error] = useState<string | null>(null);

  // 检查用户权限
  const hasPermission = can('manage_options');

  // 检查数据库配置
  const isDatabaseConfigured = settings.notion_api_key && settings.notion_database_id;

  // 处理视图类型变化
  const handleViewTypeChange = (newViewType: ViewType) => {
    setViewType(newViewType);
    showToast({
      type: 'info',
      message: `已切换到${newViewType === 'table' ? '表格' : newViewType === 'gallery' ? '画廊' : '看板'}视图`,
    });
  };

  // 处理记录点击
  const handleRecordClick = (record: { id: string; [key: string]: unknown }) => {
    console.log('记录点击:', record);
    showToast({
      type: 'info',
      message: `已选择记录: ${record.id}`,
    });
  };

  // 检查用户权限
  if (!hasPermission) {
    return <DatabasePermissionDenied />;
  }

  // 如果没有配置数据库，显示设置指导
  if (!isDatabaseConfigured) {
    return <DatabaseSetupGuide />;
  }

  // 如果有错误，显示错误状态
  if (error) {
    return <DatabaseViewError error={error} />;
  }

  return (
    <div className="notion-wp-settings-section">
      <div className="database-tab-header">
        <h2>🗄️ 数据库视图</h2>
        <p className="description">
          查看和管理Notion数据库中的记录。当前数据库: <code>{settings.notion_database_id}</code>
        </p>
      </div>

      <div className="database-tab-content">
        <DatabaseView
          databaseId={settings.notion_database_id}
          defaultViewType={viewType}
          enableSearch={true}
          enableFilter={true}
          enableSort={true}
          enablePagination={true}
          pageSize={20}
          autoRefresh={false}
          onViewTypeChange={handleViewTypeChange}
          onRecordClick={handleRecordClick}
          className="admin-database-view"
        />
      </div>
    </div>
  );
}
