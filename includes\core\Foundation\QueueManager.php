<?php
declare(strict_types=1);

namespace NTWP\Core\Foundation;

use NTWP\Infrastructure\Concurrency\TaskQueue;
use NTWP\Core\Foundation\Logger;

/**
 * 队列管理器
 *
 * 提供统一的队列管理接口，封装TaskQueue的复杂性
 * 支持静态方法调用，便于全局访问
 *
 * @deprecated 2.0.0-beta.2 请直接使用 NTWP\Infrastructure\Concurrency\TaskQueue 的静态方法
 * @see        NTWP\Infrastructure\Concurrency\TaskQueue
 *
 * @since      2.0.0-beta.1
 * @version    2.0.0-beta.2
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-<PERSON>ong/Notion-to-WordPress
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class QueueManager {

    /**
     * TaskQueue实例
     * @deprecated 2.0.0-beta.2 请直接使用 TaskQueue::getInstance()
     */
    private static ?TaskQueue $taskQueue = null;
    
    /**
     * 初始化队列管理器
     */
    public static function init(): void {
        if (self::$taskQueue === null) {
            self::$taskQueue = new TaskQueue();
            Logger::infoLog('队列管理器已初始化', 'Queue Manager');
        }
    }
    
    /**
     * 获取TaskQueue实例
     *
     * @return TaskQueue TaskQueue实例
     */
    public static function getTaskQueue(): TaskQueue {
        if (self::$taskQueue === null) {
            self::init();
        }
        return self::$taskQueue;
    }
    
    /**
     * 将任务加入队列
     *
     * @param array $data 任务数据
     * @param int $priority 优先级 (1-15, 数字越大优先级越高)
     * @return string 队列项目ID
     */
    public static function enqueue(array $data, int $priority = 5): string {
        return self::getTaskQueue()->enqueue($data, $priority);
    }
    
    /**
     * 从队列中取出任务
     *
     * @return array|null 任务数据或null（如果队列为空）
     */
    public static function dequeue(): ?array {
        return self::getTaskQueue()->dequeue();
    }
    
    /**
     * 获取队列大小
     *
     * @return int 队列中的任务数量
     */
    public static function size(): int {
        return self::getTaskQueue()->size();
    }
    
    /**
     * 清空队列
     */
    public static function clear(): void {
        self::getTaskQueue()->clear();
    }
    
    /**
     * 根据任务ID移除任务
     *
     * @param string $taskId 任务ID
     * @return int 移除的任务数量
     */
    public static function removeByTaskId(string $taskId): int {
        return self::getTaskQueue()->removeByTaskId($taskId);
    }
    
    /**
     * 清理过期的锁文件和损坏的队列文件
     */
    public static function cleanup(): void {
        self::getTaskQueue()->cleanup();
    }
    
    /**
     * 获取队列状态
     *
     * @return array 队列状态信息
     */
    public static function getQueueStatus(): array {
        try {
            $taskQueue = self::getTaskQueue();
            $size = $taskQueue->size();
            
            // 分析队列文件获取详细状态
            $status = [
                'total_tasks' => $size,
                'pending' => $size, // 简化实现，实际应该分析队列文件
                'processing' => 0,
                'completed' => 0,
                'failed' => 0,
                'queue_healthy' => true
            ];
            
            // 如果队列为空，返回默认状态
            if ($size === 0) {
                return [
                    'total_tasks' => 0,
                    'pending' => 0,
                    'processing' => 0,
                    'completed' => 0,
                    'failed' => 0,
                    'queue_healthy' => true
                ];
            }
            
            return $status;
            
        } catch (\Exception $e) {
            Logger::errorLog(
                sprintf('获取队列状态失败: %s', $e->getMessage()),
                'Queue Manager'
            );
            
            return [
                'total_tasks' => 0,
                'pending' => 0,
                'processing' => 0,
                'completed' => 0,
                'failed' => 0,
                'queue_healthy' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 检查队列是否健康
     *
     * @return bool 队列是否健康
     */
    public static function isHealthy(): bool {
        try {
            $status = self::getQueueStatus();
            return $status['queue_healthy'] ?? false;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取队列统计信息
     *
     * @return array 统计信息
     */
    public static function getStats(): array {
        $status = self::getQueueStatus();
        
        return [
            'queue_size' => $status['total_tasks'],
            'is_healthy' => $status['queue_healthy'] ?? false,
            'last_cleanup' => time(), // 简化实现
            'uptime' => time() - (time() - 3600), // 简化实现
        ];
    }
} 