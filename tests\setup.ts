/**
 * Jest测试环境设置
 *
 * 配置测试环境的全局设置和模拟
 */

// 设置测试环境的全局变量
(global as any).wp = {
  hooks: {
    addAction: jest.fn(),
    doAction: jest.fn(),
    addFilter: jest.fn(),
    applyFilters: jest.fn(),
    removeAction: jest.fn(),
    removeFilter: jest.fn()
  },
  ajax: {
    post: jest.fn(),
    send: jest.fn()
  },
  i18n: {
    __: jest.fn((text: string) => text),
    _x: jest.fn((text: string) => text),
    _n: jest.fn((single: string) => single),
    sprintf: jest.fn((text: string) => text)
  }
};

// 模拟jQuery
(global as any).$ = jest.fn(() => ({
  ready: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
  trigger: jest.fn(),
  find: jest.fn(),
  addClass: jest.fn(),
  removeClass: jest.fn(),
  toggleClass: jest.fn(),
  attr: jest.fn(),
  removeAttr: jest.fn(),
  val: jest.fn(),
  text: jest.fn(),
  html: jest.fn(),
  show: jest.fn(),
  hide: jest.fn(),
  fadeIn: jest.fn(),
  fadeOut: jest.fn(),
  slideUp: jest.fn(),
  slideDown: jest.fn(),
  animate: jest.fn(),
  css: jest.fn(),
  data: jest.fn(),
  removeData: jest.fn(),
  each: jest.fn(),
  map: jest.fn(),
  filter: jest.fn(),
  not: jest.fn(),
  is: jest.fn(),
  hasClass: jest.fn(),
  closest: jest.fn(),
  parent: jest.fn(),
  parents: jest.fn(),
  children: jest.fn(),
  siblings: jest.fn(),
  next: jest.fn(),
  prev: jest.fn(),
  first: jest.fn(),
  last: jest.fn(),
  eq: jest.fn(),
  get: jest.fn(),
  index: jest.fn(),
  length: 0,
  ajax: jest.fn(),
  post: jest.fn(),
  load: jest.fn()
}));

(global as any).jQuery = (global as any).$;

// 模拟WordPress全局变量
(global as any).ajaxurl = '/wp-admin/admin-ajax.php';
(global as any).notionToWp = {
  ajaxUrl: '/wp-admin/admin-ajax.php',
  nonce: 'test-nonce',
  pluginUrl: '/wp-content/plugins/notion-to-wordpress/',
  version: '2.0.0-beta.1'
};

// 模拟console方法以避免测试输出污染
const originalConsole = global.console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
};

// 模拟window对象 - 移除location模拟以避免jsdom冲突

// 模拟localStorage和sessionStorage
const createStorageMock = () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn()
});

// 只在不存在时才设置
if (!window.localStorage) {
  Object.defineProperty(window, 'localStorage', {
    value: createStorageMock(),
    writable: true
  });
}

if (!window.sessionStorage) {
  Object.defineProperty(window, 'sessionStorage', {
    value: createStorageMock(),
    writable: true
  });
}

// 设置测试超时
jest.setTimeout(10000);

// 在每个测试后清理模拟
afterEach(() => {
  jest.clearAllMocks();
  // 重置console模拟
  (console.log as jest.Mock).mockClear();
  (console.warn as jest.Mock).mockClear();
  (console.error as jest.Mock).mockClear();
});
