/**
 * 数据库信息管理Hook
 * 
 * 用于获取和管理数据库的元信息（属性、标题等）
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useWordPressAjax } from './useWordPress';
import { on, off } from '../../../shared/core/EventBus';
import type { DatabaseInfo } from '../types';

// Hook状态接口
export interface DatabaseInfoState {
  info: DatabaseInfo | null;
  loading: boolean;
  error: string | null;
}

// Hook配置选项
export interface UseDatabaseInfoOptions {
  autoLoad?: boolean;
  cacheTime?: number; // 缓存时间（毫秒）
}

// Hook返回值接口
export interface UseDatabaseInfoReturn {
  state: DatabaseInfoState;
  loadInfo: () => Promise<DatabaseInfo | null>;
  refreshInfo: () => Promise<DatabaseInfo | null>;
  clearInfo: () => void;
  isLoading: boolean;
  hasError: boolean;
  hasInfo: boolean;
}

// 缓存管理
const infoCache = new Map<string, { info: DatabaseInfo; timestamp: number }>();
const DEFAULT_CACHE_TIME = 5 * 60 * 1000; // 5分钟

/**
 * 数据库信息管理Hook
 */
export function useDatabaseInfo(
  databaseId: string,
  options: UseDatabaseInfoOptions = {}
): UseDatabaseInfoReturn {
  const { autoLoad = true, cacheTime = DEFAULT_CACHE_TIME } = options;

  // 状态管理
  const [state, setState] = useState<DatabaseInfoState>({
    info: null,
    loading: false,
    error: null,
  });

  const mountedRef = useRef(true);
  const { request } = useWordPressAjax();

  // 安全更新状态
  const safeSetState = useCallback((updater: Partial<DatabaseInfoState> | ((prev: DatabaseInfoState) => DatabaseInfoState)) => {
    if (mountedRef.current) {
      setState(typeof updater === 'function' ? updater : (prev) => ({ ...prev, ...updater }));
    }
  }, []);

  // 检查缓存
  const getCachedInfo = useCallback((dbId: string): DatabaseInfo | null => {
    const cached = infoCache.get(dbId);
    if (cached && Date.now() - cached.timestamp < cacheTime) {
      return cached.info;
    }
    return null;
  }, [cacheTime]);

  // 设置缓存
  const setCachedInfo = useCallback((dbId: string, info: DatabaseInfo) => {
    infoCache.set(dbId, { info, timestamp: Date.now() });
  }, []);

  // 加载数据库信息
  const loadInfo = useCallback(async (): Promise<DatabaseInfo | null> => {
    if (!databaseId) {
      safeSetState({ error: '数据库ID未提供' });
      return null;
    }

    // 检查缓存
    const cachedInfo = getCachedInfo(databaseId);
    if (cachedInfo) {
      safeSetState({ info: cachedInfo, loading: false, error: null });
      return cachedInfo;
    }

    safeSetState({ loading: true, error: null });

    try {
      const response = await request('notion_to_wordpress_get_database_info', {
        database_id: databaseId,
      });

      if (response.success && response.data) {
        const info = response.data as DatabaseInfo;
        setCachedInfo(databaseId, info);
        safeSetState({ info, loading: false, error: null });
        return info;
      } else {
        throw new Error(response.data?.message || '获取数据库信息失败');
      }
    } catch (error) {
      const errorMessage = (error as Error).message;
      safeSetState({ loading: false, error: errorMessage });
      console.error('获取数据库信息失败:', error);
      return null;
    }
  }, [databaseId, request, getCachedInfo, setCachedInfo, safeSetState]);

  // 刷新数据库信息（忽略缓存）
  const refreshInfo = useCallback(async (): Promise<DatabaseInfo | null> => {
    if (!databaseId) return null;

    // 清除缓存
    infoCache.delete(databaseId);
    return loadInfo();
  }, [databaseId, loadInfo]);

  // 清除信息
  const clearInfo = useCallback(() => {
    if (databaseId) {
      infoCache.delete(databaseId);
    }
    safeSetState({ info: null, error: null });
  }, [databaseId, safeSetState]);

  // 事件监听器
  useEffect(() => {
    if (!databaseId) return;

    // 监听数据库信息更新事件
    const handleDatabaseInfoUpdated = (event: any) => {
      const { databaseId: eventDbId, info } = event.detail || event;
      if (eventDbId === databaseId) {
        setCachedInfo(databaseId, info);
        safeSetState({ info, error: null });
      }
    };

    // 监听数据库删除事件
    const handleDatabaseDeleted = (event: any) => {
      const { databaseId: eventDbId } = event.detail || event;
      if (eventDbId === databaseId) {
        clearInfo();
      }
    };

    on('database:info:updated', handleDatabaseInfoUpdated);
    on('database:deleted', handleDatabaseDeleted);

    return () => {
      off('database:info:updated', handleDatabaseInfoUpdated);
      off('database:deleted', handleDatabaseDeleted);
    };
  }, [databaseId, setCachedInfo, safeSetState, clearInfo]);

  // 自动加载
  useEffect(() => {
    if (databaseId && autoLoad) {
      loadInfo().catch(console.error);
    }
  }, [databaseId, autoLoad, loadInfo]);

  // 组件卸载清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // 计算派生状态
  const isLoading = state.loading;
  const hasError = !!state.error;
  const hasInfo = !!state.info;

  return {
    state,
    loadInfo,
    refreshInfo,
    clearInfo,
    isLoading,
    hasError,
    hasInfo,
  };
}

/**
 * 清除所有数据库信息缓存
 */
export function clearAllDatabaseInfoCache(): void {
  infoCache.clear();
}

/**
 * 获取缓存统计信息
 */
export function getDatabaseInfoCacheStats(): {
  size: number;
  keys: string[];
} {
  return {
    size: infoCache.size,
    keys: Array.from(infoCache.keys()),
  };
}
