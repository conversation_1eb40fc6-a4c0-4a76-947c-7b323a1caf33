/**
 * React应用类型定义
 */

// WordPress全局对象类型
export interface WordPressGlobals {
  ajaxUrl: string;
  nonce: string;
  version: string;
  scriptNonce: string;
  apiEndpoints: Record<string, string>;
  currentUser: {
    id: number;
    name: string;
    email: string;
    roles: string[];
    capabilities: string[];
  };
  i18n: Record<string, string>;
  debug_mode?: boolean;
}

// 插件设置类型
export interface PluginSettings {
  notion_api_key: string;
  notion_database_id: string;
  sync_schedule: string;
  delete_on_uninstall: boolean;
  field_mapping: Record<string, string>;
  performance_level: string;
  field_template: string;
  webhook_enabled: boolean;
  webhook_verify_token: string;
  webhook_token: string;
  webhook_incremental_sync: boolean;
  webhook_check_deletions: boolean;
  cron_incremental_sync: boolean;
  cron_check_deletions: boolean;

  // 字段映射高级选项
  auto_create_categories?: boolean;
  auto_create_tags?: boolean;
  preserve_html?: boolean;
  sync_metadata?: boolean;

  // 性能配置
  api_page_size?: number;
  concurrent_requests?: number;
  request_timeout?: number;
  retry_attempts?: number;
  cache_enabled?: boolean;
  cache_duration?: number;
  batch_processing?: boolean;
  memory_limit_check?: boolean;
  smart_throttling?: boolean;
  incremental_optimization?: boolean;

  // 其他设置
  log_level?: string;
  debug_mode?: boolean;
  log_retention_days?: number;
  default_post_status?: string;
  default_post_type?: string;
  timezone?: string;
  encrypt_api_key?: boolean;
  admin_only_access?: boolean;
  anonymous_stats?: boolean;
  keep_synced_posts?: boolean;
}

// 同步状态类型
export interface SyncStatus {
  isRunning: boolean;
  type: 'manual' | 'full' | 'webhook' | 'cron';
  progress: number;
  currentStep: string;
  startTime?: Date;
  endTime?: Date;
  error?: string;
  stats?: {
    imported: number;
    published: number;
    updated: number;
    skipped: number;
  };
}

// 统计数据类型
export interface StatsData {
  importedCount: number;
  publishedCount: number;
  lastUpdate: string;
  nextRun: string;
}

// 表单验证状态
export interface ValidationState {
  isValid: boolean;
  errors: Record<string, string>;
  warnings: Record<string, string>;
}

// 标签页类型
export type TabType =
  | 'api-settings'
  | 'field-mapping'
  | 'performance-config'
  | 'performance'
  | 'other-settings'
  | 'database-view'
  | 'debug'
  | 'help'
  | 'about-author';

// 数据库相关类型定义
export interface DatabaseRecord {
  id: string;
  properties: Record<string, any>;
  created_time: string;
  last_edited_time: string;
  url?: string;
  archived?: boolean;
  icon?: {
    type: string;
    emoji?: string;
    file?: { url: string };
    external?: { url: string };
  };
  cover?: {
    type: string;
    file?: { url: string };
    external?: { url: string };
  };
}

export interface DatabaseInfo {
  id: string;
  title: Array<{
    type: string;
    text: { content: string };
  }>;
  description: Array<{
    type: string;
    text: { content: string };
  }>;
  properties: Record<string, {
    id: string;
    name: string;
    type: string;
    [key: string]: any;
  }>;
  created_time: string;
  last_edited_time: string;
  url: string;
  archived: boolean;
  icon?: {
    type: string;
    emoji?: string;
    file?: { url: string };
    external?: { url: string };
  };
  cover?: {
    type: string;
    file?: { url: string };
    external?: { url: string };
  };
}

export interface DatabaseFilter {
  property: string;
  condition: string;
  value: any;
  type?: string;
}

export interface DatabaseSort {
  property: string;
  direction: 'asc' | 'desc';
}

export interface DatabaseQuery {
  filter?: {
    and?: DatabaseFilter[];
    or?: DatabaseFilter[];
  };
  sorts?: DatabaseSort[];
  start_cursor?: string;
  page_size?: number;
}

export interface DatabaseState {
  records: DatabaseRecord[];
  info: DatabaseInfo | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  hasMore: boolean;
  nextCursor?: string;
}

// 组件Props基础类型
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// 按钮组件Props
export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  title?: string;
}

// 输入组件Props
export interface InputProps extends BaseComponentProps {
  type?: 'text' | 'password' | 'email' | 'number';
  value?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  validation?: 'api-key' | 'database-id' | 'email';
  onChange?: (value: string) => void;
  onBlur?: () => void;
}

// 选择器组件Props
export interface SelectProps extends BaseComponentProps {
  value?: string;
  options: Array<{ value: string; label: string }>;
  disabled?: boolean;
  required?: boolean;
  onChange?: (value: string) => void;
}

// 复选框组件Props
export interface CheckboxProps extends BaseComponentProps {
  checked?: boolean;
  disabled?: boolean;
  label?: string;
  description?: string;
  onChange?: (checked: boolean) => void;
}

// 模态框Props
export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  title?: string;
  onClose: () => void;
  size?: 'small' | 'medium' | 'large';
}

// Toast消息类型
export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  actions?: Array<{
    label: string;
    onClick: () => void;
  }>;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}

// 事件类型
export interface AppEvent {
  type: string;
  payload?: any;
  timestamp: Date;
}

// 应用状态类型
export interface AppState {
  settings: PluginSettings;
  syncStatus: SyncStatus;
  stats: StatsData;
  validation: ValidationState;
  activeTab: TabType;
  toasts: ToastMessage[];
  isLoading: boolean;
  error?: string;
}

// 应用Action类型
export type AppAction =
  | { type: 'SET_SETTINGS'; payload: Partial<PluginSettings> }
  | { type: 'SET_SYNC_STATUS'; payload: Partial<SyncStatus> }
  | { type: 'SET_STATS'; payload: Partial<StatsData> }
  | { type: 'SET_VALIDATION'; payload: Partial<ValidationState> }
  | { type: 'SET_ACTIVE_TAB'; payload: TabType }
  | { type: 'ADD_TOAST'; payload: ToastMessage }
  | { type: 'REMOVE_TOAST'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | undefined }
  | { type: 'RESET_STATE' };

// Context类型
export interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  actions: {
    updateSettings: (settings: Partial<PluginSettings>) => void;
    updateSyncStatus: (status: Partial<SyncStatus>) => void;
    updateStats: (stats: Partial<StatsData>) => void;
    setActiveTab: (tab: TabType) => void;
    showToast: (toast: Omit<ToastMessage, 'id'>) => void;
    hideToast: (id: string) => void;
    setLoading: (loading: boolean) => void;
    setError: (error: string | undefined) => void;
  };
}

// 重新导出数据库视图相关类型（避免与本文件中的类型冲突）
export type {
  ViewType,
  RenderOptions,
  PropertyConfig,
  DatabaseViewProps,
  DatabaseToolbarProps,
  ViewComponentProps,
  TableViewProps,
  GalleryViewProps,
  BoardViewProps,
  DatabaseAction
} from '../components/database/types';

// 重新导出数据库组件
export { DatabaseView, DatabaseToolbar } from '../components/database';
