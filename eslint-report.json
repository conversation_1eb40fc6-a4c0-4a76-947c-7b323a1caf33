[{"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\App.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\AboutAuthorTab.tsx", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 11, "column": 32, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 11, "endColumn": 35, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [182, 185], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [182, 185], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 关于作者标签页组件\n */\n\nimport { useI18n } from '../hooks/useWordPress';\n\nexport function AboutAuthorTab() {\n  const { __ } = useI18n();\n\n  // 获取插件目录URL\n  const pluginUrl = (window as any).notionToWp?.pluginUrl || '';\n  const avatarUrl = pluginUrl + 'assets/avatar.svg';\n\n  return (\n    <div className=\"notion-wp-settings-section\">\n      <h2>{__('关于作者', '关于作者')}</h2>\n\n      <div className=\"author-info\">\n        <div className=\"author-avatar\">\n          <img\n            src={avatarUrl}\n            alt=\"Frank-Loong\"\n            onError={(e) => {\n              (e.target as HTMLImageElement).style.display = 'none';\n            }}\n          />\n        </div>\n        <div className=\"author-details\">\n          <h3>Frank-<PERSON>ong</h3>\n          <p className=\"author-title\">{__('科技爱好者 & AI玩家', '科技爱好者 & AI玩家')}</p>\n          <p className=\"author-description\">\n            {__('对互联网、计算机等科技行业充满热情，擅长 AI 工具的使用与调教。', '对互联网、计算机等科技行业充满热情，擅长 AI 工具的使用与调教。')}\n            {__('此插件在强大的 AI 编程助手 Cursor 和 Augment 的协助下完成，现在将这个有趣的项目分享给大家。', '此插件在强大的 AI 编程助手 Cursor 和 Augment 的协助下完成，现在将这个有趣的项目分享给大家。')}\n          </p>\n          <div className=\"author-links\">\n            <a href=\"https://frankloong.com\" target=\"_blank\" className=\"author-link\">\n              <span className=\"link-icon\">🌐</span>\n              {__('个人网站', '个人网站')}\n            </a>\n            <a href=\"mailto:<EMAIL>\" className=\"author-link\">\n              <span className=\"link-icon\">📧</span>\n              {__('联系邮箱', '联系邮箱')}\n            </a>\n            <a href=\"https://github.com/Frank-Loong/Notion-to-WordPress\" target=\"_blank\" className=\"author-link\">\n              <span className=\"link-icon\">💻</span>\n              GitHub\n            </a>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"plugin-info\">\n        <h4>{__('插件信息', '插件信息')}</h4>\n        <div className=\"info-grid\">\n          <div className=\"info-item\">\n            <span className=\"info-label\">{__('版本：', '版本：')}</span>\n            <span className=\"info-value\">2.0.0-beta.2</span>\n          </div>\n          <div className=\"info-item\">\n            <span className=\"info-label\">{__('许可证：', '许可证：')}</span>\n            <span className=\"info-value\">GPL v3</span>\n          </div>\n          <div className=\"info-item\">\n            <span className=\"info-label\">{__('兼容性：', '兼容性：')}</span>\n            <span className=\"info-value\">WordPress 5.0+</span>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"acknowledgments\">\n        <h4>{__('致谢与参考', '致谢与参考')}</h4>\n        <p>{__('本项目的开发过程中参考了以下优秀的开源项目：', '本项目的开发过程中参考了以下优秀的开源项目：')}</p>\n        <div className=\"reference-projects\">\n          <div className=\"reference-item\">\n            <a href=\"https://github.com/tangly1024/NotionNext\" target=\"_blank\">NotionNext</a>\n            <p>{__('基于 Notion 的强大静态博客系统', '基于 Notion 的强大静态博客系统')}</p>\n          </div>\n          <div className=\"reference-item\">\n            <a href=\"https://github.com/LetTTGACO/elog\" target=\"_blank\">Elog</a>\n            <p>{__('支持多平台的开源博客写作客户端', '支持多平台的开源博客写作客户端')}</p>\n          </div>\n          <div className=\"reference-item\">\n            <a href=\"https://github.com/pchang78/notion-content\" target=\"_blank\">notion-content</a>\n            <p>{__('Notion 内容管理解决方案', 'Notion 内容管理解决方案')}</p>\n          </div>\n        </div>\n        <p className=\"acknowledgments-footer\">\n          <em>{__('感谢这些项目及其维护者对开源社区的贡献！', '感谢这些项目及其维护者对开源社区的贡献！')}</em>\n        </p>\n      </div>\n\n    </div>\n  );\n}\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\ApiSettingsTab.tsx", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 25, "column": 52, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 25, "endColumn": 55, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [917, 920], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [917, 920], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * API设置标签页组件\n */\n\nimport { useState } from 'react';\nimport { useSettings, useStats, useSyncStatus, useAppActions } from '../context/AppContext';\nimport { useWordPressSettings, useSyncOperations, useI18n } from '../hooks/useWordPress';\nimport { Input, PasswordInput, Select, Checkbox, FormRow } from './Input';\nimport { Button, SyncButton } from './Button';\nimport { WebhookSettings } from './WebhookSettings';\nimport { QuickConfig } from './QuickConfig';\n\nexport function ApiSettingsTab() {\n  const settings = useSettings();\n  const stats = useStats();\n  const syncStatus = useSyncStatus();\n  const { updateSettings, showToast } = useAppActions();\n  const { testConnection } = useWordPressSettings();\n  const { startSync } = useSyncOperations();\n  const { __ } = useI18n();\n\n  const [isTestingConnection, setIsTestingConnection] = useState(false);\n\n  // 处理设置更新\n  const handleSettingChange = (key: string, value: any) => {\n    updateSettings({ [key]: value });\n  };\n\n  // 测试连接\n  const handleTestConnection = async () => {\n    if (!settings.notion_api_key || !settings.notion_database_id) {\n      showToast({\n        type: 'warning',\n        message: '请先填写API密钥和数据库ID',\n      });\n      return;\n    }\n\n    setIsTestingConnection(true);\n    try {\n      const response = await testConnection(settings.notion_api_key, settings.notion_database_id);\n      \n      if (response.success) {\n        showToast({\n          type: 'success',\n          message: '连接测试成功！',\n        });\n      } else {\n        showToast({\n          type: 'error',\n          message: response.message || '连接测试失败',\n        });\n      }\n    } catch (error) {\n      showToast({\n        type: 'error',\n        message: '连接测试失败：' + (error instanceof Error ? error.message : '未知错误'),\n      });\n    } finally {\n      setIsTestingConnection(false);\n    }\n  };\n\n  // 开始同步\n  const handleSync = async (type: 'manual' | 'full') => {\n    try {\n      const response = await startSync(type);\n      \n      if (response.success) {\n        showToast({\n          type: 'success',\n          message: `${type === 'manual' ? '智能' : '完全'}同步已开始`,\n        });\n      } else {\n        showToast({\n          type: 'error',\n          message: response.message || '同步启动失败',\n        });\n      }\n    } catch (error) {\n      showToast({\n        type: 'error',\n        message: '同步启动失败：' + (error instanceof Error ? error.message : '未知错误'),\n      });\n    }\n  };\n\n  // 同步计划选项\n  const scheduleOptions = [\n    { value: 'manual', label: __('手动同步', '手动同步') },\n    { value: 'twicedaily', label: __('每天两次', '每天两次') },\n    { value: 'daily', label: __('每天一次', '每天一次') },\n    { value: 'weekly', label: __('每周一次', '每周一次') },\n    { value: 'biweekly', label: __('每两周一次', '每两周一次') },\n    { value: 'monthly', label: __('每月一次', '每月一次') },\n  ];\n\n  return (\n    <div className=\"notion-wp-settings-section\">\n      <h2>{__('Notion API 设置', 'Notion API 设置')}</h2>\n      \n      {/* 统计数据网格 */}\n      <div className=\"notion-stats-grid\">\n        <div className=\"stat-card\">\n          <h3 className=\"stat-imported-count\">{stats.importedCount}</h3>\n          <span>{__('已导入页面', '已导入页面')}</span>\n        </div>\n        <div className=\"stat-card\">\n          <h3 className=\"stat-published-count\">{stats.publishedCount}</h3>\n          <span>{__('已发布页面', '已发布页面')}</span>\n        </div>\n        <div className=\"stat-card\">\n          <h3 className=\"stat-last-update\">{stats.lastUpdate}</h3>\n          <span>{__('最后同步', '最后同步')}</span>\n        </div>\n        <div className=\"stat-card\">\n          <h3 className=\"stat-next-run\">{stats.nextRun}</h3>\n          <span>{__('下次同步', '下次同步')}</span>\n        </div>\n      </div>\n      \n      <p className=\"description\">\n        {__('连接到您的Notion数据库所需的设置。', '连接到您的Notion数据库所需的设置。')}\n        <a href=\"https://developers.notion.com/docs/getting-started\" target=\"_blank\">\n          {__('了解如何获取API密钥', '了解如何获取API密钥')}\n        </a>\n      </p>\n      \n      <table className=\"form-table\">\n        <tbody>\n          <FormRow\n            label={__('API密钥', 'API密钥')}\n            required\n            description={__('在Notion的\"我的集成\"页面创建并获取API密钥。', '在Notion的\"我的集成\"页面创建并获取API密钥。')}\n          >\n            <PasswordInput\n              value={settings.notion_api_key}\n              placeholder={__('输入您的Notion API密钥', '输入您的Notion API密钥')}\n              validation=\"api-key\"\n              onChange={(value) => handleSettingChange('notion_api_key', value)}\n            />\n          </FormRow>\n\n          <FormRow\n            label={__('数据库ID', '数据库ID')}\n            required\n            description={__('可以从Notion数据库URL中找到，格式如：https://www.notion.so/xxx/数据库ID?v=xxx', '可以从Notion数据库URL中找到，格式如：https://www.notion.so/xxx/数据库ID?v=xxx')}\n          >\n            <Input\n              value={settings.notion_database_id}\n              placeholder={__('输入您的Notion数据库ID', '输入您的Notion数据库ID')}\n              validation=\"database-id\"\n              onChange={(value) => handleSettingChange('notion_database_id', value)}\n            />\n          </FormRow>\n\n          <FormRow\n            label={__('自动同步频率', '自动同步频率')}\n            description={__('选择 \"手动同步\" 以禁用定时任务。', '选择 \"手动同步\" 以禁用定时任务。')}\n          >\n            <Select\n              value={settings.sync_schedule}\n              options={scheduleOptions}\n              onChange={(value) => handleSettingChange('sync_schedule', value)}\n            />\n          </FormRow>\n\n          <FormRow label={__('定时同步选项', '定时同步选项')}>\n            <div className=\"checkbox-group\">\n              <Checkbox\n                checked={settings.cron_incremental_sync}\n                label={__('启用增量同步', '启用增量同步')}\n                description={__('仅同步有变化的页面，提高同步速度', '仅同步有变化的页面，提高同步速度')}\n                onChange={(checked) => handleSettingChange('cron_incremental_sync', checked)}\n              />\n              \n              <Checkbox\n                checked={settings.cron_check_deletions}\n                label={__('检查删除的页面', '检查删除的页面')}\n                description={__('自动删除在Notion中已删除但WordPress中仍存在的文章', '自动删除在Notion中已删除但WordPress中仍存在的文章')}\n                onChange={(checked) => handleSettingChange('cron_check_deletions', checked)}\n              />\n            </div>\n          </FormRow>\n        </tbody>\n      </table>\n\n      {/* 测试连接按钮 */}\n      <div className=\"notion-wp-button-row\">\n        <Button\n          variant=\"secondary\"\n          loading={isTestingConnection}\n          onClick={handleTestConnection}\n        >\n          <span className=\"dashicons dashicons-admin-network\" style={{ marginRight: '4px' }} />\n          {__('测试连接', '测试连接')}\n        </Button>\n      </div>\n\n      {/* Webhook设置 */}\n      <WebhookSettings />\n\n      {/* 同步操作 */}\n      <div className=\"notion-wp-sync-actions\">\n        <h3>{__('同步操作', '同步操作')}</h3>\n        <div className=\"sync-buttons\">\n          <SyncButton\n            syncType=\"manual\"\n            isRunning={syncStatus.isRunning && syncStatus.type === 'manual'}\n            onSync={handleSync}\n          >\n            {__('智能同步', '智能同步')}\n          </SyncButton>\n\n          <SyncButton\n            syncType=\"full\"\n            isRunning={syncStatus.isRunning && syncStatus.type === 'full'}\n            onSync={handleSync}\n          >\n            {__('完全同步', '完全同步')}\n          </SyncButton>\n        </div>\n\n        {/* 同步进度 */}\n        {syncStatus.isRunning && (\n          <div className=\"sync-progress\" id=\"sync-progress\">\n            <div className=\"progress-bar\">\n              <div\n                className=\"progress-fill\"\n                style={{ width: `${syncStatus.progress}%` }}\n              />\n            </div>\n            <div className=\"progress-text\">\n              <span className=\"current-step\">{syncStatus.currentStep}</span>\n              <span className=\"progress-percentage\">{syncStatus.progress}%</span>\n            </div>\n          </div>\n        )}\n\n        <div className=\"sync-info\">\n          <p>\n            <strong>{__('智能同步', '智能同步')}</strong>: {__('只同步有变化的页面，速度更快', '只同步有变化的页面，速度更快')}\n          </p>\n          <p>\n            <strong>{__('完全同步', '完全同步')}</strong>: {__('同步所有页面，确保数据一致性', '同步所有页面，确保数据一致性')}\n          </p>\n        </div>\n      </div>\n\n      {/* 快速配置 */}\n      <QuickConfig />\n    </div>\n  );\n}\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\Button.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\DatabaseTab.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'___' is assigned a value but never used.", "line": 13, "column": 15, "nodeType": null, "messageId": "unusedVar", "endLine": 13, "endColumn": 18}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'___' is assigned a value but never used.", "line": 78, "column": 15, "nodeType": null, "messageId": "unusedVar", "endLine": 78, "endColumn": 18}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'___' is assigned a value but never used.", "line": 111, "column": 15, "nodeType": null, "messageId": "unusedVar", "endLine": 111, "endColumn": 18}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'___' is assigned a value but never used.", "line": 142, "column": 15, "nodeType": null, "messageId": "unusedVar", "endLine": 142, "endColumn": 18}, {"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'_setError' is assigned a value but never used.", "line": 146, "column": 17, "nodeType": null, "messageId": "unusedVar", "endLine": 146, "endColumn": 26}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 164, "column": 38, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 164, "endColumn": 41, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [4762, 4765], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [4762, 4765], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 数据库视图标签页组件\n */\n\nimport { useState } from 'react';\nimport { useSettings, useAppActions } from '../context/AppContext';\nimport { useI18n, useUserCapabilities } from '../hooks/useWordPress';\nimport { DatabaseView } from './database/DatabaseView';\nimport type { ViewType } from './database/types';\n\n// 数据库设置指导组件\nfunction DatabaseSetupGuide() {\n  const { __: ___ } = useI18n();\n\n  return (\n    <div className=\"notion-wp-settings-section\">\n      <div className=\"notion-wp-setup-guide\">\n        <div className=\"setup-guide-header\">\n          <h2>🗄️ 数据库视图</h2>\n          <p className=\"description\">\n            在这里您可以查看和管理Notion数据库中的记录。\n          </p>\n        </div>\n\n        <div className=\"setup-guide-content\">\n          <div className=\"notice notice-warning\">\n            <h3>⚠️ 需要配置数据库连接</h3>\n            <p>要使用数据库视图功能，请先完成以下配置：</p>\n            <ol>\n              <li>在 <strong>🔄 同步设置</strong> 标签页中配置Notion API密钥</li>\n              <li>设置要查看的Notion数据库ID</li>\n              <li>测试连接确保配置正确</li>\n            </ol>\n            <p>配置完成后，您就可以在这里查看数据库记录了。</p>\n          </div>\n\n          <div className=\"setup-guide-features\">\n            <h3>🌟 功能特性</h3>\n            <div className=\"features-grid\">\n              <div className=\"feature-item\">\n                <div className=\"feature-icon\">📊</div>\n                <div className=\"feature-content\">\n                  <h4>表格视图</h4>\n                  <p>以表格形式查看所有记录，支持排序和搜索</p>\n                </div>\n              </div>\n              <div className=\"feature-item\">\n                <div className=\"feature-icon\">🖼️</div>\n                <div className=\"feature-content\">\n                  <h4>画廊视图</h4>\n                  <p>以卡片形式展示记录，适合包含图片的内容</p>\n                </div>\n              </div>\n              <div className=\"feature-item\">\n                <div className=\"feature-icon\">📋</div>\n                <div className=\"feature-content\">\n                  <h4>看板视图</h4>\n                  <p>按状态分组显示，适合项目管理和任务跟踪</p>\n                </div>\n              </div>\n              <div className=\"feature-item\">\n                <div className=\"feature-icon\">🔍</div>\n                <div className=\"feature-content\">\n                  <h4>搜索过滤</h4>\n                  <p>快速搜索和过滤记录，找到您需要的内容</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// 数据库视图错误组件\nfunction DatabaseViewError({ error }: { error: string }) {\n  const { __: ___ } = useI18n();\n\n  return (\n    <div className=\"notion-wp-settings-section\">\n      <div className=\"notion-wp-error-state\">\n        <div className=\"error-header\">\n          <h2>❌ 数据库连接错误</h2>\n        </div>\n        <div className=\"error-content\">\n          <div className=\"notice notice-error\">\n            <p><strong>无法连接到数据库：</strong></p>\n            <p>{error}</p>\n          </div>\n          <div className=\"error-actions\">\n            <p>请检查以下设置：</p>\n            <ul>\n              <li>Notion API密钥是否正确</li>\n              <li>数据库ID是否有效</li>\n              <li>数据库权限是否已授予</li>\n              <li>网络连接是否正常</li>\n            </ul>\n            <p>\n              如需帮助，请查看 <strong>📖 使用帮助</strong> 标签页中的详细说明。\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// 权限不足组件\nfunction DatabasePermissionDenied() {\n  const { __: ___ } = useI18n();\n\n  return (\n    <div className=\"notion-wp-settings-section\">\n      <div className=\"notion-wp-error-state\">\n        <div className=\"error-header\">\n          <h2>🔒 权限不足</h2>\n        </div>\n        <div className=\"error-content\">\n          <div className=\"notice notice-error\">\n            <p><strong>您没有访问数据库视图的权限。</strong></p>\n            <p>此功能需要管理员权限才能使用。</p>\n          </div>\n          <div className=\"error-actions\">\n            <p>请联系网站管理员获取相应权限，或者：</p>\n            <ul>\n              <li>确认您的用户角色具有管理权限</li>\n              <li>检查插件权限设置</li>\n              <li>联系系统管理员</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// 主数据库标签页组件\nexport function DatabaseTab() {\n  const settings = useSettings();\n  const { showToast } = useAppActions();\n  const { __: ___ } = useI18n();\n  const { can } = useUserCapabilities();\n\n  const [viewType, setViewType] = useState<ViewType>('table');\n  const [error, _setError] = useState<string | null>(null);\n\n  // 检查用户权限\n  const hasPermission = can('manage_options');\n\n  // 检查数据库配置\n  const isDatabaseConfigured = settings.notion_api_key && settings.notion_database_id;\n\n  // 处理视图类型变化\n  const handleViewTypeChange = (newViewType: ViewType) => {\n    setViewType(newViewType);\n    showToast({\n      type: 'info',\n      message: `已切换到${newViewType === 'table' ? '表格' : newViewType === 'gallery' ? '画廊' : '看板'}视图`,\n    });\n  };\n\n  // 处理记录点击\n  const handleRecordClick = (record: any) => {\n    console.log('记录点击:', record);\n    showToast({\n      type: 'info',\n      message: `已选择记录: ${record.id}`,\n    });\n  };\n\n  // 检查用户权限\n  if (!hasPermission) {\n    return <DatabasePermissionDenied />;\n  }\n\n  // 如果没有配置数据库，显示设置指导\n  if (!isDatabaseConfigured) {\n    return <DatabaseSetupGuide />;\n  }\n\n  // 如果有错误，显示错误状态\n  if (error) {\n    return <DatabaseViewError error={error} />;\n  }\n\n  return (\n    <div className=\"notion-wp-settings-section\">\n      <div className=\"database-tab-header\">\n        <h2>🗄️ 数据库视图</h2>\n        <p className=\"description\">\n          查看和管理Notion数据库中的记录。当前数据库: <code>{settings.notion_database_id}</code>\n        </p>\n      </div>\n\n      <div className=\"database-tab-content\">\n        <DatabaseView\n          databaseId={settings.notion_database_id}\n          defaultViewType={viewType}\n          enableSearch={true}\n          enableFilter={true}\n          enableSort={true}\n          enablePagination={true}\n          pageSize={20}\n          autoRefresh={false}\n          onViewTypeChange={handleViewTypeChange}\n          onRecordClick={handleRecordClick}\n          className=\"admin-database-view\"\n        />\n      </div>\n    </div>\n  );\n}\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\DatabaseViewHelp.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'___' is assigned a value but never used.", "line": 9, "column": 15, "nodeType": null, "messageId": "unusedVar", "endLine": 9, "endColumn": 18}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 数据库视图帮助文档组件\n */\n\n// React is used in JSX\nimport { useI18n } from '../hooks/useWordPress';\n\nexport function DatabaseViewHelp() {\n  const { __: ___ } = useI18n();\n\n  return (\n    <div className=\"notion-wp-help-section\">\n      <div className=\"help-header\">\n        <h2>🗄️ 数据库视图使用指南</h2>\n        <p className=\"description\">\n          了解如何使用数据库视图功能查看和管理Notion数据库中的记录。\n        </p>\n      </div>\n\n      <div className=\"help-content\">\n        {/* 快速开始 */}\n        <div className=\"help-section\">\n          <h3>🚀 快速开始</h3>\n          <ol>\n            <li>\n              <strong>配置连接</strong>\n              <p>在 <strong>🔄 同步设置</strong> 标签页中配置Notion API密钥和数据库ID。</p>\n            </li>\n            <li>\n              <strong>测试连接</strong>\n              <p>点击\"测试连接\"按钮确保配置正确。</p>\n            </li>\n            <li>\n              <strong>访问数据库视图</strong>\n              <p>切换到 <strong>🗄️ 数据库视图</strong> 标签页开始使用。</p>\n            </li>\n          </ol>\n        </div>\n\n        {/* 视图类型 */}\n        <div className=\"help-section\">\n          <h3>👁️ 视图类型</h3>\n          \n          <div className=\"view-type-item\">\n            <h4>📊 表格视图</h4>\n            <p>以表格形式显示所有记录，适合查看详细数据。</p>\n            <ul>\n              <li>支持列排序</li>\n              <li>支持搜索过滤</li>\n              <li>支持分页加载</li>\n              <li>适合数据分析</li>\n            </ul>\n          </div>\n\n          <div className=\"view-type-item\">\n            <h4>🖼️ 画廊视图</h4>\n            <p>以卡片形式展示记录，适合包含图片的内容。</p>\n            <ul>\n              <li>支持图片懒加载</li>\n              <li>响应式网格布局</li>\n              <li>卡片悬停效果</li>\n              <li>适合内容展示</li>\n            </ul>\n          </div>\n\n          <div className=\"view-type-item\">\n            <h4>📋 看板视图</h4>\n            <p>按状态分组显示，适合项目管理和任务跟踪。</p>\n            <ul>\n              <li>自动状态分组</li>\n              <li>拖拽式交互（即将推出）</li>\n              <li>状态统计</li>\n              <li>适合项目管理</li>\n            </ul>\n          </div>\n        </div>\n\n        {/* 功能特性 */}\n        <div className=\"help-section\">\n          <h3>⚡ 功能特性</h3>\n          \n          <div className=\"feature-grid\">\n            <div className=\"feature-item\">\n              <h4>🔍 搜索功能</h4>\n              <p>在工具栏中输入关键词快速搜索记录。支持标题、内容等字段的模糊搜索。</p>\n            </div>\n\n            <div className=\"feature-item\">\n              <h4>🔄 实时刷新</h4>\n              <p>点击刷新按钮获取最新数据。数据会自动缓存以提升性能。</p>\n            </div>\n\n            <div className=\"feature-item\">\n              <h4>📱 响应式设计</h4>\n              <p>完美适配桌面、平板和手机设备，随时随地查看数据。</p>\n            </div>\n\n            <div className=\"feature-item\">\n              <h4>⚡ 性能优化</h4>\n              <p>支持分页加载、虚拟滚动等技术，确保大数据量下的流畅体验。</p>\n            </div>\n          </div>\n        </div>\n\n        {/* 常见问题 */}\n        <div className=\"help-section\">\n          <h3>❓ 常见问题</h3>\n          \n          <div className=\"faq-item\">\n            <h4>Q: 为什么看不到数据库视图标签页？</h4>\n            <p>A: 请确保您具有管理员权限。数据库视图功能需要 <code>manage_options</code> 权限。</p>\n          </div>\n\n          <div className=\"faq-item\">\n            <h4>Q: 为什么显示\"需要配置数据库连接\"？</h4>\n            <p>A: 请先在同步设置中配置Notion API密钥和数据库ID，并测试连接成功。</p>\n          </div>\n\n          <div className=\"faq-item\">\n            <h4>Q: 数据加载很慢怎么办？</h4>\n            <p>A: 可以在性能配置中调整页面大小和并发请求数。大数据库建议使用较小的页面大小。</p>\n          </div>\n\n          <div className=\"faq-item\">\n            <h4>Q: 如何查看特定类型的记录？</h4>\n            <p>A: 使用搜索功能输入关键词，或者在看板视图中查看按状态分组的记录。</p>\n          </div>\n\n          <div className=\"faq-item\">\n            <h4>Q: 支持编辑记录吗？</h4>\n            <p>A: 当前版本主要用于查看记录。编辑功能将在后续版本中推出。</p>\n          </div>\n        </div>\n\n        {/* 技术说明 */}\n        <div className=\"help-section\">\n          <h3>🔧 技术说明</h3>\n          \n          <div className=\"tech-note\">\n            <h4>数据同步</h4>\n            <p>数据库视图显示的是Notion数据库的实时数据，通过Notion API获取。数据会在本地缓存以提升性能。</p>\n          </div>\n\n          <div className=\"tech-note\">\n            <h4>权限要求</h4>\n            <p>使用数据库视图功能需要WordPress的 <code>manage_options</code> 权限，通常只有管理员具有此权限。</p>\n          </div>\n\n          <div className=\"tech-note\">\n            <h4>性能优化</h4>\n            <p>系统采用了多种性能优化技术：</p>\n            <ul>\n              <li>数据分页加载</li>\n              <li>图片懒加载</li>\n              <li>虚拟滚动（大数据量）</li>\n              <li>智能缓存机制</li>\n            </ul>\n          </div>\n        </div>\n\n        {/* 支持信息 */}\n        <div className=\"help-section\">\n          <h3>💬 获取支持</h3>\n          <p>如果您在使用过程中遇到问题，可以：</p>\n          <ul>\n            <li>查看 <strong>🐞 调试工具</strong> 标签页中的错误日志</li>\n            <li>检查 <strong>📊 性能监控</strong> 中的系统状态</li>\n            <li>联系插件开发者获取技术支持</li>\n            <li>查看插件官方文档</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\DebugTab.tsx", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 13, "column": 48, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 13, "endColumn": 51, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [306, 309], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [306, 309], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 调试工具标签页组件\n */\n\nimport { useState } from 'react';\nimport { useWordPressAjax, useI18n } from '../hooks/useWordPress';\nimport { Button } from './Button';\n\nexport function DebugTab() {\n  const { request } = useWordPressAjax();\n  const { __ } = useI18n();\n\n  const [systemInfo, setSystemInfo] = useState<any>(null);\n  const [logs, setLogs] = useState<string>('');\n  const [isLoadingInfo, setIsLoadingInfo] = useState(false);\n  const [isLoadingLogs, setIsLoadingLogs] = useState(false);\n\n  // 获取系统信息\n  const handleGetSystemInfo = async () => {\n    setIsLoadingInfo(true);\n    try {\n      const response = await request('notion_to_wordpress_get_system_info');\n      \n      if (response.success) {\n        setSystemInfo(response.data);\n      }\n    } catch (error) {\n      console.error('Failed to get system info:', error);\n    } finally {\n      setIsLoadingInfo(false);\n    }\n  };\n\n  // 获取日志\n  const handleGetLogs = async () => {\n    setIsLoadingLogs(true);\n    try {\n      const response = await request('notion_to_wordpress_get_logs');\n      \n      if (response.success) {\n        setLogs(response.data.logs || '');\n      }\n    } catch (error) {\n      console.error('Failed to get logs:', error);\n    } finally {\n      setIsLoadingLogs(false);\n    }\n  };\n\n  // 清理日志\n  const handleClearLogs = async () => {\n    if (!confirm(__('确定要清理所有日志吗？', '确定要清理所有日志吗？'))) {\n      return;\n    }\n\n    try {\n      const response = await request('notion_to_wordpress_clear_logs');\n      \n      if (response.success) {\n        setLogs('');\n        alert(__('日志已清理', '日志已清理'));\n      }\n    } catch (error) {\n      console.error('Failed to clear logs:', error);\n    }\n  };\n\n  // 下载日志\n  const handleDownloadLogs = () => {\n    if (!logs) {\n      alert(__('没有日志可下载', '没有日志可下载'));\n      return;\n    }\n\n    const blob = new Blob([logs], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `notion-to-wordpress-logs-${new Date().toISOString().split('T')[0]}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  // 复制系统信息\n  const handleCopySystemInfo = () => {\n    if (!systemInfo) {\n      alert(__('请先获取系统信息', '请先获取系统信息'));\n      return;\n    }\n\n    const infoText = JSON.stringify(systemInfo, null, 2);\n    navigator.clipboard.writeText(infoText).then(() => {\n      alert(__('系统信息已复制到剪贴板', '系统信息已复制到剪贴板'));\n    });\n  };\n\n  return (\n    <div className=\"notion-wp-settings-section\">\n      <h2>{__('🐞 调试工具', '🐞 调试工具')}</h2>\n      <p className=\"description\">\n        {__('用于诊断问题和获取技术支持信息的调试工具。', '用于诊断问题和获取技术支持信息的调试工具。')}\n      </p>\n\n      {/* 系统信息 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('💻 系统信息', '💻 系统信息')}</h3>\n        <p className=\"description\">\n          {__('获取服务器和插件的详细信息，用于问题诊断。', '获取服务器和插件的详细信息，用于问题诊断。')}\n        </p>\n        \n        <div className=\"debug-actions\">\n          <Button\n            variant=\"secondary\"\n            loading={isLoadingInfo}\n            onClick={handleGetSystemInfo}\n          >\n            <span className=\"dashicons dashicons-info\" style={{ marginRight: '4px' }} />\n            {__('获取系统信息', '获取系统信息')}\n          </Button>\n          \n          {systemInfo && (\n            <Button\n              variant=\"secondary\"\n              onClick={handleCopySystemInfo}\n            >\n              <span className=\"dashicons dashicons-clipboard\" style={{ marginRight: '4px' }} />\n              {__('复制信息', '复制信息')}\n            </Button>\n          )}\n        </div>\n\n        {systemInfo && (\n          <div className=\"system-info-display\">\n            <h4>{__('系统信息详情', '系统信息详情')}</h4>\n            <div className=\"info-grid\">\n              <div className=\"info-section\">\n                <h5>{__('WordPress信息', 'WordPress信息')}</h5>\n                <ul>\n                  <li><strong>{__('版本', '版本')}:</strong> {systemInfo.wordpress?.version}</li>\n                  <li><strong>{__('语言', '语言')}:</strong> {systemInfo.wordpress?.language}</li>\n                  <li><strong>{__('主题', '主题')}:</strong> {systemInfo.wordpress?.theme}</li>\n                  <li><strong>{__('调试模式', '调试模式')}:</strong> {systemInfo.wordpress?.debug ? __('开启', '开启') : __('关闭', '关闭')}</li>\n                </ul>\n              </div>\n              \n              <div className=\"info-section\">\n                <h5>{__('服务器信息', '服务器信息')}</h5>\n                <ul>\n                  <li><strong>PHP {__('版本', '版本')}:</strong> {systemInfo.server?.php_version}</li>\n                  <li><strong>{__('内存限制', '内存限制')}:</strong> {systemInfo.server?.memory_limit}</li>\n                  <li><strong>{__('最大执行时间', '最大执行时间')}:</strong> {systemInfo.server?.max_execution_time}s</li>\n                  <li><strong>{__('操作系统', '操作系统')}:</strong> {systemInfo.server?.os}</li>\n                </ul>\n              </div>\n              \n              <div className=\"info-section\">\n                <h5>{__('插件信息', '插件信息')}</h5>\n                <ul>\n                  <li><strong>{__('版本', '版本')}:</strong> {systemInfo.plugin?.version}</li>\n                  <li><strong>{__('数据库版本', '数据库版本')}:</strong> {systemInfo.plugin?.db_version}</li>\n                  <li><strong>{__('最后同步', '最后同步')}:</strong> {systemInfo.plugin?.last_sync}</li>\n                  <li><strong>{__('同步状态', '同步状态')}:</strong> {systemInfo.plugin?.sync_status}</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 日志查看器 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('📋 日志查看器', '📋 日志查看器')}</h3>\n        <p className=\"description\">\n          {__('查看插件运行日志，帮助诊断同步问题。', '查看插件运行日志，帮助诊断同步问题。')}\n        </p>\n        \n        <div className=\"debug-actions\">\n          <Button\n            variant=\"secondary\"\n            loading={isLoadingLogs}\n            onClick={handleGetLogs}\n          >\n            <span className=\"dashicons dashicons-media-text\" style={{ marginRight: '4px' }} />\n            {__('获取日志', '获取日志')}\n          </Button>\n          \n          {logs && (\n            <>\n              <Button\n                variant=\"secondary\"\n                onClick={handleDownloadLogs}\n              >\n                <span className=\"dashicons dashicons-download\" style={{ marginRight: '4px' }} />\n                {__('下载日志', '下载日志')}\n              </Button>\n              \n              <Button\n                variant=\"danger\"\n                onClick={handleClearLogs}\n              >\n                <span className=\"dashicons dashicons-trash\" style={{ marginRight: '4px' }} />\n                {__('清理日志', '清理日志')}\n              </Button>\n            </>\n          )}\n        </div>\n\n        {logs && (\n          <div className=\"log-viewer\">\n            <h4>{__('最新日志', '最新日志')}</h4>\n            <textarea\n              className=\"log-content\"\n              value={logs}\n              readOnly\n              rows={20}\n              style={{\n                width: '100%',\n                fontFamily: 'monospace',\n                fontSize: '12px',\n                backgroundColor: '#f1f1f1',\n                border: '1px solid #ddd',\n                padding: '10px',\n              }}\n            />\n          </div>\n        )}\n      </div>\n\n      {/* 连接测试 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('🔗 连接测试', '🔗 连接测试')}</h3>\n        <p className=\"description\">\n          {__('测试与Notion API的连接状态和响应时间。', '测试与Notion API的连接状态和响应时间。')}\n        </p>\n        \n        <div className=\"connection-tests\">\n          <div className=\"test-item\">\n            <h4>{__('API连接测试', 'API连接测试')}</h4>\n            <p>{__('测试基本的API连接', '测试基本的API连接')}</p>\n            <Button variant=\"secondary\">\n              {__('开始测试', '开始测试')}\n            </Button>\n          </div>\n          \n          <div className=\"test-item\">\n            <h4>{__('数据库访问测试', '数据库访问测试')}</h4>\n            <p>{__('测试对指定数据库的访问权限', '测试对指定数据库的访问权限')}</p>\n            <Button variant=\"secondary\">\n              {__('开始测试', '开始测试')}\n            </Button>\n          </div>\n          \n          <div className=\"test-item\">\n            <h4>{__('Webhook测试', 'Webhook测试')}</h4>\n            <p>{__('测试Webhook端点的可访问性', '测试Webhook端点的可访问性')}</p>\n            <Button variant=\"secondary\">\n              {__('开始测试', '开始测试')}\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* 故障排除 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('🔧 故障排除', '🔧 故障排除')}</h3>\n        \n        <div className=\"troubleshooting-guide\">\n          <div className=\"trouble-item\">\n            <h4>{__('常见问题', '常见问题')}</h4>\n            <ul>\n              <li>{__('同步失败：检查API密钥和数据库ID是否正确', '同步失败：检查API密钥和数据库ID是否正确')}</li>\n              <li>{__('内容不完整：检查字段映射配置', '内容不完整：检查字段映射配置')}</li>\n              <li>{__('同步缓慢：调整性能配置参数', '同步缓慢：调整性能配置参数')}</li>\n              <li>{__('Webhook不工作：检查服务器防火墙设置', 'Webhook不工作：检查服务器防火墙设置')}</li>\n            </ul>\n          </div>\n          \n          <div className=\"trouble-item\">\n            <h4>{__('获取支持', '获取支持')}</h4>\n            <p>{__('如果问题仍然存在，请：', '如果问题仍然存在，请：')}</p>\n            <ol>\n              <li>{__('复制系统信息', '复制系统信息')}</li>\n              <li>{__('下载最新日志', '下载最新日志')}</li>\n              <li>{__('访问插件支持页面', '访问插件支持页面')}</li>\n            </ol>\n            <Button variant=\"primary\">\n              <span className=\"dashicons dashicons-external\" style={{ marginRight: '4px' }} />\n              {__('访问支持页面', '访问支持页面')}\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\FieldMappingTab.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\HelpTab.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\Input.tsx", "messages": [{"ruleId": "no-case-declarations", "severity": 1, "message": "Unexpected lexical declaration in case block.", "line": 30, "column": 9, "nodeType": "VariableDeclaration", "messageId": "unexpected", "endLine": 30, "endColumn": 75}, {"ruleId": "no-case-declarations", "severity": 1, "message": "Unexpected lexical declaration in case block.", "line": 39, "column": 9, "nodeType": "VariableDeclaration", "messageId": "unexpected", "endLine": 39, "endColumn": 81}, {"ruleId": "no-case-declarations", "severity": 1, "message": "Unexpected lexical declaration in case block.", "line": 48, "column": 9, "nodeType": "VariableDeclaration", "messageId": "unexpected", "endLine": 48, "endColumn": 76}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 输入组件\n */\n\nimport { useState, useCallback } from 'react';\nimport type { InputProps, SelectProps, CheckboxProps } from '../types';\nimport { IconButton } from './Button';\n\n// 基础输入组件\nexport function Input({\n  type = 'text',\n  value = '',\n  placeholder,\n  disabled = false,\n  required = false,\n  validation,\n  onChange,\n  onBlur,\n  className = '',\n  ...props\n}: InputProps) {\n  const [isValid, setIsValid] = useState(true);\n  const [errorMessage, setErrorMessage] = useState('');\n\n  const validateInput = useCallback((inputValue: string) => {\n    if (!validation) return true;\n\n    switch (validation) {\n      case 'api-key':\n        const isValidApiKey = /^secret_[a-zA-Z0-9]{43}$/.test(inputValue);\n        if (!isValidApiKey && inputValue.length > 0) {\n          setErrorMessage('API密钥格式不正确，应以\"secret_\"开头');\n          setIsValid(false);\n          return false;\n        }\n        break;\n\n      case 'database-id':\n        const isValidDbId = /^[a-f0-9]{32}$/.test(inputValue.replace(/-/g, ''));\n        if (!isValidDbId && inputValue.length > 0) {\n          setErrorMessage('数据库ID格式不正确');\n          setIsValid(false);\n          return false;\n        }\n        break;\n\n      case 'email':\n        const isValidEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(inputValue);\n        if (!isValidEmail && inputValue.length > 0) {\n          setErrorMessage('邮箱格式不正确');\n          setIsValid(false);\n          return false;\n        }\n        break;\n    }\n\n    setIsValid(true);\n    setErrorMessage('');\n    return true;\n  }, [validation]);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const newValue = e.target.value;\n    validateInput(newValue);\n    onChange?.(newValue);\n  };\n\n  const handleBlur = () => {\n    validateInput(value);\n    onBlur?.();\n  };\n\n  const inputClasses = [\n    'regular-text',\n    'notion-wp-validated-input',\n    !isValid && 'invalid',\n    className,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div className=\"input-with-validation\">\n      <input\n        type={type}\n        value={value}\n        placeholder={placeholder}\n        disabled={disabled}\n        required={required}\n        className={inputClasses}\n        onChange={handleChange}\n        onBlur={handleBlur}\n        data-validation={validation}\n        {...props}\n      />\n      {!isValid && errorMessage && (\n        <div className=\"validation-feedback error\">\n          {errorMessage}\n        </div>\n      )}\n    </div>\n  );\n}\n\n// 密码输入组件\nexport function PasswordInput({\n  showToggle = true,\n  ...props\n}: InputProps & { showToggle?: boolean }) {\n  const [showPassword, setShowPassword] = useState(false);\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  return (\n    <div className=\"input-with-button\">\n      <Input\n        {...props}\n        type={showPassword ? 'text' : 'password'}\n        className={`password-input ${props.className || ''}`}\n      />\n      {showToggle && (\n        <IconButton\n          variant=\"secondary\"\n          size=\"medium\"\n          icon={showPassword ? 'hidden' : 'visibility'}\n          title={showPassword ? '隐藏密码' : '显示密码'}\n          onClick={togglePasswordVisibility}\n          className=\"show-hide-password\"\n        />\n      )}\n    </div>\n  );\n}\n\n// 选择器组件\nexport function Select({\n  value = '',\n  options,\n  disabled = false,\n  required = false,\n  onChange,\n  className = '',\n  ...props\n}: SelectProps) {\n  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    onChange?.(e.target.value);\n  };\n\n  return (\n    <select\n      value={value}\n      disabled={disabled}\n      required={required}\n      className={`regular-text ${className}`}\n      onChange={handleChange}\n      {...props}\n    >\n      {options.map(option => (\n        <option key={option.value} value={option.value}>\n          {option.label}\n        </option>\n      ))}\n    </select>\n  );\n}\n\n// 复选框组件\nexport function Checkbox({\n  checked = false,\n  disabled = false,\n  label,\n  description,\n  onChange,\n  className = '',\n  children,\n  ...props\n}: CheckboxProps) {\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onChange?.(e.target.checked);\n  };\n\n  return (\n    <div className={`checkbox-wrapper ${className}`}>\n      <label className=\"checkbox-with-label\">\n        <input\n          type=\"checkbox\"\n          checked={checked}\n          disabled={disabled}\n          onChange={handleChange}\n          {...props}\n        />\n        {label && <span className=\"checkbox-label\">{label}</span>}\n        {children}\n      </label>\n      {description && (\n        <p className=\"description\">{description}</p>\n      )}\n    </div>\n  );\n}\n\n// 字段集组件\nexport function Fieldset({\n  legend,\n  children,\n  className = '',\n}: {\n  legend?: string;\n  children: React.ReactNode;\n  className?: string;\n}) {\n  return (\n    <fieldset className={className}>\n      {legend && <legend>{legend}</legend>}\n      {children}\n    </fieldset>\n  );\n}\n\n// 表单行组件\nexport function FormRow({\n  label,\n  required = false,\n  description,\n  children,\n  className = '',\n}: {\n  label?: string;\n  required?: boolean;\n  description?: string;\n  children: React.ReactNode;\n  className?: string;\n}) {\n  return (\n    <tr className={className}>\n      {label && (\n        <th scope=\"row\">\n          <label>\n            {label}\n            {required && <span className=\"required\">*</span>}\n          </label>\n        </th>\n      )}\n      <td>\n        {children}\n        {description && (\n          <p className=\"description\">{description}</p>\n        )}\n      </td>\n    </tr>\n  );\n}\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\LoadingOverlay.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\OtherSettingsTab.tsx", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 17, "column": 52, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 17, "endColumn": 55, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [515, 518], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [515, 518], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 其他设置标签页组件\n */\n\nimport { useSettings, useAppActions } from '../context/AppContext';\nimport { useWordPressAjax, useI18n } from '../hooks/useWordPress';\nimport { Input, Select, Checkbox, FormRow } from './Input';\nimport { Button } from './Button';\n\nexport function OtherSettingsTab() {\n  const settings = useSettings();\n  const { updateSettings, showToast } = useAppActions();\n  const { request } = useWordPressAjax();\n  const { __ } = useI18n();\n\n  // 处理设置更新\n  const handleSettingChange = (key: string, value: any) => {\n    updateSettings({ [key]: value });\n  };\n\n  // 清理缓存\n  const handleClearCache = async () => {\n    try {\n      const response = await request('notion_to_wordpress_clear_cache');\n      \n      if (response.success) {\n        showToast({\n          type: 'success',\n          message: '缓存已清理',\n        });\n      } else {\n        showToast({\n          type: 'error',\n          message: response.message || '清理缓存失败',\n        });\n      }\n    } catch (error) {\n      showToast({\n        type: 'error',\n        message: '清理缓存失败：' + (error instanceof Error ? error.message : '未知错误'),\n      });\n    }\n  };\n\n  // 重置所有设置\n  const handleResetAllSettings = async () => {\n    if (!confirm(__('确定要重置所有设置吗？此操作不可撤销。', '确定要重置所有设置吗？此操作不可撤销。'))) {\n      return;\n    }\n\n    try {\n      const response = await request('notion_to_wordpress_reset_settings');\n      \n      if (response.success) {\n        showToast({\n          type: 'success',\n          message: '所有设置已重置',\n        });\n        // 重新加载页面以应用默认设置\n        window.location.reload();\n      } else {\n        showToast({\n          type: 'error',\n          message: response.message || '重置设置失败',\n        });\n      }\n    } catch (error) {\n      showToast({\n        type: 'error',\n        message: '重置设置失败：' + (error instanceof Error ? error.message : '未知错误'),\n      });\n    }\n  };\n\n  // 日志级别选项\n  const logLevelOptions = [\n    { value: 'none', label: __('关闭日志', '关闭日志') },\n    { value: 'error', label: __('仅错误', '仅错误') },\n    { value: 'warning', label: __('警告及以上', '警告及以上') },\n    { value: 'info', label: __('信息及以上', '信息及以上') },\n    { value: 'debug', label: __('调试模式', '调试模式') },\n  ];\n\n  // 时区选项\n  const timezoneOptions = [\n    { value: 'UTC', label: 'UTC' },\n    { value: 'Asia/Shanghai', label: '北京时间 (UTC+8)' },\n    { value: 'America/New_York', label: '纽约时间 (UTC-5/-4)' },\n    { value: 'Europe/London', label: '伦敦时间 (UTC+0/+1)' },\n    { value: 'Asia/Tokyo', label: '东京时间 (UTC+9)' },\n  ];\n\n  return (\n    <div className=\"notion-wp-settings-section\">\n      <h2>{__('⚙️ 其他设置', '⚙️ 其他设置')}</h2>\n      <p className=\"description\">\n        {__('配置插件的其他功能选项和系统设置。', '配置插件的其他功能选项和系统设置。')}\n      </p>\n\n      {/* 日志和调试 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('📝 日志和调试', '📝 日志和调试')}</h3>\n        \n        <table className=\"form-table\">\n          <tbody>\n            <FormRow\n              label={__('日志级别', '日志级别')}\n              description={__('选择要记录的日志级别。调试模式会记录详细信息，但可能影响性能。', '选择要记录的日志级别。调试模式会记录详细信息，但可能影响性能。')}\n            >\n              <Select\n                value={settings.log_level || 'info'}\n                options={logLevelOptions}\n                onChange={(value) => handleSettingChange('log_level', value)}\n              />\n            </FormRow>\n\n            <FormRow\n              label={__('启用调试模式', '启用调试模式')}\n              description={__('启用详细的调试信息输出，用于问题诊断。', '启用详细的调试信息输出，用于问题诊断。')}\n            >\n              <Checkbox\n                checked={settings.debug_mode || false}\n                onChange={(checked) => handleSettingChange('debug_mode', checked)}\n              />\n            </FormRow>\n\n            <FormRow\n              label={__('保留日志天数', '保留日志天数')}\n              description={__('自动清理多少天前的日志文件。设为0表示不自动清理。', '自动清理多少天前的日志文件。设为0表示不自动清理。')}\n            >\n              <Input\n                type=\"number\"\n                value={String(settings.log_retention_days || 30)}\n                onChange={(value) => handleSettingChange('log_retention_days', parseInt(value) || 30)}\n              />\n            </FormRow>\n          </tbody>\n        </table>\n      </div>\n\n      {/* 内容处理 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('📄 内容处理', '📄 内容处理')}</h3>\n        \n        <table className=\"form-table\">\n          <tbody>\n            <FormRow\n              label={__('默认文章状态', '默认文章状态')}\n              description={__('当Notion页面没有指定状态时使用的默认WordPress文章状态。', '当Notion页面没有指定状态时使用的默认WordPress文章状态。')}\n            >\n              <Select\n                value={settings.default_post_status || 'draft'}\n                options={[\n                  { value: 'draft', label: __('草稿', '草稿') },\n                  { value: 'publish', label: __('发布', '发布') },\n                  { value: 'private', label: __('私密', '私密') },\n                  { value: 'pending', label: __('待审核', '待审核') },\n                ]}\n                onChange={(value) => handleSettingChange('default_post_status', value)}\n              />\n            </FormRow>\n\n            <FormRow\n              label={__('默认文章类型', '默认文章类型')}\n              description={__('当Notion页面没有指定类型时使用的默认WordPress文章类型。', '当Notion页面没有指定类型时使用的默认WordPress文章类型。')}\n            >\n              <Select\n                value={settings.default_post_type || 'post'}\n                options={[\n                  { value: 'post', label: __('文章', '文章') },\n                  { value: 'page', label: __('页面', '页面') },\n                ]}\n                onChange={(value) => handleSettingChange('default_post_type', value)}\n              />\n            </FormRow>\n\n            <FormRow\n              label={__('时区设置', '时区设置')}\n              description={__('用于处理日期和时间的时区。建议与WordPress设置保持一致。', '用于处理日期和时间的时区。建议与WordPress设置保持一致。')}\n            >\n              <Select\n                value={settings.timezone || 'UTC'}\n                options={timezoneOptions}\n                onChange={(value) => handleSettingChange('timezone', value)}\n              />\n            </FormRow>\n          </tbody>\n        </table>\n      </div>\n\n      {/* 安全和隐私 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('🔒 安全和隐私', '🔒 安全和隐私')}</h3>\n        \n        <table className=\"form-table\">\n          <tbody>\n            <FormRow\n              label={__('API密钥加密存储', 'API密钥加密存储')}\n              description={__('使用WordPress内置加密功能保护API密钥。', '使用WordPress内置加密功能保护API密钥。')}\n            >\n              <Checkbox\n                checked={settings.encrypt_api_key !== false}\n                onChange={(checked) => handleSettingChange('encrypt_api_key', checked)}\n              />\n            </FormRow>\n\n            <FormRow\n              label={__('限制管理员访问', '限制管理员访问')}\n              description={__('只允许超级管理员访问插件设置。', '只允许超级管理员访问插件设置。')}\n            >\n              <Checkbox\n                checked={settings.admin_only_access || false}\n                onChange={(checked) => handleSettingChange('admin_only_access', checked)}\n              />\n            </FormRow>\n\n            <FormRow\n              label={__('匿名统计', '匿名统计')}\n              description={__('允许发送匿名使用统计以帮助改进插件。不会收集敏感信息。', '允许发送匿名使用统计以帮助改进插件。不会收集敏感信息。')}\n            >\n              <Checkbox\n                checked={settings.anonymous_stats !== false}\n                onChange={(checked) => handleSettingChange('anonymous_stats', checked)}\n              />\n            </FormRow>\n          </tbody>\n        </table>\n      </div>\n\n      {/* 卸载选项 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('🗑️ 卸载选项', '🗑️ 卸载选项')}</h3>\n        \n        <table className=\"form-table\">\n          <tbody>\n            <FormRow\n              label={__('卸载时删除数据', '卸载时删除数据')}\n              description={__('卸载插件时删除所有设置和数据。注意：此操作不可撤销！', '卸载插件时删除所有设置和数据。注意：此操作不可撤销！')}\n            >\n              <Checkbox\n                checked={settings.delete_on_uninstall || false}\n                onChange={(checked) => handleSettingChange('delete_on_uninstall', checked)}\n              />\n            </FormRow>\n\n            <FormRow\n              label={__('保留同步的文章', '保留同步的文章')}\n              description={__('卸载时保留已同步的WordPress文章，只删除插件数据。', '卸载时保留已同步的WordPress文章，只删除插件数据。')}\n            >\n              <Checkbox\n                checked={settings.keep_synced_posts !== false}\n                onChange={(checked) => handleSettingChange('keep_synced_posts', checked)}\n              />\n            </FormRow>\n          </tbody>\n        </table>\n      </div>\n\n      {/* 系统操作 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('🔧 系统操作', '🔧 系统操作')}</h3>\n        \n        <div className=\"system-actions\">\n          <div className=\"action-group\">\n            <h4>{__('缓存管理', '缓存管理')}</h4>\n            <p>{__('清理所有缓存数据，强制重新获取最新内容。', '清理所有缓存数据，强制重新获取最新内容。')}</p>\n            <Button\n              variant=\"secondary\"\n              onClick={handleClearCache}\n            >\n              <span className=\"dashicons dashicons-trash\" style={{ marginRight: '4px' }} />\n              {__('清理缓存', '清理缓存')}\n            </Button>\n          </div>\n\n          <div className=\"action-group danger-zone\">\n            <h4>{__('⚠️ 危险操作', '⚠️ 危险操作')}</h4>\n            <p>{__('以下操作将重置所有设置，请谨慎操作！', '以下操作将重置所有设置，请谨慎操作！')}</p>\n            <Button\n              variant=\"danger\"\n              onClick={handleResetAllSettings}\n            >\n              <span className=\"dashicons dashicons-warning\" style={{ marginRight: '4px' }} />\n              {__('重置所有设置', '重置所有设置')}\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\PerformanceConfigTab.tsx", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 20, "column": 52, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 20, "endColumn": 55, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [622, 625], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [622, 625], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 性能配置标签页组件\n */\n\nimport { useState } from 'react';\nimport { useSettings, useAppActions } from '../context/AppContext';\nimport { useWordPressAjax, useI18n } from '../hooks/useWordPress';\nimport { Select, Checkbox, FormRow } from './Input';\nimport { Button } from './Button';\n\nexport function PerformanceConfigTab() {\n  const settings = useSettings();\n  const { updateSettings, showToast } = useAppActions();\n  const { request } = useWordPressAjax();\n  const { __ } = useI18n();\n\n  const [isTestingPerformance, setIsTestingPerformance] = useState(false);\n\n  // 处理设置更新\n  const handleSettingChange = (key: string, value: any) => {\n    updateSettings({ [key]: value });\n  };\n\n  // 性能测试\n  const handlePerformanceTest = async () => {\n    setIsTestingPerformance(true);\n    try {\n      const response = await request('notion_to_wordpress_performance_test');\n      \n      if (response.success) {\n        showToast({\n          type: 'success',\n          title: '性能测试完成',\n          message: `平均响应时间: ${response.data.avg_response_time}ms`,\n        });\n      } else {\n        showToast({\n          type: 'error',\n          message: response.message || '性能测试失败',\n        });\n      }\n    } catch (error) {\n      showToast({\n        type: 'error',\n        message: '性能测试失败：' + (error instanceof Error ? error.message : '未知错误'),\n      });\n    } finally {\n      setIsTestingPerformance(false);\n    }\n  };\n\n  // 重置为推荐设置\n  const handleResetToRecommended = () => {\n    const recommendedSettings = {\n      api_page_size: 50,\n      concurrent_requests: 3,\n      request_timeout: 30,\n      retry_attempts: 3,\n      cache_enabled: true,\n      cache_duration: 3600,\n      batch_processing: true,\n      memory_limit_check: true,\n    };\n\n    Object.entries(recommendedSettings).forEach(([key, value]) => {\n      updateSettings({ [key]: value });\n    });\n\n    showToast({\n      type: 'success',\n      message: '已重置为推荐的性能设置',\n    });\n  };\n\n  // 配置选项\n  const pageSizeOptions = [\n    { value: '10', label: '10 (保守)' },\n    { value: '25', label: '25 (平衡)' },\n    { value: '50', label: '50 (推荐)' },\n    { value: '100', label: '100 (激进)' },\n  ];\n\n  const concurrentOptions = [\n    { value: '1', label: '1 (最安全)' },\n    { value: '2', label: '2 (保守)' },\n    { value: '3', label: '3 (推荐)' },\n    { value: '5', label: '5 (激进)' },\n    { value: '10', label: '10 (高性能)' },\n  ];\n\n  const timeoutOptions = [\n    { value: '15', label: '15秒 (快速)' },\n    { value: '30', label: '30秒 (推荐)' },\n    { value: '60', label: '60秒 (保守)' },\n    { value: '120', label: '120秒 (极保守)' },\n  ];\n\n  const retryOptions = [\n    { value: '0', label: '0 (不重试)' },\n    { value: '1', label: '1次' },\n    { value: '3', label: '3次 (推荐)' },\n    { value: '5', label: '5次' },\n  ];\n\n  const cacheDurationOptions = [\n    { value: '300', label: '5分钟' },\n    { value: '1800', label: '30分钟' },\n    { value: '3600', label: '1小时 (推荐)' },\n    { value: '7200', label: '2小时' },\n    { value: '86400', label: '24小时' },\n  ];\n\n  return (\n    <div className=\"notion-wp-settings-section\">\n      <h2>{__('⚡ 性能配置', '⚡ 性能配置')}</h2>\n      <p className=\"description\">\n        {__('调整同步性能参数以适应您的服务器配置。建议先使用默认设置，然后根据实际情况进行调整。', '调整同步性能参数以适应您的服务器配置。建议先使用默认设置，然后根据实际情况进行调整。')}\n      </p>\n\n      {/* API请求配置 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('🔗 API请求配置', '🔗 API请求配置')}</h3>\n        \n        <table className=\"form-table\">\n          <tbody>\n            <FormRow\n              label={__('API分页大小', 'API分页大小')}\n              description={__('每次从Notion API获取的页面数量。较大的值可以减少请求次数，但可能增加内存使用。', '每次从Notion API获取的页面数量。较大的值可以减少请求次数，但可能增加内存使用。')}\n            >\n              <Select\n                value={String(settings.api_page_size || 50)}\n                options={pageSizeOptions}\n                onChange={(value) => handleSettingChange('api_page_size', parseInt(value))}\n              />\n            </FormRow>\n\n            <FormRow\n              label={__('并发请求数', '并发请求数')}\n              description={__('同时进行的API请求数量。增加并发可以提高速度，但可能触发API限制。', '同时进行的API请求数量。增加并发可以提高速度，但可能触发API限制。')}\n            >\n              <Select\n                value={String(settings.concurrent_requests || 3)}\n                options={concurrentOptions}\n                onChange={(value) => handleSettingChange('concurrent_requests', parseInt(value))}\n              />\n            </FormRow>\n\n            <FormRow\n              label={__('请求超时时间', '请求超时时间')}\n              description={__('单个API请求的最大等待时间。网络较慢时建议增加此值。', '单个API请求的最大等待时间。网络较慢时建议增加此值。')}\n            >\n              <Select\n                value={String(settings.request_timeout || 30)}\n                options={timeoutOptions}\n                onChange={(value) => handleSettingChange('request_timeout', parseInt(value))}\n              />\n            </FormRow>\n\n            <FormRow\n              label={__('重试次数', '重试次数')}\n              description={__('请求失败时的重试次数。适当的重试可以提高成功率。', '请求失败时的重试次数。适当的重试可以提高成功率。')}\n            >\n              <Select\n                value={String(settings.retry_attempts || 3)}\n                options={retryOptions}\n                onChange={(value) => handleSettingChange('retry_attempts', parseInt(value))}\n              />\n            </FormRow>\n          </tbody>\n        </table>\n      </div>\n\n      {/* 缓存配置 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('💾 缓存配置', '💾 缓存配置')}</h3>\n        \n        <table className=\"form-table\">\n          <tbody>\n            <FormRow\n              label={__('启用缓存', '启用缓存')}\n              description={__('缓存API响应以减少重复请求，提高同步速度。', '缓存API响应以减少重复请求，提高同步速度。')}\n            >\n              <Checkbox\n                checked={settings.cache_enabled !== false}\n                onChange={(checked) => handleSettingChange('cache_enabled', checked)}\n              />\n            </FormRow>\n\n            {settings.cache_enabled !== false && (\n              <FormRow\n                label={__('缓存持续时间', '缓存持续时间')}\n                description={__('缓存数据的有效期。较长的缓存时间可以提高性能，但可能导致数据不够实时。', '缓存数据的有效期。较长的缓存时间可以提高性能，但可能导致数据不够实时。')}\n              >\n                <Select\n                  value={String(settings.cache_duration || 3600)}\n                  options={cacheDurationOptions}\n                  onChange={(value) => handleSettingChange('cache_duration', parseInt(value))}\n                />\n              </FormRow>\n            )}\n          </tbody>\n        </table>\n      </div>\n\n      {/* 处理优化 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('🚀 处理优化', '🚀 处理优化')}</h3>\n        \n        <table className=\"form-table\">\n          <tbody>\n            <FormRow\n              label={__('批量处理', '批量处理')}\n              description={__('将多个操作合并为批量处理，提高数据库操作效率。', '将多个操作合并为批量处理，提高数据库操作效率。')}\n            >\n              <Checkbox\n                checked={settings.batch_processing !== false}\n                onChange={(checked) => handleSettingChange('batch_processing', checked)}\n              />\n            </FormRow>\n\n            <FormRow\n              label={__('内存限制检查', '内存限制检查')}\n              description={__('监控内存使用情况，防止内存溢出。建议在共享主机上启用。', '监控内存使用情况，防止内存溢出。建议在共享主机上启用。')}\n            >\n              <Checkbox\n                checked={settings.memory_limit_check !== false}\n                onChange={(checked) => handleSettingChange('memory_limit_check', checked)}\n              />\n            </FormRow>\n\n            <FormRow\n              label={__('智能限流', '智能限流')}\n              description={__('根据API响应时间自动调整请求频率，避免触发限制。', '根据API响应时间自动调整请求频率，避免触发限制。')}\n            >\n              <Checkbox\n                checked={settings.smart_throttling !== false}\n                onChange={(checked) => handleSettingChange('smart_throttling', checked)}\n              />\n            </FormRow>\n\n            <FormRow\n              label={__('增量同步优化', '增量同步优化')}\n              description={__('优化增量同步算法，只处理真正有变化的内容。', '优化增量同步算法，只处理真正有变化的内容。')}\n            >\n              <Checkbox\n                checked={settings.incremental_optimization !== false}\n                onChange={(checked) => handleSettingChange('incremental_optimization', checked)}\n              />\n            </FormRow>\n          </tbody>\n        </table>\n      </div>\n\n      {/* 操作按钮 */}\n      <div className=\"notion-wp-button-row\">\n        <Button\n          variant=\"primary\"\n          loading={isTestingPerformance}\n          onClick={handlePerformanceTest}\n        >\n          <span className=\"dashicons dashicons-performance\" style={{ marginRight: '4px' }} />\n          {__('性能测试', '性能测试')}\n        </Button>\n        \n        <Button\n          variant=\"secondary\"\n          onClick={handleResetToRecommended}\n        >\n          {__('重置为推荐设置', '重置为推荐设置')}\n        </Button>\n      </div>\n\n      {/* 性能建议 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('💡 性能优化建议', '💡 性能优化建议')}</h3>\n        <div className=\"performance-tips\">\n          <div className=\"tip-card\">\n            <h4>{__('🏃‍♂️ 提高速度', '🏃‍♂️ 提高速度')}</h4>\n            <ul>\n              <li>{__('增加API分页大小和并发请求数', '增加API分页大小和并发请求数')}</li>\n              <li>{__('启用缓存和批量处理', '启用缓存和批量处理')}</li>\n              <li>{__('使用增量同步而非完全同步', '使用增量同步而非完全同步')}</li>\n            </ul>\n          </div>\n          \n          <div className=\"tip-card\">\n            <h4>{__('🛡️ 提高稳定性', '🛡️ 提高稳定性')}</h4>\n            <ul>\n              <li>{__('减少并发请求数', '减少并发请求数')}</li>\n              <li>{__('增加请求超时时间和重试次数', '增加请求超时时间和重试次数')}</li>\n              <li>{__('启用内存限制检查和智能限流', '启用内存限制检查和智能限流')}</li>\n            </ul>\n          </div>\n          \n          <div className=\"tip-card\">\n            <h4>{__('⚖️ 平衡配置', '⚖️ 平衡配置')}</h4>\n            <ul>\n              <li>{__('使用推荐的默认设置', '使用推荐的默认设置')}</li>\n              <li>{__('根据服务器性能逐步调整', '根据服务器性能逐步调整')}</li>\n              <li>{__('定期进行性能测试', '定期进行性能测试')}</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\PerformanceTab.tsx", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 13, "column": 58, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 13, "endColumn": 61, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [333, 336], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [333, 336], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "react-hooks/exhaustive-deps", "severity": 1, "message": "React Hook useEffect has a missing dependency: 'fetchPerformanceData'. Either include it or remove the dependency array.", "line": 34, "column": 6, "nodeType": "ArrayExpression", "endLine": 34, "endColumn": 8, "suggestions": [{"desc": "Update the dependencies array to be: [fetchPerformanceData]", "fix": {"range": [855, 857], "text": "[fetchPerformanceData]"}}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 187, "column": 54, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 187, "endColumn": 57, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [6062, 6065], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [6062, 6065], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 性能监控标签页组件\n */\n\nimport { useState, useEffect } from 'react';\nimport { useWordPressAjax, useI18n } from '../hooks/useWordPress';\nimport { Button } from './Button';\n\nexport function PerformanceTab() {\n  const { request } = useWordPressAjax();\n  const { __ } = useI18n();\n\n  const [performanceData, setPerformanceData] = useState<any>(null);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // 获取性能数据\n  const fetchPerformanceData = async () => {\n    setIsLoading(true);\n    try {\n      const response = await request('notion_to_wordpress_get_performance_data');\n      \n      if (response.success) {\n        setPerformanceData(response.data);\n      }\n    } catch (error) {\n      console.error('Failed to fetch performance data:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchPerformanceData();\n  }, []);\n\n  // 模拟性能数据（如果API不可用）\n  const mockData = {\n    sync_stats: {\n      total_syncs: 42,\n      successful_syncs: 38,\n      failed_syncs: 4,\n      avg_sync_time: '2.3分钟',\n      last_sync: '2小时前',\n    },\n    api_performance: {\n      avg_response_time: 245,\n      total_requests: 1250,\n      failed_requests: 12,\n      cache_hit_rate: 78,\n    },\n    system_resources: {\n      memory_usage: 45,\n      cpu_usage: 23,\n      disk_usage: 67,\n      php_memory_limit: '256M',\n      current_memory: '115M',\n    },\n    recent_activities: [\n      { time: '10:30', action: '智能同步完成', status: 'success', duration: '1.2分钟' },\n      { time: '09:15', action: 'API连接测试', status: 'success', duration: '0.3秒' },\n      { time: '08:45', action: '完全同步', status: 'success', duration: '3.8分钟' },\n      { time: '07:20', action: 'Webhook触发', status: 'warning', duration: '0.8秒' },\n      { time: '06:10', action: '缓存清理', status: 'success', duration: '0.1秒' },\n    ]\n  };\n\n  const data = performanceData || mockData;\n\n  return (\n    <div className=\"notion-wp-settings-section\">\n      <h2>{__('📊 性能监控', '📊 性能监控')}</h2>\n      <p className=\"description\">\n        {__('监控插件的运行性能和系统资源使用情况。', '监控插件的运行性能和系统资源使用情况。')}\n      </p>\n\n      {/* 刷新按钮 */}\n      <div className=\"notion-wp-button-row\">\n        <Button\n          variant=\"secondary\"\n          loading={isLoading}\n          onClick={fetchPerformanceData}\n        >\n          <span className=\"dashicons dashicons-update\" style={{ marginRight: '4px' }} />\n          {__('刷新数据', '刷新数据')}\n        </Button>\n      </div>\n\n      {/* 同步统计 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('🔄 同步统计', '🔄 同步统计')}</h3>\n        <div className=\"performance-grid\">\n          <div className=\"performance-card\">\n            <h4>{data.sync_stats.total_syncs}</h4>\n            <span>{__('总同步次数', '总同步次数')}</span>\n          </div>\n          <div className=\"performance-card success\">\n            <h4>{data.sync_stats.successful_syncs}</h4>\n            <span>{__('成功同步', '成功同步')}</span>\n          </div>\n          <div className=\"performance-card error\">\n            <h4>{data.sync_stats.failed_syncs}</h4>\n            <span>{__('失败同步', '失败同步')}</span>\n          </div>\n          <div className=\"performance-card\">\n            <h4>{data.sync_stats.avg_sync_time}</h4>\n            <span>{__('平均同步时间', '平均同步时间')}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* API性能 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('🌐 API性能', '🌐 API性能')}</h3>\n        <div className=\"performance-grid\">\n          <div className=\"performance-card\">\n            <h4>{data.api_performance.avg_response_time}ms</h4>\n            <span>{__('平均响应时间', '平均响应时间')}</span>\n          </div>\n          <div className=\"performance-card\">\n            <h4>{data.api_performance.total_requests}</h4>\n            <span>{__('总请求数', '总请求数')}</span>\n          </div>\n          <div className=\"performance-card\">\n            <h4>{data.api_performance.failed_requests}</h4>\n            <span>{__('失败请求', '失败请求')}</span>\n          </div>\n          <div className=\"performance-card\">\n            <h4>{data.api_performance.cache_hit_rate}%</h4>\n            <span>{__('缓存命中率', '缓存命中率')}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* 系统资源 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('💻 系统资源', '💻 系统资源')}</h3>\n        <div className=\"resource-monitors\">\n          <div className=\"resource-item\">\n            <label>{__('内存使用', '内存使用')}</label>\n            <div className=\"progress-bar\">\n              <div \n                className=\"progress-fill\" \n                style={{ width: `${data.system_resources.memory_usage}%` }}\n              />\n            </div>\n            <span>{data.system_resources.current_memory} / {data.system_resources.php_memory_limit}</span>\n          </div>\n          \n          <div className=\"resource-item\">\n            <label>{__('CPU使用', 'CPU使用')}</label>\n            <div className=\"progress-bar\">\n              <div \n                className=\"progress-fill\" \n                style={{ width: `${data.system_resources.cpu_usage}%` }}\n              />\n            </div>\n            <span>{data.system_resources.cpu_usage}%</span>\n          </div>\n          \n          <div className=\"resource-item\">\n            <label>{__('磁盘使用', '磁盘使用')}</label>\n            <div className=\"progress-bar\">\n              <div \n                className=\"progress-fill\" \n                style={{ width: `${data.system_resources.disk_usage}%` }}\n              />\n            </div>\n            <span>{data.system_resources.disk_usage}%</span>\n          </div>\n        </div>\n      </div>\n\n      {/* 最近活动 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('📋 最近活动', '📋 最近活动')}</h3>\n        <div className=\"activity-log\">\n          <table className=\"wp-list-table widefat fixed striped\">\n            <thead>\n              <tr>\n                <th>{__('时间', '时间')}</th>\n                <th>{__('操作', '操作')}</th>\n                <th>{__('状态', '状态')}</th>\n                <th>{__('耗时', '耗时')}</th>\n              </tr>\n            </thead>\n            <tbody>\n              {data.recent_activities.map((activity: any, index: number) => (\n                <tr key={index}>\n                  <td>{activity.time}</td>\n                  <td>{activity.action}</td>\n                  <td>\n                    <span className={`status-badge ${activity.status}`}>\n                      {activity.status === 'success' && __('成功', '成功')}\n                      {activity.status === 'warning' && __('警告', '警告')}\n                      {activity.status === 'error' && __('错误', '错误')}\n                    </span>\n                  </td>\n                  <td>{activity.duration}</td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* 性能建议 */}\n      <div className=\"notion-wp-settings-section\">\n        <h3>{__('💡 性能建议', '💡 性能建议')}</h3>\n        <div className=\"performance-recommendations\">\n          <div className=\"recommendation-item\">\n            <span className=\"dashicons dashicons-yes-alt\" style={{ color: '#46b450' }} />\n            <span>{__('API响应时间正常，系统运行良好', 'API响应时间正常，系统运行良好')}</span>\n          </div>\n          <div className=\"recommendation-item\">\n            <span className=\"dashicons dashicons-warning\" style={{ color: '#ffb900' }} />\n            <span>{__('建议启用缓存以提高性能', '建议启用缓存以提高性能')}</span>\n          </div>\n          <div className=\"recommendation-item\">\n            <span className=\"dashicons dashicons-info\" style={{ color: '#00a0d2' }} />\n            <span>{__('可以考虑增加并发请求数以加快同步速度', '可以考虑增加并发请求数以加快同步速度')}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\QuickConfig.tsx", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 18, "column": 58, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 18, "endColumn": 61, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [601, 604], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [601, 604], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 快速配置组件\n */\n\nimport { useState } from 'react';\nimport { useSettings, useAppActions } from '../context/AppContext';\nimport { useWordPressAjax, useI18n } from '../hooks/useWordPress';\nimport { Select, FormRow } from './Input';\nimport { Button } from './Button';\n\nexport function QuickConfig() {\n  const settings = useSettings();\n  const { updateSettings, showToast } = useAppActions();\n  const { request } = useWordPressAjax();\n  const { __ } = useI18n();\n\n  const [isGettingRecommendations, setIsGettingRecommendations] = useState(false);\n  const [recommendations, setRecommendations] = useState<any>(null);\n\n  // 性能级别选项\n  const performanceLevels = [\n    { value: 'conservative', label: __('保守模式 - 适合配置较低的服务器', '保守模式 - 适合配置较低的服务器') },\n    { value: 'balanced', label: __('平衡模式 - 推荐的默认配置', '平衡模式 - 推荐的默认配置') },\n    { value: 'aggressive', label: __('激进模式 - 适合高性能服务器', '激进模式 - 适合高性能服务器') },\n  ];\n\n  // 字段模板选项\n  const fieldTemplates = [\n    { value: 'english', label: __('英文模板 - 适合英文Notion数据库', '英文模板 - 适合英文Notion数据库') },\n    { value: 'chinese', label: __('中文模板 - 适合中文Notion数据库', '中文模板 - 适合中文Notion数据库') },\n    { value: 'mixed', label: __('混合模板 - 中英文兼容', '混合模板 - 中英文兼容') },\n    { value: 'custom', label: __('自定义 - 手动配置所有字段', '自定义 - 手动配置所有字段') },\n  ];\n\n  // 获取智能推荐\n  const handleGetRecommendations = async () => {\n    setIsGettingRecommendations(true);\n    try {\n      const response = await request('notion_to_wordpress_get_smart_recommendations', {\n        api_key: settings.notion_api_key,\n        database_id: settings.notion_database_id,\n      });\n      \n      if (response.success && response.data) {\n        setRecommendations(response.data);\n        showToast({\n          type: 'success',\n          message: '智能推荐已生成',\n        });\n      } else {\n        showToast({\n          type: 'error',\n          message: response.message || '获取推荐失败',\n        });\n      }\n    } catch (error) {\n      showToast({\n        type: 'error',\n        message: '获取推荐失败：' + (error instanceof Error ? error.message : '未知错误'),\n      });\n    } finally {\n      setIsGettingRecommendations(false);\n    }\n  };\n\n  // 应用推荐配置\n  const handleApplyRecommendation = (type: string) => {\n    if (!recommendations || !recommendations[type]) return;\n\n    const config = recommendations[type];\n    updateSettings(config);\n    \n    showToast({\n      type: 'success',\n      message: `已应用${type}推荐配置`,\n    });\n  };\n\n  return (\n    <div className=\"notion-wp-settings-section\">\n      <h2>{__('🚀 快速配置', '🚀 快速配置')}</h2>\n      <p className=\"description\">\n        {__('使用预设模板快速配置插件，适合大多数用户。高级用户可以在其他标签页进行详细配置。', '使用预设模板快速配置插件，适合大多数用户。高级用户可以在其他标签页进行详细配置。')}\n      </p>\n\n      <table className=\"form-table\">\n        <tbody>\n          <FormRow\n            label={__('性能级别', '性能级别')}\n            description={__('选择适合您服务器配置的性能级别。系统会自动设置最优的API分页大小、基准并发数等参数，并根据实时性能动态调整。', '选择适合您服务器配置的性能级别。系统会自动设置最优的API分页大小、基准并发数等参数，并根据实时性能动态调整。')}\n          >\n            <Select\n              value={settings.performance_level}\n              options={performanceLevels}\n              onChange={(value) => updateSettings({ performance_level: value })}\n            />\n          </FormRow>\n\n          <FormRow\n            label={__('字段映射模板', '字段映射模板')}\n            description={__('选择与您的Notion数据库语言匹配的字段映射模板。选择\"自定义\"可在字段映射标签页进行详细配置。', '选择与您的Notion数据库语言匹配的字段映射模板。选择\"自定义\"可在字段映射标签页进行详细配置。')}\n          >\n            <Select\n              value={settings.field_template}\n              options={fieldTemplates}\n              onChange={(value) => updateSettings({ field_template: value })}\n            />\n          </FormRow>\n        </tbody>\n      </table>\n\n      {/* 智能推荐 */}\n      <div className=\"notion-wp-smart-recommendations\">\n        <h3>{__('💡 智能推荐', '💡 智能推荐')}</h3>\n        <div className=\"config-recommendations\">\n          <Button\n            variant=\"secondary\"\n            loading={isGettingRecommendations}\n            onClick={handleGetRecommendations}\n            disabled={!settings.notion_api_key || !settings.notion_database_id}\n          >\n            {__('获取配置建议', '获取配置建议')}\n          </Button>\n          \n          {recommendations && (\n            <div className=\"recommendations-result\">\n              <h4>{__('推荐配置', '推荐配置')}</h4>\n              \n              {recommendations.performance && (\n                <div className=\"recommendation-item\">\n                  <h5>{__('性能优化建议', '性能优化建议')}</h5>\n                  <p>{recommendations.performance.description}</p>\n                  <Button\n                    variant=\"secondary\"\n                    size=\"small\"\n                    onClick={() => handleApplyRecommendation('performance')}\n                  >\n                    {__('应用此配置', '应用此配置')}\n                  </Button>\n                </div>\n              )}\n              \n              {recommendations.field_mapping && (\n                <div className=\"recommendation-item\">\n                  <h5>{__('字段映射建议', '字段映射建议')}</h5>\n                  <p>{recommendations.field_mapping.description}</p>\n                  <Button\n                    variant=\"secondary\"\n                    size=\"small\"\n                    onClick={() => handleApplyRecommendation('field_mapping')}\n                  >\n                    {__('应用此配置', '应用此配置')}\n                  </Button>\n                </div>\n              )}\n              \n              {recommendations.sync_schedule && (\n                <div className=\"recommendation-item\">\n                  <h5>{__('同步计划建议', '同步计划建议')}</h5>\n                  <p>{recommendations.sync_schedule.description}</p>\n                  <Button\n                    variant=\"secondary\"\n                    size=\"small\"\n                    onClick={() => handleApplyRecommendation('sync_schedule')}\n                  >\n                    {__('应用此配置', '应用此配置')}\n                  </Button>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\TabManager.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\ToastContainer.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\WebhookSettings.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\database\\DatabaseToolbar.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\database\\DatabaseView.tsx", "messages": [{"ruleId": "@typescript-eslint/no-unused-vars", "severity": 1, "message": "'children' is defined but never used. Allowed unused args must match /^_/u.", "line": 39, "column": 3, "nodeType": null, "messageId": "unusedVar", "endLine": 39, "endColumn": 11}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 数据库视图主容器组件\n * \n * 这是数据库视图的主要容器组件，负责：\n * - 整体布局和状态管理\n * - 工具栏和视图内容的协调\n * - 与现有EventBus系统的集成\n */\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { DatabaseToolbar } from './DatabaseToolbar';\nimport { TableView } from './views/TableView';\nimport { GalleryView } from './views/GalleryView';\nimport { BoardView } from './views/BoardView';\nimport { useDatabaseView } from '../../hooks/useDatabaseView';\nimport type {\n  DatabaseViewProps,\n  ViewType,\n  DatabaseRecord\n} from './types';\n\n// 注意：原有的reducer代码已被hook替代，不再需要\n\n/**\n * 数据库视图主组件\n */\nexport function DatabaseView({\n  databaseId,\n  defaultViewType = 'table',\n  enableSearch = true,\n  enableFilter = true,\n  enableSort = true,\n  enablePagination = true,\n  pageSize = 20,\n  autoRefresh = false,\n  onRecordClick,\n  onViewTypeChange,\n  className = '',\n  children,\n  ...props\n}: DatabaseViewProps) {\n  // 使用数据库视图hook\n  const databaseView = useDatabaseView(databaseId, {\n    records: {\n      pageSize,\n      enableSearch,\n      enableFilter,\n      enableSort,\n      autoRefresh,\n      initialLoad: !!databaseId,\n    },\n    info: {\n      autoLoad: !!databaseId,\n    },\n  });\n\n  // 本地状态（视图类型和搜索词）\n  const [viewType, setViewType] = useState<ViewType>(defaultViewType);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // 处理视图类型变化\n  const handleViewTypeChange = useCallback((newViewType: ViewType) => {\n    setViewType(newViewType);\n    onViewTypeChange?.(newViewType);\n  }, [onViewTypeChange]);\n\n  // 处理搜索\n  const handleSearchChange = useCallback((newSearchTerm: string) => {\n    setSearchTerm(newSearchTerm);\n\n    // 如果搜索词不为空，执行搜索\n    if (newSearchTerm.trim()) {\n      databaseView.searchRecords(newSearchTerm.trim()).catch(console.error);\n    } else {\n      // 搜索词为空时，重新加载所有记录\n      databaseView.loadRecords().catch(console.error);\n    }\n  }, [databaseView]);\n\n  // 处理过滤\n  const handleFilterClick = useCallback(() => {\n    // TODO: 实现过滤功能\n    console.log('🔍 [数据库视图] 过滤按钮点击');\n  }, []);\n\n  // 处理排序\n  const handleSortClick = useCallback(() => {\n    // TODO: 实现排序功能\n    console.log('↕️ [数据库视图] 排序按钮点击');\n  }, []);\n\n  // 处理刷新\n  const handleRefreshClick = useCallback(() => {\n    console.log('🔄 [数据库视图] 刷新按钮点击');\n    databaseView.refreshAll().catch(console.error);\n  }, [databaseView]);\n\n  // 处理记录点击\n  const handleRecordClick = useCallback((record: DatabaseRecord) => {\n    onRecordClick?.(record);\n  }, [onRecordClick]);\n\n  // 处理加载更多\n  const handleLoadMore = useCallback(() => {\n    if (databaseView.state.canLoadMore) {\n      databaseView.loadMoreRecords().catch(console.error);\n    }\n  }, [databaseView]);\n\n  // 组件挂载时的初始化\n  useEffect(() => {\n    if (!databaseId) {\n      console.warn('🔄 [数据库视图] 数据库ID未配置');\n      return;\n    }\n\n    console.log('🔄 [数据库视图] 初始化中...', { databaseId, defaultViewType });\n  }, [databaseId, defaultViewType]);\n\n  // 渲染加载状态\n  if (databaseView.isLoading && databaseView.state.records.length === 0) {\n    return (\n      <div className={`notion-database-view-component ${className}`} {...props}>\n        <div className=\"database-view-container\">\n          <div className=\"loading-placeholder\">\n            <div className=\"spinner\"></div>\n            <span>加载中...</span>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // 渲染错误状态\n  if (databaseView.hasError && databaseView.state.records.length === 0) {\n    return (\n      <div className={`notion-database-view-component ${className}`} {...props}>\n        <div className=\"database-view-container\">\n          <div className=\"error-placeholder\">\n            <div className=\"error-icon\">❌</div>\n            <div className=\"error-message\">{databaseView.state.error}</div>\n            <button\n              className=\"retry-button\"\n              type=\"button\"\n              onClick={() => databaseView.refreshAll().catch(console.error)}\n            >\n              重试\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // 主要渲染\n  return (\n    <div className={`notion-database-view-component ${className}`} {...props}>\n      {/* 数据库工具栏 */}\n      <DatabaseToolbar\n        viewType={viewType}\n        searchTerm={searchTerm}\n        enableSearch={enableSearch}\n        enableFilter={enableFilter}\n        enableSort={enableSort}\n        enableRefresh={true}\n        isLoading={databaseView.isLoading}\n        recordCount={databaseView.state.totalCount}\n        onViewTypeChange={handleViewTypeChange}\n        onSearchChange={handleSearchChange}\n        onFilterClick={handleFilterClick}\n        onSortClick={handleSortClick}\n        onRefreshClick={handleRefreshClick}\n      />\n\n      {/* 视图容器 */}\n      <div className=\"database-view-container\">\n        {databaseView.state.isEmpty && !databaseView.isLoading ? (\n          <div className=\"empty-placeholder\">\n            <div className=\"empty-icon\">📭</div>\n            <div className=\"empty-message\">\n              {searchTerm ? `未找到包含\"${searchTerm}\"的记录` : '暂无记录'}\n            </div>\n            {searchTerm && (\n              <button\n                className=\"clear-search-button\"\n                type=\"button\"\n                onClick={() => handleSearchChange('')}\n              >\n                清除搜索\n              </button>\n            )}\n          </div>\n        ) : (\n          <div className={`notion-database-view notion-database-view-${viewType}`}>\n            {/* 根据视图类型渲染对应组件 */}\n            {viewType === 'table' && databaseView.state.databaseInfo && (\n              <TableView\n                records={databaseView.state.records}\n                databaseInfo={databaseView.state.databaseInfo}\n                options={{\n                  viewType,\n                  enableInteraction: true,\n                  responsive: true,\n                }}\n                onRecordClick={handleRecordClick}\n              />\n            )}\n\n            {viewType === 'list' && (\n              <div className=\"view-placeholder\">\n                <div className=\"placeholder-icon\">📋</div>\n                <div className=\"placeholder-message\">列表视图即将推出</div>\n                <div className=\"records-count\">\n                  {databaseView.state.records.length} 条记录\n                </div>\n              </div>\n            )}\n\n            {viewType === 'gallery' && databaseView.state.databaseInfo && (\n              <GalleryView\n                records={databaseView.state.records}\n                databaseInfo={databaseView.state.databaseInfo}\n                options={{\n                  viewType,\n                  enableInteraction: true,\n                  responsive: true,\n                }}\n                onRecordClick={handleRecordClick}\n              />\n            )}\n\n            {viewType === 'board' && databaseView.state.databaseInfo && (\n              <BoardView\n                records={databaseView.state.records}\n                databaseInfo={databaseView.state.databaseInfo}\n                options={{\n                  viewType,\n                  enableInteraction: true,\n                  responsive: true,\n                }}\n                onRecordClick={handleRecordClick}\n              />\n            )}\n\n            {(viewType === 'calendar' || viewType === 'timeline') && (\n              <div className=\"view-placeholder\">\n                <div className=\"placeholder-icon\">📅</div>\n                <div className=\"placeholder-message\">{viewType === 'calendar' ? '日历' : '时间线'}视图即将推出</div>\n                <div className=\"records-count\">\n                  {databaseView.state.records.length} 条记录\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* 分页 */}\n      {enablePagination && databaseView.state.canLoadMore && (\n        <div className=\"database-pagination\">\n          <button\n            className=\"load-more-button\"\n            type=\"button\"\n            onClick={handleLoadMore}\n            disabled={databaseView.isLoading}\n          >\n            {databaseView.isLoading ? '加载中...' : '加载更多'}\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default DatabaseView;\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\database\\views\\BoardView.tsx", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 90, "column": 41, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 90, "endColumn": 44, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [2235, 2238], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [2235, 2238], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 124, "column": 52, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 124, "endColumn": 55, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [3005, 3008], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [3005, 3008], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 154, "column": 51, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 154, "endColumn": 54, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [4042, 4045], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [4042, 4045], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 看板视图React组件\n * \n * 支持按状态分组显示、动态列管理和卡片交互\n */\n\nimport { useState, useMemo, useCallback } from 'react';\nimport type {\n  BoardViewProps,\n  DatabaseRecord,\n  DatabaseInfo\n} from '../types';\n\n// 看板列组件Props\ninterface BoardColumnProps {\n  title: string;\n  records: DatabaseRecord[];\n  databaseInfo: DatabaseInfo | null;\n  onRecordClick?: (record: DatabaseRecord) => void;\n  showProperties?: string[];\n  hideProperties?: string[];\n}\n\n// 看板卡片组件Props\ninterface BoardCardProps {\n  record: DatabaseRecord;\n  databaseInfo: DatabaseInfo | null;\n  onClick?: (record: DatabaseRecord) => void;\n  showProperties?: string[];\n  hideProperties?: string[];\n}\n\n// 记录属性组件Props\ninterface RecordPropertiesProps {\n  record: DatabaseRecord;\n  databaseInfo: DatabaseInfo | null;\n  showProperties?: string[];\n  hideProperties?: string[];\n  maxProperties?: number;\n}\n\n/**\n * 按状态分组记录\n */\nfunction groupRecordsByStatus(\n  records: DatabaseRecord[],\n  _databaseInfo: DatabaseInfo | null\n): Record<string, DatabaseRecord[]> {\n  const grouped: Record<string, DatabaseRecord[]> = {};\n\n  records.forEach(record => {\n    // 查找状态属性\n    let status = '未分类';\n\n    Object.entries(record.properties).forEach(([name, value]) => {\n      if (\n        name.toLowerCase().includes('status') ||\n        name.toLowerCase().includes('状态') ||\n        name.toLowerCase().includes('stage') ||\n        name.toLowerCase().includes('phase')\n      ) {\n        if (value && typeof value === 'object' && 'name' in value) {\n          status = value.name;\n        } else if (typeof value === 'string') {\n          status = value;\n        }\n      }\n    });\n\n    if (!grouped[status]) {\n      grouped[status] = [];\n    }\n    grouped[status].push(record);\n  });\n\n  return grouped;\n}\n\n/**\n * 记录标题组件\n */\nfunction RecordTitle({ record }: { record: DatabaseRecord }) {\n  // 查找标题属性\n  const getTitle = useCallback((rec: DatabaseRecord): string => {\n    for (const [name, value] of Object.entries(rec.properties)) {\n      if (name.toLowerCase().includes('title') || \n          name.toLowerCase().includes('名称') ||\n          name.toLowerCase().includes('name')) {\n        if (value && typeof value === 'object' && 'title' in value && Array.isArray(value.title)) {\n          return value.title.map((item: any) => item.plain_text || '').join('') || '无标题';\n        } else if (value && typeof value === 'object' && 'plain_text' in value) {\n          return value.plain_text || '无标题';\n        } else if (typeof value === 'string') {\n          return value || '无标题';\n        }\n      }\n    }\n    return '无标题';\n  }, []);\n\n  const title = getTitle(record);\n\n  return (\n    <div className=\"notion-record-title\" title={title}>\n      {title}\n    </div>\n  );\n}\n\n/**\n * 记录属性组件\n */\nfunction RecordProperties({ \n  record, \n  databaseInfo, \n  showProperties = [], \n  hideProperties = [],\n  maxProperties = 3 \n}: RecordPropertiesProps) {\n  // 获取可见属性\n  const visibleProperties = useMemo(() => {\n    if (!databaseInfo?.properties) return [];\n\n    const properties: Array<{ name: string; value: any; type: string }> = [];\n\n    Object.entries(record.properties).forEach(([name, value]) => {\n      // 跳过标题属性和状态属性（已在其他地方显示）\n      if (name.toLowerCase().includes('title') || \n          name.toLowerCase().includes('名称') ||\n          name.toLowerCase().includes('name') ||\n          name.toLowerCase().includes('status') ||\n          name.toLowerCase().includes('状态')) {\n        return;\n      }\n\n      // 检查是否应该显示此属性\n      const shouldShow = showProperties.length === 0 || showProperties.includes(name);\n      const shouldHide = hideProperties.includes(name);\n\n      if (shouldShow && !shouldHide && value !== null && value !== undefined) {\n        const propertyConfig = databaseInfo.properties[name];\n        properties.push({\n          name,\n          value,\n          type: propertyConfig?.type || 'text',\n        });\n      }\n    });\n\n    return properties.slice(0, maxProperties);\n  }, [record.properties, databaseInfo, showProperties, hideProperties, maxProperties]);\n\n  // 格式化属性值\n  const formatPropertyValue = useCallback((value: any, type: string): string => {\n    if (value === null || value === undefined) return '';\n\n    switch (type) {\n      case 'rich_text':\n        if (Array.isArray(value)) {\n          return value.map(item => item.plain_text || '').join('');\n        }\n        return value?.plain_text || String(value);\n      \n      case 'number':\n        return typeof value === 'number' ? value.toLocaleString() : String(value);\n      \n      case 'select':\n        return value?.name || String(value);\n      \n      case 'multi_select':\n        if (Array.isArray(value)) {\n          return value.map(item => item.name || item).join(', ');\n        }\n        return String(value);\n      \n      case 'date':\n        if (value?.start) {\n          return new Date(value.start).toLocaleDateString();\n        }\n        return String(value);\n      \n      case 'checkbox':\n        return value ? '✓' : '✗';\n      \n      case 'url':\n        return value ? String(value) : '';\n      \n      case 'email':\n        return value ? String(value) : '';\n      \n      case 'phone_number':\n        return value ? String(value) : '';\n      \n      default:\n        return String(value);\n    }\n  }, []);\n\n  if (visibleProperties.length === 0) {\n    return null;\n  }\n\n  return (\n    <div className=\"notion-record-properties\">\n      {visibleProperties.map((property, index) => {\n        const formattedValue = formatPropertyValue(property.value, property.type);\n\n        if (!formattedValue) return null;\n\n        return (\n          <div key={index} className=\"notion-record-property\">\n            <span className=\"notion-property-label\">{property.name}:</span>\n            <span className={`notion-property-value property-type-${property.type}`}>\n              {formattedValue}\n            </span>\n          </div>\n        );\n      })}\n    </div>\n  );\n}\n\n/**\n * 看板卡片组件\n */\nfunction BoardCard({\n  record,\n  databaseInfo,\n  onClick,\n  showProperties,\n  hideProperties\n}: BoardCardProps) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  const handleClick = useCallback(() => {\n    onClick?.(record);\n  }, [record, onClick]);\n\n  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' || e.key === ' ') {\n      e.preventDefault();\n      handleClick();\n    }\n  }, [handleClick]);\n\n  const handleMouseEnter = useCallback(() => {\n    setIsHovered(true);\n  }, []);\n\n  const handleMouseLeave = useCallback(() => {\n    setIsHovered(false);\n  }, []);\n\n  return (\n    <div\n      className={`notion-board-card ${onClick ? 'notion-board-card-interactive' : ''} ${isHovered ? 'hovered' : ''}`}\n      onClick={handleClick}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n      role={onClick ? 'button' : undefined}\n      tabIndex={onClick ? 0 : undefined}\n      onKeyDown={onClick ? handleKeyDown : undefined}\n      data-record-id={record.id}\n    >\n      <RecordTitle record={record} />\n      <RecordProperties\n        record={record}\n        databaseInfo={databaseInfo}\n        showProperties={showProperties}\n        hideProperties={hideProperties}\n        maxProperties={3}\n      />\n    </div>\n  );\n}\n\n/**\n * 看板列组件\n */\nfunction BoardColumn({\n  title,\n  records,\n  databaseInfo,\n  onRecordClick,\n  showProperties,\n  hideProperties\n}: BoardColumnProps) {\n  return (\n    <div className=\"notion-board-column\">\n      <div className=\"notion-board-header\">\n        <span className=\"column-title\">{title}</span>\n        <span className=\"column-count\">({records.length})</span>\n      </div>\n\n      <div className=\"notion-board-list\">\n        {records.length === 0 ? (\n          <div className=\"empty-column\">\n            <div className=\"empty-icon\">📋</div>\n            <div className=\"empty-message\">暂无卡片</div>\n          </div>\n        ) : (\n          records.map((record) => (\n            <BoardCard\n              key={record.id}\n              record={record}\n              databaseInfo={databaseInfo}\n              onClick={onRecordClick}\n              showProperties={showProperties}\n              hideProperties={hideProperties}\n            />\n          ))\n        )}\n      </div>\n    </div>\n  );\n}\n\n/**\n * 看板视图主组件\n */\nexport function BoardView({\n  records,\n  databaseInfo,\n  options,\n  onRecordClick,\n  className = '',\n  children,\n  ...props\n}: BoardViewProps) {\n  // 按状态分组记录\n  const groupedRecords = useMemo(() => {\n    return groupRecordsByStatus(records, databaseInfo);\n  }, [records, databaseInfo]);\n\n  // 获取状态列表（按记录数量排序）\n  const statusColumns = useMemo(() => {\n    return Object.entries(groupedRecords)\n      .sort(([, a], [, b]) => b.length - a.length) // 按记录数量降序排列\n      .map(([status, statusRecords]) => ({\n        status,\n        records: statusRecords,\n      }));\n  }, [groupedRecords]);\n\n  // 如果没有记录，显示空状态\n  if (records.length === 0) {\n    return (\n      <div className={`notion-database-view-board ${className}`} {...props}>\n        <div className=\"empty-board\">\n          <div className=\"empty-icon\">📋</div>\n          <div className=\"empty-message\">暂无记录</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`notion-database-view-board ${className}`} {...props}>\n      <div className=\"notion-database-board\">\n        {statusColumns.map(({ status, records: statusRecords }) => (\n          <BoardColumn\n            key={status}\n            title={status}\n            records={statusRecords}\n            databaseInfo={databaseInfo}\n            onRecordClick={onRecordClick}\n            showProperties={options?.showProperties}\n            hideProperties={options?.hideProperties}\n          />\n        ))}\n      </div>\n      {children}\n    </div>\n  );\n}\n\nexport default BoardView;\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\database\\views\\GalleryView.tsx", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 47, "column": 43, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 47, "endColumn": 46, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [1030, 1033], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [1030, 1033], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 121, "column": 41, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 121, "endColumn": 44, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [3080, 3083], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [3080, 3083], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 155, "column": 52, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 155, "endColumn": 55, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [3850, 3853], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [3850, 3853], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 183, "column": 51, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 183, "endColumn": 54, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [4783, 4786], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [4783, 4786], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 画廊视图React组件\n * \n * 支持卡片式布局、图片懒加载、响应式网格和交互动画\n */\n\nimport { useState, useMemo, useCallback, useEffect, useRef } from 'react';\nimport type {\n  GalleryViewProps,\n  DatabaseRecord,\n  DatabaseInfo\n} from '../types';\n\n// 画廊卡片组件Props\ninterface GalleryCardProps {\n  record: DatabaseRecord;\n  databaseInfo: DatabaseInfo | null;\n  onClick?: (record: DatabaseRecord) => void;\n  showProperties?: string[];\n  hideProperties?: string[];\n}\n\n// 记录封面组件Props\ninterface RecordCoverProps {\n  record: DatabaseRecord;\n  className?: string;\n}\n\n// 记录属性组件Props\ninterface RecordPropertiesProps {\n  record: DatabaseRecord;\n  databaseInfo: DatabaseInfo | null;\n  showProperties?: string[];\n  hideProperties?: string[];\n  maxProperties?: number;\n}\n\n/**\n * 记录封面组件\n */\nfunction RecordCover({ record, className = '' }: RecordCoverProps) {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const imgRef = useRef<HTMLImageElement>(null);\n\n  // 获取封面URL\n  const getCoverUrl = useCallback((cover: any): string | null => {\n    if (!cover) return null;\n    \n    if (cover.type === 'file' && cover.file?.url) {\n      return cover.file.url;\n    } else if (cover.type === 'external' && cover.external?.url) {\n      return cover.external.url;\n    }\n    \n    return null;\n  }, []);\n\n  const coverUrl = getCoverUrl(record.cover);\n\n  // 处理图片加载\n  const handleImageLoad = useCallback(() => {\n    setImageLoaded(true);\n    setImageError(false);\n  }, []);\n\n  const handleImageError = useCallback(() => {\n    setImageError(true);\n    setImageLoaded(false);\n  }, []);\n\n  // 如果没有封面，显示默认占位符\n  if (!coverUrl) {\n    return (\n      <div className={`notion-record-cover no-cover ${className}`}>\n        <div className=\"cover-placeholder\">\n          <span className=\"placeholder-icon\">🖼️</span>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`notion-record-cover ${className}`}>\n      <img\n        ref={imgRef}\n        src={coverUrl}\n        alt=\"Record cover\"\n        className={`notion-lazy-image ${imageLoaded ? 'loaded' : ''} ${imageError ? 'error' : ''}`}\n        onLoad={handleImageLoad}\n        onError={handleImageError}\n        loading=\"lazy\"\n      />\n      {!imageLoaded && !imageError && (\n        <div className=\"cover-loading\">\n          <div className=\"loading-spinner\"></div>\n        </div>\n      )}\n      {imageError && (\n        <div className=\"cover-error\">\n          <span className=\"error-icon\">❌</span>\n          <span className=\"error-text\">加载失败</span>\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * 记录标题组件\n */\nfunction RecordTitle({ record }: { record: DatabaseRecord }) {\n  // 查找标题属性\n  const getTitle = useCallback((rec: DatabaseRecord): string => {\n    // 查找标题属性\n    for (const [name, value] of Object.entries(rec.properties)) {\n      if (name.toLowerCase().includes('title') || \n          name.toLowerCase().includes('名称') ||\n          name.toLowerCase().includes('name')) {\n        if (value && typeof value === 'object' && 'title' in value && Array.isArray(value.title)) {\n          return value.title.map((item: any) => item.plain_text || '').join('') || '无标题';\n        } else if (value && typeof value === 'object' && 'plain_text' in value) {\n          return value.plain_text || '无标题';\n        } else if (typeof value === 'string') {\n          return value || '无标题';\n        }\n      }\n    }\n    return '无标题';\n  }, []);\n\n  const title = getTitle(record);\n\n  return (\n    <div className=\"notion-record-title\" title={title}>\n      {title}\n    </div>\n  );\n}\n\n/**\n * 记录属性组件\n */\nfunction RecordProperties({ \n  record, \n  databaseInfo, \n  showProperties = [], \n  hideProperties = [],\n  maxProperties = 3 \n}: RecordPropertiesProps) {\n  // 获取可见属性\n  const visibleProperties = useMemo(() => {\n    if (!databaseInfo?.properties) return [];\n\n    const properties: Array<{ name: string; value: any; type: string }> = [];\n\n    Object.entries(record.properties).forEach(([name, value]) => {\n      // 跳过标题属性（已在标题中显示）\n      if (name.toLowerCase().includes('title') || \n          name.toLowerCase().includes('名称') ||\n          name.toLowerCase().includes('name')) {\n        return;\n      }\n\n      // 检查是否应该显示此属性\n      const shouldShow = showProperties.length === 0 || showProperties.includes(name);\n      const shouldHide = hideProperties.includes(name);\n\n      if (shouldShow && !shouldHide && value !== null && value !== undefined) {\n        const propertyConfig = databaseInfo.properties[name];\n        properties.push({\n          name,\n          value,\n          type: propertyConfig?.type || 'text',\n        });\n      }\n    });\n\n    return properties.slice(0, maxProperties);\n  }, [record.properties, databaseInfo, showProperties, hideProperties, maxProperties]);\n\n  // 格式化属性值\n  const formatPropertyValue = useCallback((value: any, type: string): string => {\n    if (value === null || value === undefined) return '';\n\n    switch (type) {\n      case 'rich_text':\n        if (Array.isArray(value)) {\n          return value.map(item => item.plain_text || '').join('');\n        }\n        return value?.plain_text || String(value);\n      \n      case 'number':\n        return typeof value === 'number' ? value.toLocaleString() : String(value);\n      \n      case 'select':\n        return value?.name || String(value);\n      \n      case 'multi_select':\n        if (Array.isArray(value)) {\n          return value.map(item => item.name || item).join(', ');\n        }\n        return String(value);\n      \n      case 'date':\n        if (value?.start) {\n          return new Date(value.start).toLocaleDateString();\n        }\n        return String(value);\n      \n      case 'checkbox':\n        return value ? '✓' : '✗';\n      \n      case 'url':\n        return value ? String(value) : '';\n      \n      case 'email':\n        return value ? String(value) : '';\n      \n      case 'phone_number':\n        return value ? String(value) : '';\n      \n      default:\n        return String(value);\n    }\n  }, []);\n\n  if (visibleProperties.length === 0) {\n    return null;\n  }\n\n  return (\n    <div className=\"notion-record-properties\">\n      {visibleProperties.map((property, index) => {\n        const formattedValue = formatPropertyValue(property.value, property.type);\n\n        if (!formattedValue) return null;\n\n        return (\n          <div key={index} className=\"notion-record-property\">\n            <span className=\"notion-property-label\">{property.name}:</span>\n            <span className={`notion-property-value property-type-${property.type}`}>\n              {formattedValue}\n            </span>\n          </div>\n        );\n      })}\n    </div>\n  );\n}\n\n/**\n * 画廊卡片组件\n */\nfunction GalleryCard({\n  record,\n  databaseInfo,\n  onClick,\n  showProperties,\n  hideProperties\n}: GalleryCardProps) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  const handleClick = useCallback(() => {\n    onClick?.(record);\n  }, [record, onClick]);\n\n  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' || e.key === ' ') {\n      e.preventDefault();\n      handleClick();\n    }\n  }, [handleClick]);\n\n  const handleMouseEnter = useCallback(() => {\n    setIsHovered(true);\n  }, []);\n\n  const handleMouseLeave = useCallback(() => {\n    setIsHovered(false);\n  }, []);\n\n  return (\n    <div\n      className={`notion-gallery-card ${onClick ? 'notion-gallery-card-interactive' : ''} ${isHovered ? 'hovered' : ''}`}\n      onClick={handleClick}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n      role={onClick ? 'button' : undefined}\n      tabIndex={onClick ? 0 : undefined}\n      onKeyDown={onClick ? handleKeyDown : undefined}\n      data-record-id={record.id}\n    >\n      <RecordCover record={record} />\n\n      <div className=\"notion-gallery-content\">\n        <RecordTitle record={record} />\n        <RecordProperties\n          record={record}\n          databaseInfo={databaseInfo}\n          showProperties={showProperties}\n          hideProperties={hideProperties}\n          maxProperties={3}\n        />\n      </div>\n    </div>\n  );\n}\n\n/**\n * 画廊视图主组件\n */\nexport function GalleryView({\n  records,\n  databaseInfo,\n  options,\n  onRecordClick,\n  className = '',\n  children,\n  ...props\n}: GalleryViewProps) {\n  const galleryRef = useRef<HTMLDivElement>(null);\n\n  // 懒加载效果\n  useEffect(() => {\n    if (galleryRef.current) {\n      // 触发懒加载刷新\n      const images = galleryRef.current.querySelectorAll('img[loading=\"lazy\"]');\n      images.forEach(img => {\n        // 确保图片在视口中时加载\n        if (img instanceof HTMLImageElement && !img.complete) {\n          const observer = new IntersectionObserver((entries) => {\n            entries.forEach(entry => {\n              if (entry.isIntersecting) {\n                const image = entry.target as HTMLImageElement;\n                if (image.dataset.src) {\n                  image.src = image.dataset.src;\n                }\n                observer.unobserve(image);\n              }\n            });\n          }, {\n            rootMargin: '50px 0px',\n            threshold: 0.1,\n          });\n          observer.observe(img);\n        }\n      });\n    }\n  }, [records]);\n\n  // 如果没有记录，显示空状态\n  if (records.length === 0) {\n    return (\n      <div className={`notion-database-view-gallery ${className}`} {...props}>\n        <div className=\"empty-gallery\">\n          <div className=\"empty-icon\">🖼️</div>\n          <div className=\"empty-message\">暂无记录</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`notion-database-view-gallery ${className}`} {...props}>\n      <div ref={galleryRef} className=\"notion-database-gallery\">\n        {records.map((record) => (\n          <GalleryCard\n            key={record.id}\n            record={record}\n            databaseInfo={databaseInfo}\n            onClick={onRecordClick}\n            showProperties={options?.showProperties}\n            hideProperties={options?.hideProperties}\n          />\n        ))}\n      </div>\n      {children}\n    </div>\n  );\n}\n\nexport default GalleryView;\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\database\\views\\TableView.tsx", "messages": [{"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 82, "column": 41, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 82, "endColumn": 44, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [1867, 1870], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [1867, 1870], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 203, "column": 76, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 203, "endColumn": 79, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [4924, 4927], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [4924, 4927], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 212, "column": 77, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 212, "endColumn": 80, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [5236, 5239], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [5236, 5239], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "react-hooks/exhaustive-deps", "severity": 1, "message": "React Hook useCallback has an unnecessary dependency: 'currentSortConfig'. Either exclude it or remove the dependency array.", "line": 324, "column": 6, "nodeType": "ArrayExpression", "endLine": 324, "endColumn": 33, "suggestions": [{"desc": "Update the dependencies array to be: [onSort]", "fix": {"range": [7882, 7909], "text": "[onSort]"}}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 表格视图React组件\n * \n * 支持动态列显示、排序、行选择和响应式设计\n */\n\nimport { useState, useMemo, useCallback } from 'react';\nimport type {\n  TableViewProps,\n  DatabaseRecord,\n  DatabaseInfo,\n  PropertyConfig\n} from '../types';\n\n// 排序配置接口\ninterface SortConfig {\n  property: string;\n  direction: 'asc' | 'desc';\n}\n\n// 表格头部组件Props\ninterface TableHeaderProps {\n  properties: PropertyConfig[];\n  sortConfig: SortConfig | null;\n  onSort: (property: string) => void;\n}\n\n// 表格行组件Props\ninterface TableRowProps {\n  record: DatabaseRecord;\n  properties: PropertyConfig[];\n  onClick?: (record: DatabaseRecord) => void;\n  isSelected?: boolean;\n}\n\n// 表格单元格组件Props\ninterface TableCellProps {\n  record: DatabaseRecord;\n  property: PropertyConfig;\n}\n\n/**\n * 表格头部组件\n */\nfunction TableHeader({ properties, sortConfig, onSort }: TableHeaderProps) {\n  return (\n    <div className=\"notion-table-header\">\n      {properties.map((property) => (\n        <div\n          key={property.name}\n          className={`notion-table-header-cell ${sortConfig?.property === property.name ? 'sorted' : ''}`}\n          style={{ width: property.width }}\n          onClick={() => onSort(property.name)}\n          role=\"button\"\n          tabIndex={0}\n          onKeyDown={(e) => {\n            if (e.key === 'Enter' || e.key === ' ') {\n              e.preventDefault();\n              onSort(property.name);\n            }\n          }}\n        >\n          <span className=\"header-text\">{property.name}</span>\n          {sortConfig?.property === property.name && (\n            <span className=\"sort-indicator\">\n              {sortConfig.direction === 'asc' ? '↑' : '↓'}\n            </span>\n          )}\n        </div>\n      ))}\n    </div>\n  );\n}\n\n/**\n * 表格单元格组件\n */\nfunction TableCell({ record, property }: TableCellProps) {\n  const value = record.properties[property.name];\n  \n  // 格式化属性值\n  const formatValue = useCallback((val: any, prop: PropertyConfig): string => {\n    if (val === null || val === undefined) {\n      return '';\n    }\n\n    switch (prop.type) {\n      case 'title':\n        return val?.title?.[0]?.plain_text || val?.plain_text || String(val);\n      \n      case 'rich_text':\n        if (Array.isArray(val)) {\n          return val.map(item => item.plain_text || '').join('');\n        }\n        return val?.plain_text || String(val);\n      \n      case 'number':\n        return typeof val === 'number' ? val.toLocaleString() : String(val);\n      \n      case 'select':\n        return val?.name || String(val);\n      \n      case 'multi_select':\n        if (Array.isArray(val)) {\n          return val.map(item => item.name || item).join(', ');\n        }\n        return String(val);\n      \n      case 'date':\n        if (val?.start) {\n          return new Date(val.start).toLocaleDateString();\n        }\n        return String(val);\n      \n      case 'checkbox':\n        return val ? '✓' : '✗';\n      \n      case 'url':\n        return val ? String(val) : '';\n      \n      case 'email':\n        return val ? String(val) : '';\n      \n      case 'phone_number':\n        return val ? String(val) : '';\n      \n      default:\n        return String(val);\n    }\n  }, []);\n\n  const formattedValue = formatValue(value, property);\n\n  return (\n    <div \n      className=\"notion-table-cell\"\n      style={{ width: property.width }}\n      title={formattedValue}\n    >\n      {property.type === 'url' && formattedValue ? (\n        <a \n          href={formattedValue} \n          target=\"_blank\" \n          rel=\"noopener noreferrer\"\n          onClick={(e) => e.stopPropagation()}\n        >\n          {formattedValue}\n        </a>\n      ) : property.type === 'email' && formattedValue ? (\n        <a \n          href={`mailto:${formattedValue}`}\n          onClick={(e) => e.stopPropagation()}\n        >\n          {formattedValue}\n        </a>\n      ) : (\n        <span className={`cell-content cell-type-${property.type}`}>\n          {formattedValue}\n        </span>\n      )}\n    </div>\n  );\n}\n\n/**\n * 表格行组件\n */\nfunction TableRow({ record, properties, onClick, isSelected = false }: TableRowProps) {\n  const handleClick = useCallback(() => {\n    onClick?.(record);\n  }, [record, onClick]);\n\n  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' || e.key === ' ') {\n      e.preventDefault();\n      handleClick();\n    }\n  }, [handleClick]);\n\n  return (\n    <div\n      className={`notion-table-row ${onClick ? 'notion-table-row-interactive' : ''} ${isSelected ? 'selected' : ''}`}\n      onClick={handleClick}\n      role={onClick ? 'button' : undefined}\n      tabIndex={onClick ? 0 : undefined}\n      onKeyDown={onClick ? handleKeyDown : undefined}\n      data-record-id={record.id}\n    >\n      {properties.map((property) => (\n        <TableCell\n          key={property.name}\n          record={record}\n          property={property}\n        />\n      ))}\n    </div>\n  );\n}\n\n/**\n * 获取可见属性\n */\nfunction getVisibleProperties(databaseInfo: DatabaseInfo | null, options?: any): PropertyConfig[] {\n  if (!databaseInfo?.properties) {\n    return [];\n  }\n\n  const properties: PropertyConfig[] = [];\n  const showProperties = options?.showProperties || [];\n  const hideProperties = options?.hideProperties || [];\n\n  Object.entries(databaseInfo.properties).forEach(([name, config]: [string, any]) => {\n    // 检查是否应该显示此属性\n    const shouldShow = showProperties.length === 0 || showProperties.includes(name);\n    const shouldHide = hideProperties.includes(name);\n\n    if (shouldShow && !shouldHide) {\n      properties.push({\n        name,\n        type: config.type || 'text',\n        visible: true,\n        width: getPropertyWidth(config.type),\n        format: getPropertyFormat(config.type),\n      });\n    }\n  });\n\n  return properties;\n}\n\n/**\n * 获取属性宽度\n */\nfunction getPropertyWidth(type: string): string {\n  switch (type) {\n    case 'checkbox':\n      return '60px';\n    case 'number':\n      return '100px';\n    case 'date':\n      return '120px';\n    case 'select':\n      return '150px';\n    case 'title':\n      return '200px';\n    default:\n      return '150px';\n  }\n}\n\n/**\n * 获取属性格式\n */\nfunction getPropertyFormat(type: string): string {\n  return type;\n}\n\n/**\n * 表格视图主组件\n */\nexport function TableView({\n  records,\n  databaseInfo,\n  options,\n  sortConfig,\n  onSort,\n  onRecordClick,\n  className = '',\n  children,\n  ...props\n}: TableViewProps) {\n  // 本地排序状态（如果没有外部排序配置）\n  const [localSortConfig, setLocalSortConfig] = useState<SortConfig | null>(null);\n  \n  // 使用外部排序配置或本地排序配置\n  const currentSortConfig = sortConfig || localSortConfig;\n\n  // 获取可见属性\n  const visibleProperties = useMemo(() => {\n    return getVisibleProperties(databaseInfo, options);\n  }, [databaseInfo, options]);\n\n  // 排序记录\n  const sortedRecords = useMemo(() => {\n    if (!currentSortConfig) {\n      return records;\n    }\n\n    return [...records].sort((a, b) => {\n      const aValue = a.properties[currentSortConfig.property];\n      const bValue = b.properties[currentSortConfig.property];\n      \n      // 处理空值\n      if (aValue === null || aValue === undefined) return 1;\n      if (bValue === null || bValue === undefined) return -1;\n      \n      // 根据类型进行比较\n      let comparison = 0;\n      \n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        comparison = aValue.localeCompare(bValue);\n      } else if (typeof aValue === 'number' && typeof bValue === 'number') {\n        comparison = aValue - bValue;\n      } else {\n        comparison = String(aValue).localeCompare(String(bValue));\n      }\n      \n      return currentSortConfig.direction === 'asc' ? comparison : -comparison;\n    });\n  }, [records, currentSortConfig]);\n\n  // 处理排序\n  const handleSort = useCallback((property: string) => {\n    if (onSort) {\n      // 使用外部排序处理\n      onSort(property);\n    } else {\n      // 使用本地排序\n      setLocalSortConfig(prev => ({\n        property,\n        direction: prev?.property === property && prev.direction === 'asc' ? 'desc' : 'asc'\n      }));\n    }\n  }, [onSort, currentSortConfig]);\n\n  // 如果没有记录，显示空状态\n  if (records.length === 0) {\n    return (\n      <div className={`notion-database-view-table ${className}`} {...props}>\n        <div className=\"empty-table\">\n          <div className=\"empty-icon\">📋</div>\n          <div className=\"empty-message\">暂无数据</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`notion-database-view-table ${className}`} {...props}>\n      <div className=\"notion-database-table\">\n        <TableHeader\n          properties={visibleProperties}\n          sortConfig={currentSortConfig}\n          onSort={handleSort}\n        />\n        <div className=\"notion-table-body\">\n          {sortedRecords.map((record) => (\n            <TableRow\n              key={record.id}\n              record={record}\n              properties={visibleProperties}\n              onClick={onRecordClick}\n            />\n          ))}\n        </div>\n      </div>\n      {children}\n    </div>\n  );\n}\n\nexport default TableView;\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\database\\views\\VirtualizedGalleryView.tsx", "messages": [{"ruleId": "react-hooks/exhaustive-deps", "severity": 1, "message": "The 'config' object makes the dependencies of useMemo Hook (at line 62) change on every render. To fix this, wrap the initialization of 'config' in its own useMemo() Hook.", "line": 48, "column": 9, "nodeType": "VariableDeclarator", "endLine": 48, "endColumn": 71}, {"ruleId": "react-hooks/exhaustive-deps", "severity": 1, "message": "The 'config' object makes the dependencies of useMemo Hook (at line 76) change on every render. To fix this, wrap the initialization of 'config' in its own useMemo() Hook.", "line": 48, "column": 9, "nodeType": "VariableDeclarator", "endLine": 48, "endColumn": 71}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 161, "column": 17, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 161, "endColumn": 20, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [4389, 4392], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [4389, 4392], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 165, "column": 13, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 165, "endColumn": 16, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [4487, 4490], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [4487, 4490], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 184, "column": 43, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 184, "endColumn": 46, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [4913, 4916], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [4913, 4916], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 203, "column": 41, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 203, "endColumn": 44, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [5621, 5624], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [5621, 5624], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 虚拟化画廊视图React组件\n * \n * 为大数据量场景提供性能优化的画廊视图\n */\n\nimport React, { useState, useMemo, useCallback, useRef } from 'react';\nimport type { GalleryViewProps, DatabaseRecord } from '../types';\n\n// 虚拟滚动配置\ninterface VirtualScrollConfig {\n  itemHeight: number;\n  itemWidth: number;\n  containerHeight: number;\n  columnsCount: number;\n  gap: number;\n  overscan: number; // 预渲染的额外行数\n}\n\n// 虚拟化画廊Props\ninterface VirtualizedGalleryViewProps extends GalleryViewProps {\n  virtualScrollConfig?: Partial<VirtualScrollConfig>;\n}\n\n// 默认虚拟滚动配置\nconst DEFAULT_VIRTUAL_CONFIG: VirtualScrollConfig = {\n  itemHeight: 320, // 每个卡片高度\n  itemWidth: 280, // 每个卡片宽度\n  containerHeight: 600, // 容器高度\n  columnsCount: 3, // 列数\n  gap: 20, // 间距\n  overscan: 2, // 预渲染2行\n};\n\n/**\n * 虚拟化画廊视图组件\n */\nexport function VirtualizedGalleryView({\n  records,\n  databaseInfo,\n  options,\n  onRecordClick,\n  virtualScrollConfig = {},\n  className = '',\n  children,\n  ...props\n}: VirtualizedGalleryViewProps) {\n  const config = { ...DEFAULT_VIRTUAL_CONFIG, ...virtualScrollConfig };\n  const [scrollTop, setScrollTop] = useState(0);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  // 计算网格布局\n  const gridLayout = useMemo(() => {\n    const rowsCount = Math.ceil(records.length / config.columnsCount);\n    const totalHeight = rowsCount * (config.itemHeight + config.gap) - config.gap;\n    \n    return {\n      rowsCount,\n      totalHeight,\n      itemsPerRow: config.columnsCount,\n    };\n  }, [records.length, config]);\n\n  // 计算可见范围\n  const visibleRange = useMemo(() => {\n    const startRow = Math.floor(scrollTop / (config.itemHeight + config.gap));\n    const endRow = Math.min(\n      startRow + Math.ceil(config.containerHeight / (config.itemHeight + config.gap)) + config.overscan,\n      gridLayout.rowsCount\n    );\n    \n    return {\n      start: Math.max(0, startRow - config.overscan),\n      end: endRow,\n    };\n  }, [scrollTop, config, gridLayout.rowsCount]);\n\n  // 可见记录\n  const visibleRecords = useMemo(() => {\n    const startIndex = visibleRange.start * config.columnsCount;\n    const endIndex = visibleRange.end * config.columnsCount;\n    return records.slice(startIndex, endIndex);\n  }, [records, visibleRange, config.columnsCount]);\n\n  // 处理滚动\n  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {\n    setScrollTop(e.currentTarget.scrollTop);\n  }, []);\n\n  // 偏移量\n  const offsetY = visibleRange.start * (config.itemHeight + config.gap);\n\n  if (records.length === 0) {\n    return (\n      <div className={`notion-database-view-gallery virtualized ${className}`} {...props}>\n        <div className=\"empty-gallery\">\n          <div className=\"empty-icon\">🖼️</div>\n          <div className=\"empty-message\">暂无数据</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`notion-database-view-gallery virtualized ${className}`} {...props}>\n      {/* 虚拟滚动容器 */}\n      <div\n        ref={containerRef}\n        className=\"virtualized-gallery-container\"\n        style={{ \n          height: config.containerHeight, \n          overflow: 'auto',\n          position: 'relative'\n        }}\n        onScroll={handleScroll}\n      >\n        {/* 总高度占位符 */}\n        <div style={{ height: gridLayout.totalHeight, position: 'relative' }}>\n          {/* 可见项容器 */}\n          <div\n            className=\"notion-database-gallery\"\n            style={{\n              transform: `translateY(${offsetY}px)`,\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              display: 'grid',\n              gridTemplateColumns: `repeat(${config.columnsCount}, 1fr)`,\n              gap: config.gap,\n              padding: config.gap,\n            }}\n          >\n            {visibleRecords.map((record, index) => {\n              const actualIndex = visibleRange.start * config.columnsCount + index;\n              return (\n                <VirtualizedGalleryCard\n                  key={record.id}\n                  record={record}\n                  databaseInfo={databaseInfo}\n                  height={config.itemHeight}\n                  onClick={onRecordClick}\n                  index={actualIndex}\n                  options={options}\n                />\n              );\n            })}\n          </div>\n        </div>\n      </div>\n      {children}\n    </div>\n  );\n}\n\n/**\n * 虚拟化画廊卡片组件\n */\ninterface VirtualizedGalleryCardProps {\n  record: DatabaseRecord;\n  databaseInfo: any;\n  height: number;\n  onClick?: (record: DatabaseRecord) => void;\n  index: number;\n  options?: any;\n}\n\nfunction VirtualizedGalleryCard({\n  record,\n  databaseInfo: _databaseInfo,\n  height,\n  onClick,\n  index,\n  options: _options\n}: VirtualizedGalleryCardProps) {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n\n  const handleClick = useCallback(() => {\n    onClick?.(record);\n  }, [record, onClick]);\n\n  // 获取封面URL\n  const getCoverUrl = useCallback((cover: any): string | null => {\n    if (!cover) return null;\n    \n    if (cover.type === 'file' && cover.file?.url) {\n      return cover.file.url;\n    } else if (cover.type === 'external' && cover.external?.url) {\n      return cover.external.url;\n    }\n    \n    return null;\n  }, []);\n\n  // 获取标题\n  const getTitle = useCallback((rec: DatabaseRecord): string => {\n    for (const [name, value] of Object.entries(rec.properties)) {\n      if (name.toLowerCase().includes('title') || \n          name.toLowerCase().includes('名称') ||\n          name.toLowerCase().includes('name')) {\n        if (value && typeof value === 'object' && 'title' in value && Array.isArray(value.title)) {\n          return value.title.map((item: any) => item.plain_text || '').join('') || '无标题';\n        } else if (typeof value === 'string') {\n          return value || '无标题';\n        }\n      }\n    }\n    return '无标题';\n  }, []);\n\n  const coverUrl = getCoverUrl(record.cover);\n  const title = getTitle(record);\n\n  // 处理图片加载\n  const handleImageLoad = useCallback(() => {\n    setImageLoaded(true);\n    setImageError(false);\n  }, []);\n\n  const handleImageError = useCallback(() => {\n    setImageError(true);\n    setImageLoaded(false);\n  }, []);\n\n  return (\n    <div\n      className={`notion-gallery-card ${onClick ? 'notion-gallery-card-interactive' : ''}`}\n      style={{ height }}\n      onClick={handleClick}\n      data-record-id={record.id}\n      data-index={index}\n    >\n      {/* 封面 */}\n      <div className=\"notion-record-cover\" style={{ height: height * 0.6 }}>\n        {coverUrl ? (\n          <>\n            <img\n              src={coverUrl}\n              alt=\"Record cover\"\n              className={`notion-lazy-image ${imageLoaded ? 'loaded' : ''} ${imageError ? 'error' : ''}`}\n              onLoad={handleImageLoad}\n              onError={handleImageError}\n              loading=\"lazy\"\n              style={{ \n                width: '100%', \n                height: '100%', \n                objectFit: 'cover',\n                opacity: imageLoaded ? 1 : 0,\n                transition: 'opacity 0.3s ease'\n              }}\n            />\n            {!imageLoaded && !imageError && (\n              <div className=\"cover-loading\" style={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                background: 'rgba(255, 255, 255, 0.9)'\n              }}>\n                <div className=\"loading-spinner\" style={{\n                  width: 24,\n                  height: 24,\n                  border: '2px solid #f3f3f3',\n                  borderTop: '2px solid #0073aa',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}></div>\n              </div>\n            )}\n          </>\n        ) : (\n          <div className=\"cover-placeholder\" style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n            height: '100%'\n          }}>\n            <span style={{ fontSize: 32, opacity: 0.5 }}>🖼️</span>\n          </div>\n        )}\n      </div>\n\n      {/* 内容 */}\n      <div className=\"notion-gallery-content\" style={{ \n        padding: 12, \n        height: height * 0.4,\n        overflow: 'hidden'\n      }}>\n        <div className=\"notion-record-title\" style={{\n          fontWeight: 600,\n          fontSize: 14,\n          marginBottom: 8,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden',\n          lineHeight: 1.4\n        }}>\n          {title}\n        </div>\n        \n        {/* 简化的属性显示 */}\n        <div className=\"notion-record-properties\" style={{ fontSize: 12, color: '#666' }}>\n          {Object.entries(record.properties).slice(0, 2).map(([name, value], idx) => {\n            if (name.toLowerCase().includes('title') || \n                name.toLowerCase().includes('名称') ||\n                name.toLowerCase().includes('name')) {\n              return null;\n            }\n            \n            let displayValue = '';\n            if (value && typeof value === 'object' && 'name' in value) {\n              displayValue = value.name;\n            } else if (typeof value === 'string') {\n              displayValue = value;\n            } else if (typeof value === 'number') {\n              displayValue = value.toString();\n            }\n            \n            if (!displayValue) return null;\n            \n            return (\n              <div key={idx} style={{ marginBottom: 2, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>\n                <span style={{ fontWeight: 500 }}>{name}:</span> {displayValue}\n              </div>\n            );\n          })}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default VirtualizedGalleryView;\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\components\\database\\views\\VirtualizedTableView.tsx", "messages": [{"ruleId": "react-hooks/exhaustive-deps", "severity": 1, "message": "The 'config' object makes the dependencies of useMemo Hook (at line 111) change on every render. To fix this, wrap the initialization of 'config' in its own useMemo() Hook.", "line": 44, "column": 9, "nodeType": "VariableDeclarator", "endLine": 44, "endColumn": 71}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 56, "column": 79, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 56, "endColumn": 82, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [1393, 1396], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [1393, 1396], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 1, "message": "Unexpected any. Specify a different type.", "line": 257, "column": 41, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 257, "endColumn": 44, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [7311, 7314], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [7311, 7314], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 虚拟化表格视图React组件\n * \n * 为大数据量场景提供性能优化的表格视图\n */\n\nimport React, { useState, useMemo, useCallback, useRef } from 'react';\nimport type { TableViewProps, DatabaseRecord, PropertyConfig } from '../types';\n\n// 虚拟滚动配置\ninterface VirtualScrollConfig {\n  itemHeight: number;\n  containerHeight: number;\n  overscan: number; // 预渲染的额外行数\n}\n\n// 虚拟化表格Props\ninterface VirtualizedTableViewProps extends TableViewProps {\n  virtualScrollConfig?: Partial<VirtualScrollConfig>;\n}\n\n// 默认虚拟滚动配置\nconst DEFAULT_VIRTUAL_CONFIG: VirtualScrollConfig = {\n  itemHeight: 48, // 每行高度\n  containerHeight: 400, // 容器高度\n  overscan: 5, // 预渲染5行\n};\n\n/**\n * 虚拟化表格视图组件\n */\nexport function VirtualizedTableView({\n  records,\n  databaseInfo,\n  options,\n  sortConfig,\n  onSort,\n  onRecordClick,\n  virtualScrollConfig = {},\n  className = '',\n  children,\n  ...props\n}: VirtualizedTableViewProps) {\n  const config = { ...DEFAULT_VIRTUAL_CONFIG, ...virtualScrollConfig };\n  const [scrollTop, setScrollTop] = useState(0);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  // 获取可见属性（复用TableView的逻辑）\n  const visibleProperties = useMemo(() => {\n    if (!databaseInfo?.properties) return [];\n    \n    const properties: PropertyConfig[] = [];\n    const showProperties = options?.showProperties || [];\n    const hideProperties = options?.hideProperties || [];\n\n    Object.entries(databaseInfo.properties).forEach(([name, config]: [string, any]) => {\n      const shouldShow = showProperties.length === 0 || showProperties.includes(name);\n      const shouldHide = hideProperties.includes(name);\n\n      if (shouldShow && !shouldHide) {\n        properties.push({\n          name,\n          type: config.type || 'text',\n          visible: true,\n          width: getPropertyWidth(config.type),\n          format: getPropertyFormat(config.type),\n        });\n      }\n    });\n\n    return properties;\n  }, [databaseInfo, options]);\n\n  // 排序记录（复用TableView的逻辑）\n  const sortedRecords = useMemo(() => {\n    if (!sortConfig) return records;\n\n    return [...records].sort((a, b) => {\n      const aValue = a.properties[sortConfig.property];\n      const bValue = b.properties[sortConfig.property];\n      \n      if (aValue === null || aValue === undefined) return 1;\n      if (bValue === null || bValue === undefined) return -1;\n      \n      let comparison = 0;\n      \n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        comparison = aValue.localeCompare(bValue);\n      } else if (typeof aValue === 'number' && typeof bValue === 'number') {\n        comparison = aValue - bValue;\n      } else {\n        comparison = String(aValue).localeCompare(String(bValue));\n      }\n      \n      return sortConfig.direction === 'asc' ? comparison : -comparison;\n    });\n  }, [records, sortConfig]);\n\n  // 计算可见范围\n  const visibleRange = useMemo(() => {\n    const startIndex = Math.floor(scrollTop / config.itemHeight);\n    const endIndex = Math.min(\n      startIndex + Math.ceil(config.containerHeight / config.itemHeight) + config.overscan,\n      sortedRecords.length\n    );\n    \n    return {\n      start: Math.max(0, startIndex - config.overscan),\n      end: endIndex,\n    };\n  }, [scrollTop, config, sortedRecords.length]);\n\n  // 可见记录\n  const visibleRecords = useMemo(() => {\n    return sortedRecords.slice(visibleRange.start, visibleRange.end);\n  }, [sortedRecords, visibleRange]);\n\n  // 处理滚动\n  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {\n    setScrollTop(e.currentTarget.scrollTop);\n  }, []);\n\n  // 处理排序\n  const handleSort = useCallback((property: string) => {\n    onSort?.(property);\n  }, [onSort]);\n\n  // 总高度\n  const totalHeight = sortedRecords.length * config.itemHeight;\n\n  // 偏移量\n  const offsetY = visibleRange.start * config.itemHeight;\n\n  if (records.length === 0) {\n    return (\n      <div className={`notion-database-view-table virtualized ${className}`} {...props}>\n        <div className=\"empty-table\">\n          <div className=\"empty-icon\">📋</div>\n          <div className=\"empty-message\">暂无数据</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`notion-database-view-table virtualized ${className}`} {...props}>\n      <div className=\"notion-database-table\">\n        {/* 表头 */}\n        <div className=\"notion-table-header\">\n          {visibleProperties.map((property) => (\n            <div\n              key={property.name}\n              className={`notion-table-header-cell ${sortConfig?.property === property.name ? 'sorted' : ''}`}\n              style={{ width: property.width }}\n              onClick={() => handleSort(property.name)}\n              role=\"button\"\n              tabIndex={0}\n            >\n              <span className=\"header-text\">{property.name}</span>\n              {sortConfig?.property === property.name && (\n                <span className=\"sort-indicator\">\n                  {sortConfig.direction === 'asc' ? '↑' : '↓'}\n                </span>\n              )}\n            </div>\n          ))}\n        </div>\n\n        {/* 虚拟滚动容器 */}\n        <div\n          ref={containerRef}\n          className=\"notion-table-body virtualized-container\"\n          style={{ height: config.containerHeight, overflow: 'auto' }}\n          onScroll={handleScroll}\n        >\n          {/* 总高度占位符 */}\n          <div style={{ height: totalHeight, position: 'relative' }}>\n            {/* 可见行容器 */}\n            <div\n              style={{\n                transform: `translateY(${offsetY}px)`,\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n              }}\n            >\n              {visibleRecords.map((record, index) => {\n                const actualIndex = visibleRange.start + index;\n                return (\n                  <VirtualizedTableRow\n                    key={record.id}\n                    record={record}\n                    properties={visibleProperties}\n                    height={config.itemHeight}\n                    onClick={onRecordClick}\n                    index={actualIndex}\n                  />\n                );\n              })}\n            </div>\n          </div>\n        </div>\n      </div>\n      {children}\n    </div>\n  );\n}\n\n/**\n * 虚拟化表格行组件\n */\ninterface VirtualizedTableRowProps {\n  record: DatabaseRecord;\n  properties: PropertyConfig[];\n  height: number;\n  onClick?: (record: DatabaseRecord) => void;\n  index: number;\n}\n\nfunction VirtualizedTableRow({ record, properties, height, onClick, index }: VirtualizedTableRowProps) {\n  const handleClick = useCallback(() => {\n    onClick?.(record);\n  }, [record, onClick]);\n\n  return (\n    <div\n      className={`notion-table-row ${onClick ? 'notion-table-row-interactive' : ''}`}\n      style={{ height, display: 'flex', alignItems: 'center' }}\n      onClick={handleClick}\n      data-record-id={record.id}\n      data-index={index}\n    >\n      {properties.map((property) => (\n        <VirtualizedTableCell\n          key={property.name}\n          record={record}\n          property={property}\n        />\n      ))}\n    </div>\n  );\n}\n\n/**\n * 虚拟化表格单元格组件\n */\ninterface VirtualizedTableCellProps {\n  record: DatabaseRecord;\n  property: PropertyConfig;\n}\n\nfunction VirtualizedTableCell({ record, property }: VirtualizedTableCellProps) {\n  const value = record.properties[property.name];\n  \n  // 简化的格式化函数（性能优化）\n  const formatValue = useCallback((val: any): string => {\n    if (val === null || val === undefined) return '';\n    \n    switch (property.type) {\n      case 'title':\n        return val?.title?.[0]?.plain_text || String(val);\n      case 'number':\n        return typeof val === 'number' ? val.toLocaleString() : String(val);\n      case 'checkbox':\n        return val ? '✓' : '✗';\n      case 'date':\n        return val?.start ? new Date(val.start).toLocaleDateString() : String(val);\n      default:\n        return String(val);\n    }\n  }, [property.type]);\n\n  const formattedValue = formatValue(value);\n\n  return (\n    <div \n      className=\"notion-table-cell\"\n      style={{ width: property.width }}\n      title={formattedValue}\n    >\n      <span className={`cell-content cell-type-${property.type}`}>\n        {formattedValue}\n      </span>\n    </div>\n  );\n}\n\n// 辅助函数（复用TableView的逻辑）\nfunction getPropertyWidth(type: string): string {\n  switch (type) {\n    case 'checkbox': return '60px';\n    case 'number': return '100px';\n    case 'date': return '120px';\n    case 'select': return '150px';\n    case 'title': return '200px';\n    default: return '150px';\n  }\n}\n\nfunction getPropertyFormat(type: string): string {\n  return type;\n}\n\nexport default VirtualizedTableView;\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\context\\AppContext.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\admin\\react\\hooks\\useFrontendFeatures.test.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}, {"filePath": "C:\\Users\\<USER>\\Local Sites\\frankloong\\app\\public\\wp-content\\plugins\\Notion-to-WordPress\\src\\shared\\utils\\__tests__\\utils.test.ts", "messages": [{"ruleId": null, "fatal": true, "severity": 2, "message": "Parsing error: \"parserOptions.project\" has been provided for @typescript-eslint/parser.\nThe file was not found in any of the provided project(s): src\\shared\\utils\\__tests__\\utils.test.ts", "nodeType": null}], "suppressedMessages": [], "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "/**\n * 工具函数测试\n */\n\ndescribe('Utils', () => {\n  test('should pass basic test', () => {\n    expect(true).toBe(true);\n  });\n\n  test('should handle string operations', () => {\n    const testString = 'Hello World';\n    expect(testString.toLowerCase()).toBe('hello world');\n    expect(testString.toUpperCase()).toBe('HELLO WORLD');\n  });\n\n  test('should handle array operations', () => {\n    const testArray = [1, 2, 3, 4, 5];\n    expect(testArray.length).toBe(5);\n    expect(testArray.includes(3)).toBe(true);\n    expect(testArray.includes(6)).toBe(false);\n  });\n\n  test('should handle object operations', () => {\n    const testObject = { name: 'Test', value: 42 };\n    expect(testObject.name).toBe('Test');\n    expect(testObject.value).toBe(42);\n    expect(Object.keys(testObject)).toEqual(['name', 'value']);\n  });\n});\n", "usedDeprecatedRules": [{"ruleId": "no-extra-semi", "replacedBy": []}, {"ruleId": "no-mixed-spaces-and-tabs", "replacedBy": []}]}]