/**
 * 渐进式加载Hook
 * 
 * 封装ProgressiveLoader功能，提供React化的渐进式加载接口
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { ProgressiveLoader } from '../../../frontend/components/ProgressiveLoader';
import { on, off } from '../../../shared/core/EventBus';

// Hook状态接口
export interface ProgressiveLoaderState {
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  totalLoaded: number;
}

// Hook配置选项
export interface UseProgressiveLoaderOptions {
  enabled?: boolean;
  autoInit?: boolean;
  batchSize?: number;
  retryAttempts?: number;
  retryDelay?: number;
  loadingDelay?: number;
}

// Hook返回值接口
export interface UseProgressiveLoaderReturn {
  // 状态
  state: ProgressiveLoaderState;
  isLoading: boolean;
  hasError: boolean;

  // 操作方法
  loadMore: (button: HTMLButtonElement) => Promise<void>;
  destroy: () => void;

  // 事件处理
  onLoadStart: (callback: (event: any) => void) => () => void;
  onLoadSuccess: (callback: (event: any) => void) => () => void;
  onLoadError: (callback: (event: any) => void) => () => void;
}

/**
 * 渐进式加载Hook
 */
export function useProgressiveLoader(
  options: UseProgressiveLoaderOptions = {}
): UseProgressiveLoaderReturn {
  const {
    enabled = true,
    autoInit = true,
  } = options;

  // 状态管理
  const [state, setState] = useState<ProgressiveLoaderState>({
    isLoading: false,
    error: null,
    hasMore: true,
    totalLoaded: 0,
  });

  // 渐进式加载器实例引用
  const loaderRef = useRef<ProgressiveLoader | null>(null);
  const mountedRef = useRef(true);

  // 安全状态更新
  const safeSetState = useCallback((updater: (prev: ProgressiveLoaderState) => ProgressiveLoaderState) => {
    if (mountedRef.current) {
      setState(updater);
    }
  }, []);

  // 初始化渐进式加载器
  const initLoader = useCallback(() => {
    if (!enabled) return;

    try {
      loaderRef.current = ProgressiveLoader.getInstance();
      safeSetState(prev => ({ ...prev, error: null }));
    } catch (error) {
      console.error('📦 [useProgressiveLoader] 初始化失败:', error);
      safeSetState(prev => ({ ...prev, error: '初始化失败' }));
    }
  }, [enabled, safeSetState]);

  // 加载更多数据
  const loadMore = useCallback(async (button: HTMLButtonElement): Promise<void> => {
    if (!loaderRef.current) return;

    safeSetState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      await loaderRef.current.loadMore(button);
      safeSetState(prev => ({ ...prev, isLoading: false }));
    } catch (error) {
      console.error('📦 [useProgressiveLoader] 加载更多失败:', error);
      safeSetState(prev => ({ ...prev, isLoading: false, error: (error as Error).message }));
    }
  }, [safeSetState]);

  // 销毁渐进式加载器
  const destroy = useCallback(() => {
    if (loaderRef.current) {
      loaderRef.current.destroy();
      loaderRef.current = null;
    }
  }, []);

  // 监听渐进式加载事件
  useEffect(() => {
    if (!enabled) return;

    const handleLoadStart = () => {
      safeSetState(prev => ({ ...prev, isLoading: true, error: null }));
    };

    const handleLoadSuccess = (event: any) => {
      const { data } = event.detail || event;
      safeSetState(prev => ({
        ...prev,
        isLoading: false,
        hasMore: data?.hasMore ?? true,
        totalLoaded: prev.totalLoaded + (data?.records?.length ?? 0),
      }));
    };

    const handleLoadError = (event: any) => {
      const { error } = event.detail || event;
      safeSetState(prev => ({
        ...prev,
        isLoading: false,
        error: error?.message || '加载失败',
      }));
    };

    on('progressive:load:start', handleLoadStart);
    on('progressive:load:success', handleLoadSuccess);
    on('progressive:load:error', handleLoadError);

    return () => {
      off('progressive:load:start', handleLoadStart);
      off('progressive:load:success', handleLoadSuccess);
      off('progressive:load:error', handleLoadError);
    };
  }, [enabled, safeSetState]);

  // 自动初始化
  useEffect(() => {
    if (autoInit && enabled) {
      initLoader();
    }
  }, [autoInit, enabled, initLoader]);

  // 组件卸载清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // 状态查询
  const isLoading = state.isLoading;
  const hasError = !!state.error;

  return {
    state,
    isLoading,
    hasError,
    loadMore,
    destroy,
    onLoadStart: (callback: (event: any) => void) => {
      on('progressive:load:start', callback);
      return () => off('progressive:load:start', callback);
    },
    onLoadSuccess: (callback: (event: any) => void) => {
      on('progressive:load:success', callback);
      return () => off('progressive:load:success', callback);
    },
    onLoadError: (callback: (event: any) => void) => {
      on('progressive:load:error', callback);
      return () => off('progressive:load:error', callback);
    },
  };
}

/**
 * 加载更多按钮Hook
 */
export function useLoadMoreButton(options: UseProgressiveLoaderOptions = {}) {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const { loadMore, isLoading } = useProgressiveLoader(options);

  // 处理按钮点击
  const handleClick = useCallback(async () => {
    const button = buttonRef.current;
    if (!button || isLoading) return;

    await loadMore(button);
  }, [loadMore, isLoading]);

  return {
    buttonRef,
    handleClick,
    isLoading,
    disabled: isLoading,
  };
}

/**
 * 无限滚动Hook
 */
export function useInfiniteScroll(options: UseProgressiveLoaderOptions = {}) {
  const containerRef = useRef<HTMLElement>(null);
  const { loadMore, isLoading, hasError } = useProgressiveLoader(options);

  // 滚动处理
  const handleScroll = useCallback(() => {
    const container = containerRef.current;
    if (!container || isLoading || hasError) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const threshold = 100; // 距离底部100px时触发

    if (scrollTop + clientHeight >= scrollHeight - threshold) {
      // 查找加载更多按钮
      const button = container.querySelector('.notion-load-more-button') as HTMLButtonElement;
      if (button) {
        loadMore(button);
      }
    }
  }, [loadMore, isLoading, hasError]);

  // 设置滚动监听
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  return {
    containerRef,
    isLoading,
    hasError,
  };
}
