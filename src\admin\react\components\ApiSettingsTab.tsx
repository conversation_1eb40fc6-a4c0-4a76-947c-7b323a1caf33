/**
 * API设置标签页组件
 */

import { useState } from 'react';
import { useSettings, useStats, useSyncStatus, useAppActions } from '../context/AppContext';
import { useWordPressSettings, useSyncOperations, useI18n } from '../hooks/useWordPress';
import { Input, PasswordInput, Select, Checkbox, FormRow } from './Input';
import { Button, SyncButton } from './Button';
import { WebhookSettings } from './WebhookSettings';
import { QuickConfig } from './QuickConfig';

export function ApiSettingsTab() {
  const settings = useSettings();
  const stats = useStats();
  const syncStatus = useSyncStatus();
  const { updateSettings, showToast } = useAppActions();
  const { testConnection } = useWordPressSettings();
  const { startSync } = useSyncOperations();
  const { __ } = useI18n();

  const [isTestingConnection, setIsTestingConnection] = useState(false);

  // 处理设置更新
  const handleSettingChange = (key: string, value: string | boolean | number) => {
    updateSettings({ [key]: value });
  };

  // 测试连接
  const handleTestConnection = async () => {
    if (!settings.notion_api_key || !settings.notion_database_id) {
      showToast({
        type: 'warning',
        message: '请先填写API密钥和数据库ID',
      });
      return;
    }

    setIsTestingConnection(true);
    try {
      const response = await testConnection(settings.notion_api_key, settings.notion_database_id);
      
      if (response.success) {
        showToast({
          type: 'success',
          message: '连接测试成功！',
        });
      } else {
        showToast({
          type: 'error',
          message: response.message || '连接测试失败',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: '连接测试失败：' + (error instanceof Error ? error.message : '未知错误'),
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  // 开始同步
  const handleSync = async (type: 'manual' | 'full') => {
    try {
      const response = await startSync(type);
      
      if (response.success) {
        showToast({
          type: 'success',
          message: `${type === 'manual' ? '智能' : '完全'}同步已开始`,
        });
      } else {
        showToast({
          type: 'error',
          message: response.message || '同步启动失败',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: '同步启动失败：' + (error instanceof Error ? error.message : '未知错误'),
      });
    }
  };

  // 同步计划选项
  const scheduleOptions = [
    { value: 'manual', label: __('手动同步', '手动同步') },
    { value: 'twicedaily', label: __('每天两次', '每天两次') },
    { value: 'daily', label: __('每天一次', '每天一次') },
    { value: 'weekly', label: __('每周一次', '每周一次') },
    { value: 'biweekly', label: __('每两周一次', '每两周一次') },
    { value: 'monthly', label: __('每月一次', '每月一次') },
  ];

  return (
    <div className="notion-wp-settings-section">
      <h2>{__('Notion API 设置', 'Notion API 设置')}</h2>
      
      {/* 统计数据网格 */}
      <div className="notion-stats-grid">
        <div className="stat-card">
          <h3 className="stat-imported-count">{stats.importedCount}</h3>
          <span>{__('已导入页面', '已导入页面')}</span>
        </div>
        <div className="stat-card">
          <h3 className="stat-published-count">{stats.publishedCount}</h3>
          <span>{__('已发布页面', '已发布页面')}</span>
        </div>
        <div className="stat-card">
          <h3 className="stat-last-update">{stats.lastUpdate}</h3>
          <span>{__('最后同步', '最后同步')}</span>
        </div>
        <div className="stat-card">
          <h3 className="stat-next-run">{stats.nextRun}</h3>
          <span>{__('下次同步', '下次同步')}</span>
        </div>
      </div>
      
      <p className="description">
        {__('连接到您的Notion数据库所需的设置。', '连接到您的Notion数据库所需的设置。')}
        <a href="https://developers.notion.com/docs/getting-started" target="_blank">
          {__('了解如何获取API密钥', '了解如何获取API密钥')}
        </a>
      </p>
      
      <table className="form-table">
        <tbody>
          <FormRow
            label={__('API密钥', 'API密钥')}
            required
            description={__('在Notion的"我的集成"页面创建并获取API密钥。', '在Notion的"我的集成"页面创建并获取API密钥。')}
          >
            <PasswordInput
              value={settings.notion_api_key}
              placeholder={__('输入您的Notion API密钥', '输入您的Notion API密钥')}
              validation="api-key"
              onChange={(value) => handleSettingChange('notion_api_key', value)}
            />
          </FormRow>

          <FormRow
            label={__('数据库ID', '数据库ID')}
            required
            description={__('可以从Notion数据库URL中找到，格式如：https://www.notion.so/xxx/数据库ID?v=xxx', '可以从Notion数据库URL中找到，格式如：https://www.notion.so/xxx/数据库ID?v=xxx')}
          >
            <Input
              value={settings.notion_database_id}
              placeholder={__('输入您的Notion数据库ID', '输入您的Notion数据库ID')}
              validation="database-id"
              onChange={(value) => handleSettingChange('notion_database_id', value)}
            />
          </FormRow>

          <FormRow
            label={__('自动同步频率', '自动同步频率')}
            description={__('选择 "手动同步" 以禁用定时任务。', '选择 "手动同步" 以禁用定时任务。')}
          >
            <Select
              value={settings.sync_schedule}
              options={scheduleOptions}
              onChange={(value) => handleSettingChange('sync_schedule', value)}
            />
          </FormRow>

          <FormRow label={__('定时同步选项', '定时同步选项')}>
            <div className="checkbox-group">
              <Checkbox
                checked={settings.cron_incremental_sync}
                label={__('启用增量同步', '启用增量同步')}
                description={__('仅同步有变化的页面，提高同步速度', '仅同步有变化的页面，提高同步速度')}
                onChange={(checked) => handleSettingChange('cron_incremental_sync', checked)}
              />
              
              <Checkbox
                checked={settings.cron_check_deletions}
                label={__('检查删除的页面', '检查删除的页面')}
                description={__('自动删除在Notion中已删除但WordPress中仍存在的文章', '自动删除在Notion中已删除但WordPress中仍存在的文章')}
                onChange={(checked) => handleSettingChange('cron_check_deletions', checked)}
              />
            </div>
          </FormRow>
        </tbody>
      </table>

      {/* 测试连接按钮 */}
      <div className="notion-wp-button-row">
        <Button
          variant="secondary"
          loading={isTestingConnection}
          onClick={handleTestConnection}
        >
          <span className="dashicons dashicons-admin-network" style={{ marginRight: '4px' }} />
          {__('测试连接', '测试连接')}
        </Button>
      </div>

      {/* Webhook设置 */}
      <WebhookSettings />

      {/* 同步操作 */}
      <div className="notion-wp-sync-actions">
        <h3>{__('同步操作', '同步操作')}</h3>
        <div className="sync-buttons">
          <SyncButton
            syncType="manual"
            isRunning={syncStatus.isRunning && syncStatus.type === 'manual'}
            onSync={handleSync}
          >
            {__('智能同步', '智能同步')}
          </SyncButton>

          <SyncButton
            syncType="full"
            isRunning={syncStatus.isRunning && syncStatus.type === 'full'}
            onSync={handleSync}
          >
            {__('完全同步', '完全同步')}
          </SyncButton>
        </div>

        {/* 同步进度 */}
        {syncStatus.isRunning && (
          <div className="sync-progress" id="sync-progress">
            <div className="progress-bar">
              <div
                className="progress-fill"
                style={{ width: `${syncStatus.progress}%` }}
              />
            </div>
            <div className="progress-text">
              <span className="current-step">{syncStatus.currentStep}</span>
              <span className="progress-percentage">{syncStatus.progress}%</span>
            </div>
          </div>
        )}

        <div className="sync-info">
          <p>
            <strong>{__('智能同步', '智能同步')}</strong>: {__('只同步有变化的页面，速度更快', '只同步有变化的页面，速度更快')}
          </p>
          <p>
            <strong>{__('完全同步', '完全同步')}</strong>: {__('同步所有页面，确保数据一致性', '同步所有页面，确保数据一致性')}
          </p>
        </div>
      </div>

      {/* 快速配置 */}
      <QuickConfig />
    </div>
  );
}
