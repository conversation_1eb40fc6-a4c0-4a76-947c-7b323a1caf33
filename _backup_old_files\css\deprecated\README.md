# 废弃的CSS文件

⚠️ **此目录包含已被SCSS版本完全替代的原版CSS文件**

## 文件迁移映射

| 原版CSS文件 | SCSS替代版本 | 状态 |
|-----------|-------------|------|
| `katex-fonts.css` | `src/styles/frontend/math-renderer.scss` | ✅ 已完全替代 |
| `latex-styles.css` | `src/styles/frontend/math-renderer.scss` | ✅ 已完全替代 |
| `tooltip.css` | `src/styles/shared/tooltip.scss` | ✅ 已完全替代 |

## SCSS版本的主要改进

### 🎯 **现代化CSS架构**
- 使用SCSS变量和混入，提高可维护性
- 模块化设计，按功能组织样式
- 统一的设计系统和变量管理
- 更好的代码复用和扩展性

### 📱 **响应式设计增强**
- 完整的移动端适配
- 灵活的断点管理
- 优化的触摸交互
- 更好的可访问性支持

### ⚡ **性能优化**
- CSS变量的动态主题切换
- 优化的选择器和层叠
- 减少重复代码
- 更小的构建体积

### 🎨 **设计系统集成**
- 统一的颜色系统
- 一致的间距和字体
- 标准化的组件样式
- 主题变体支持

### 🔧 **开发体验**
- TypeScript样式类型定义
- 更好的IDE支持和自动补全
- 实时编译和热重载
- 清晰的样式组织结构

## 构建配置

这些废弃CSS文件已从构建配置中移除：

- **Webpack**: 只处理SCSS文件，自动编译为CSS
- **AdminController.php**: 只加载SCSS构建的CSS文件
- **版本管理**: 不再更新这些文件的版本号

## 开发者指南

### 🚀 **新项目开发**
请直接使用SCSS版本，位于：
- `src/styles/admin/` - 管理界面样式
- `src/styles/frontend/` - 前端样式
- `src/styles/shared/` - 共享样式和变量

### 🔄 **迁移现有样式**
如果有自定义CSS依赖这些原版文件：
1. 查看对应的SCSS版本
2. 使用新的CSS类名和变量
3. 参考设计系统的标准化样式

### 📚 **样式指南**
- [设计系统文档](../../../docs/DESIGN_SYSTEM.md)
- [SCSS开发指南](../../../docs/SCSS_GUIDE.md)
- [响应式设计规范](../../../docs/RESPONSIVE_DESIGN.md)

## 清理计划

这些文件将在未来版本中完全移除：
- **v2.1.0**: 标记为废弃，移动到此目录
- **v2.2.0**: 完全移除，清理构建配置
- **v3.0.0**: 移除CSS兼容性层

---

**最后更新**: 2025-08-05  
**维护者**: Frank-Loong  
**状态**: 已废弃，请使用SCSS版本