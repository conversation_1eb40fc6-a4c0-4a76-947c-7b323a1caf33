/**
 * KaTeX字体样式定义
 * 为数学公式提供完整的字体支持
 *
 * @since 2.0.0-beta.1
 * @package Notion_To_WordPress
 */

/* 精简版KaTeX字体定义 - 优先使用本地字体，CDN作为备用 */
@font-face {
    font-family: KaTeX_Main;
    font-style: normal;
    font-weight: 400;
    src: url('../vendor/katex/fonts/KaTeX_Main-Regular.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Main-Regular.woff2') format('woff2'),
         local('Times New Roman'), local('serif');
}

@font-face {
    font-family: KaTeX_Math;
    font-style: italic;
    font-weight: 400;
    src: url('../vendor/katex/fonts/KaTeX_Math-Italic.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Math-Italic.woff2') format('woff2'),
         local('Times New Roman'), local('serif');
}

@font-face {
    font-family: KaTeX_Size2;
    font-style: normal;
    font-weight: 400;
    src: url('../vendor/katex/fonts/KaTeX_Size2-Regular.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Size2-Regular.woff2') format('woff2'),
         local('Times New Roman'), local('serif');
}

/* 100%公式支持的额外字体 */
@font-face {
    font-family: KaTeX_AMS;
    font-style: normal;
    font-weight: 400;
    src: url('../vendor/katex/fonts/KaTeX_AMS-Regular.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_AMS-Regular.woff2') format('woff2'),
         local('serif');
}

@font-face {
    font-family: KaTeX_Size1;
    font-style: normal;
    font-weight: 400;
    src: url('../vendor/katex/fonts/KaTeX_Size1-Regular.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Size1-Regular.woff2') format('woff2'),
         local('serif');
}

@font-face {
    font-family: KaTeX_Size3;
    font-style: normal;
    font-weight: 400;
    src: url('../vendor/katex/fonts/KaTeX_Size3-Regular.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Size3-Regular.woff2') format('woff2'),
         local('serif');
}

@font-face {
    font-family: KaTeX_Size4;
    font-style: normal;
    font-weight: 400;
    src: url('../vendor/katex/fonts/KaTeX_Size4-Regular.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Size4-Regular.woff2') format('woff2'),
         local('serif');
}

@font-face {
    font-family: KaTeX_Main;
    font-style: normal;
    font-weight: 700;
    src: url('../vendor/katex/fonts/KaTeX_Main-Bold.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Main-Bold.woff2') format('woff2'),
         local('serif');
}

@font-face {
    font-family: KaTeX_Main;
    font-style: italic;
    font-weight: 400;
    src: url('../vendor/katex/fonts/KaTeX_Main-Italic.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Main-Italic.woff2') format('woff2'),
         local('serif');
}

@font-face {
    font-family: KaTeX_Caligraphic;
    font-style: normal;
    font-weight: 400;
    src: url('../vendor/katex/fonts/KaTeX_Caligraphic-Regular.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Caligraphic-Regular.woff2') format('woff2'),
         local('serif');
}

@font-face {
    font-family: KaTeX_Script;
    font-style: normal;
    font-weight: 400;
    src: url('../vendor/katex/fonts/KaTeX_Script-Regular.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Script-Regular.woff2') format('woff2'),
         local('serif');
}

@font-face {
    font-family: KaTeX_Typewriter;
    font-style: normal;
    font-weight: 400;
    src: url('../vendor/katex/fonts/KaTeX_Typewriter-Regular.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Typewriter-Regular.woff2') format('woff2'),
         local('monospace');
}

/* 额外的高级字体支持 */
@font-face {
    font-family: KaTeX_Caligraphic;
    font-style: normal;
    font-weight: 700;
    src: url('../vendor/katex/fonts/KaTeX_Caligraphic-Bold.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Caligraphic-Bold.woff2') format('woff2'),
         local('serif');
}

@font-face {
    font-family: KaTeX_Fraktur;
    font-style: normal;
    font-weight: 400;
    src: url('../vendor/katex/fonts/KaTeX_Fraktur-Regular.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Fraktur-Regular.woff2') format('woff2'),
         local('serif');
}

@font-face {
    font-family: KaTeX_Fraktur;
    font-style: normal;
    font-weight: 700;
    src: url('../vendor/katex/fonts/KaTeX_Fraktur-Bold.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Fraktur-Bold.woff2') format('woff2'),
         local('serif');
}

@font-face {
    font-family: KaTeX_Main;
    font-style: italic;
    font-weight: 700;
    src: url('../vendor/katex/fonts/KaTeX_Main-BoldItalic.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Main-BoldItalic.woff2') format('woff2'),
         local('serif');
}

@font-face {
    font-family: KaTeX_Math;
    font-style: italic;
    font-weight: 700;
    src: url('../vendor/katex/fonts/KaTeX_Math-BoldItalic.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Math-BoldItalic.woff2') format('woff2'),
         local('serif');
}

@font-face {
    font-family: KaTeX_SansSerif;
    font-style: normal;
    font-weight: 400;
    src: url('../vendor/katex/fonts/KaTeX_SansSerif-Regular.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_SansSerif-Regular.woff2') format('woff2'),
         local('sans-serif');
}

@font-face {
    font-family: KaTeX_SansSerif;
    font-style: normal;
    font-weight: 700;
    src: url('../vendor/katex/fonts/KaTeX_SansSerif-Bold.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_SansSerif-Bold.woff2') format('woff2'),
         local('sans-serif');
}

@font-face {
    font-family: KaTeX_SansSerif;
    font-style: italic;
    font-weight: 400;
    src: url('../vendor/katex/fonts/KaTeX_SansSerif-Italic.woff2') format('woff2'),
         url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_SansSerif-Italic.woff2') format('woff2'),
         local('sans-serif');
}

/* 为其他KaTeX字体提供备用方案 */
.katex {
    font-family: KaTeX_Main, 'Times New Roman', serif !important;
}

.katex .mathnormal {
    font-family: KaTeX_Math, 'Times New Roman', serif !important;
}

.katex .delimsizing.size2 {
    font-family: KaTeX_Size2, 'Times New Roman', serif !important;
}

/* 如果CDN字体加载失败，使用系统字体 */
.katex .mathit,
.katex .mathrm,
.katex .mathbf {
    font-family: 'Times New Roman', serif !important;
}

/* 确保数学公式在字体加载失败时仍然可读 */
.notion-equation-inline,
.notion-equation-block {
    font-family: 'Times New Roman', serif;
    font-size: 1.1em;
    line-height: 1.4;
}

/* 字体加载失败时的备用样式 */
.katex-error {
    color: #cc0000;
    font-family: monospace;
    background-color: #fff2f2;
    padding: 2px 4px;
    border-radius: 3px;
    border: 1px solid #ffcccc;
}

/* 提高数学公式的可读性 */
.katex-display {
    margin: 1em 0;
    text-align: center;
}

.katex-display > .katex {
    display: block;
    text-align: center;
    white-space: nowrap;
}

/* 确保内联公式与文本对齐 */
.notion-equation-inline .katex {
    display: inline-block;
    vertical-align: baseline;
}

/* 块级公式的样式 */
.notion-equation-block .katex {
    display: block;
    text-align: center;
    margin: 0.5em 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .katex {
        font-size: 0.9em;
    }
    
    .katex-display {
        margin: 0.5em 0;
    }
}

/* 打印样式 */
@media print {
    .katex {
        font-family: 'Times New Roman', serif !important;
        color: black !important;
    }
}
