/**
 * 数据库记录管理Hook
 * 
 * 提供React化的数据库记录管理接口
 */

import { useState, useCallback, useRef } from 'react';
import { useWordPressAjax } from './useWordPress';
import type { 
  DatabaseRecord, 
  DatabaseInfo, 
  DatabaseFilter, 
  DatabaseSort,
  DatabaseQuery
} from '../types';

// Hook专用状态接口
export interface DatabaseRecordsState {
  records: DatabaseRecord[];
  databaseInfo: DatabaseInfo | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
  nextCursor?: string;
}

// Hook配置选项
export interface UseDatabaseRecordsOptions {
  pageSize?: number;
  enableSearch?: boolean;
  enableFilter?: boolean;
  enableSort?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
  initialLoad?: boolean;
}

// Hook返回值接口
export interface UseDatabaseRecordsReturn {
  // 状态
  state: DatabaseRecordsState;
  
  // 操作方法
  loadRecords: (query?: Partial<DatabaseQuery>) => Promise<DatabaseRecord[]>;
  loadMoreRecords: () => Promise<DatabaseRecord[]>;
  searchRecords: (searchTerm: string, searchProperty?: string) => Promise<DatabaseRecord[]>;
  filterRecords: (filters: DatabaseFilter[]) => Promise<DatabaseRecord[]>;
  sortRecords: (sorts: DatabaseSort[]) => Promise<DatabaseRecord[]>;
  refreshRecords: () => Promise<DatabaseRecord[]>;
  clearRecords: () => void;
  
  // 状态查询
  isLoading: boolean;
  hasError: boolean;
  isEmpty: boolean;
  canLoadMore: boolean;
}

/**
 * 数据库记录管理Hook
 */
export function useDatabaseRecords(
  databaseId: string,
  options: UseDatabaseRecordsOptions = {}
): UseDatabaseRecordsReturn {
  const {
    pageSize = 20,
  } = options;

  // 状态管理
  const [state, setState] = useState<DatabaseRecordsState>({
    records: [],
    databaseInfo: null,
    loading: false,
    error: null,
    totalCount: 0,
    hasMore: false,
    currentPage: 1,
    nextCursor: undefined,
  });

  // 状态管理引用
  const mountedRef = useRef(true);
  const { request } = useWordPressAjax();

  // 安全状态更新
  const updateState = useCallback((updater: (prev: DatabaseRecordsState) => DatabaseRecordsState) => {
    if (mountedRef.current) {
      setState(updater);
    }
  }, []);

  // 加载记录
  const loadRecords = useCallback(async (query?: Partial<DatabaseQuery>): Promise<DatabaseRecord[]> => {
    if (!databaseId) return [];

    updateState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await request('get_database_records', {
        database_id: databaseId,
        page_size: pageSize,
        ...query,
      });

      if (response.success && response.data) {
        const { records, has_more, next_cursor, total_count } = response.data;
        
        updateState(prev => ({
          ...prev,
          records,
          hasMore: has_more || false,
          nextCursor: next_cursor,
          totalCount: total_count || records.length,
          loading: false,
        }));

        return records;
      } else {
        throw new Error(response.message || '加载记录失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      updateState(prev => ({ ...prev, loading: false, error: errorMessage }));
      return [];
    }
  }, [databaseId, pageSize, request, updateState]);

  // 加载更多记录
  const loadMoreRecords = useCallback(async (): Promise<DatabaseRecord[]> => {
    if (!state.hasMore || state.loading) return [];

    updateState(prev => ({ ...prev, loading: true }));

    try {
      const response = await request('get_database_records', {
        database_id: databaseId,
        page_size: pageSize,
        start_cursor: state.nextCursor,
      });

      if (response.success && response.data) {
        const { records, has_more, next_cursor } = response.data;
        
        updateState(prev => ({
          ...prev,
          records: [...prev.records, ...records],
          hasMore: has_more || false,
          nextCursor: next_cursor,
          currentPage: prev.currentPage + 1,
          loading: false,
        }));

        return records;
      } else {
        throw new Error(response.message || '加载更多记录失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      updateState(prev => ({ ...prev, loading: false, error: errorMessage }));
      return [];
    }
  }, [databaseId, pageSize, state.hasMore, state.loading, state.nextCursor, request, updateState]);

  // 搜索记录
  const searchRecords = useCallback(async (searchTerm: string, searchProperty?: string): Promise<DatabaseRecord[]> => {
    const query: Partial<DatabaseQuery> = {
      filter: {
        or: [
          {
            property: searchProperty || 'title',
            condition: 'contains',
            value: searchTerm,
          }
        ]
      }
    };

    return loadRecords(query);
  }, [loadRecords]);

  // 过滤记录
  const filterRecords = useCallback(async (filters: DatabaseFilter[]): Promise<DatabaseRecord[]> => {
    const query: Partial<DatabaseQuery> = {
      filter: {
        and: filters
      }
    };

    return loadRecords(query);
  }, [loadRecords]);

  // 排序记录
  const sortRecords = useCallback(async (sorts: DatabaseSort[]): Promise<DatabaseRecord[]> => {
    const query: Partial<DatabaseQuery> = {
      sorts
    };

    return loadRecords(query);
  }, [loadRecords]);

  // 刷新记录
  const refreshRecords = useCallback(async (): Promise<DatabaseRecord[]> => {
    updateState(prev => ({ ...prev, currentPage: 1, nextCursor: undefined }));
    return loadRecords();
  }, [loadRecords, updateState]);

  // 清空记录
  const clearRecords = useCallback(() => {
    updateState(prev => ({
      ...prev,
      records: [],
      totalCount: 0,
      hasMore: false,
      currentPage: 1,
      nextCursor: undefined,
      error: null,
    }));
  }, [updateState]);

  // 状态查询
  const isLoading = state.loading;
  const hasError = !!state.error;
  const isEmpty = state.records.length === 0;
  const canLoadMore = state.hasMore && !state.loading;

  return {
    state,
    loadRecords,
    loadMoreRecords,
    searchRecords,
    filterRecords,
    sortRecords,
    refreshRecords,
    clearRecords,
    isLoading,
    hasError,
    isEmpty,
    canLoadMore,
  };
}
