/**
 * 数据库画廊视图增强样式
 * 
 * 为React画廊组件提供额外的交互和视觉增强
 */

// 画廊视图增强
.notion-database-view-gallery {
  .notion-database-gallery {
    // 响应式网格优化
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    
    // 卡片间距优化
    gap: 20px;
    padding: 20px;

    .notion-gallery-card {
      // 增强的悬停效果
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      
      &.notion-gallery-card-interactive {
        &:hover,
        &.hovered {
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          transform: translateY(-4px);
          border-color: var(--database-primary-color);
        }

        // 键盘导航支持
        &:focus {
          outline: 2px solid var(--database-primary-color);
          outline-offset: 2px;
        }
      }

      .notion-record-cover {
        position: relative;
        background: var(--database-secondary-color);

        // 无封面占位符
        &.no-cover {
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

          .cover-placeholder {
            .placeholder-icon {
              font-size: 48px;
              opacity: 0.5;
            }
          }
        }

        // 图片加载状态
        .notion-lazy-image {
          transition: opacity 0.3s ease;
          
          &:not(.loaded) {
            opacity: 0;
          }

          &.loaded {
            opacity: 1;
          }

          &.error {
            display: none;
          }
        }

        // 加载中状态
        .cover-loading {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 0.9);

          .loading-spinner {
            width: 24px;
            height: 24px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--database-primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
        }

        // 错误状态
        .cover-error {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: rgba(248, 249, 250, 0.95);
          color: #666;

          .error-icon {
            font-size: 24px;
            margin-bottom: 8px;
          }

          .error-text {
            font-size: 12px;
          }
        }

        // 悬停时的图片效果
        img {
          transition: transform 0.3s ease;
        }

        &:hover img {
          transform: scale(1.05);
        }
      }

      .notion-gallery-content {
        .notion-record-title {
          // 标题截断
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          line-height: 1.4;
          height: 2.8em; // 2行高度
        }

        .notion-record-properties {
          .notion-record-property {
            // 属性值样式增强
            .notion-property-value {
              &.property-type-select {
                display: inline-block;
                padding: 2px 8px;
                background: var(--database-secondary-color);
                border-radius: 12px;
                font-size: 12px;
                font-weight: 500;
              }

              &.property-type-multi_select {
                .select-tag {
                  display: inline-block;
                  padding: 2px 6px;
                  margin: 1px 2px;
                  background: var(--database-secondary-color);
                  border-radius: 8px;
                  font-size: 11px;
                  font-weight: 500;
                }
              }

              &.property-type-checkbox {
                font-size: 14px;
                color: var(--database-primary-color);
              }

              &.property-type-number {
                font-family: 'Courier New', monospace;
                font-weight: 500;
              }

              &.property-type-date {
                color: #666;
                font-size: 13px;
              }

              &.property-type-url {
                color: var(--database-primary-color);
                text-decoration: underline;
              }
            }
          }
        }
      }
    }
  }

  // 空状态样式
  .empty-gallery {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    color: #666;

    .empty-icon {
      font-size: 64px;
      margin-bottom: 20px;
      opacity: 0.5;
    }

    .empty-message {
      font-size: 18px;
      font-weight: 500;
    }
  }
}

// 加载动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 1200px) {
  .notion-database-view-gallery {
    .notion-database-gallery {
      grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
      gap: 16px;
      padding: 16px;
    }
  }
}

@media (max-width: 768px) {
  .notion-database-view-gallery {
    .notion-database-gallery {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 12px;
      padding: 12px;

      .notion-gallery-card {
        .notion-record-cover {
          height: 140px;
        }

        .notion-gallery-content {
          padding: 12px;

          .notion-record-title {
            font-size: 15px;
            margin-bottom: 6px;
          }

          .notion-record-properties {
            .notion-record-property {
              font-size: 13px;
              margin-bottom: 3px;

              .notion-property-label {
                min-width: 70px;
              }
            }
          }
        }
      }
    }

    .empty-gallery {
      padding: 60px 20px;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      .empty-message {
        font-size: 16px;
      }
    }
  }
}

@media (max-width: 480px) {
  .notion-database-view-gallery {
    .notion-database-gallery {
      grid-template-columns: 1fr;
      gap: 10px;
      padding: 10px;

      .notion-gallery-card {
        .notion-record-cover {
          height: 120px;
        }

        .notion-gallery-content {
          padding: 10px;

          .notion-record-title {
            font-size: 14px;
            margin-bottom: 5px;
          }

          .notion-record-properties {
            .notion-record-property {
              font-size: 12px;
              margin-bottom: 2px;

              .notion-property-label {
                min-width: 60px;
              }
            }
          }
        }
      }
    }

    .empty-gallery {
      padding: 40px 15px;

      .empty-icon {
        font-size: 36px;
        margin-bottom: 12px;
      }

      .empty-message {
        font-size: 14px;
      }
    }
  }
}

// 加载状态
.notion-database-view-gallery {
  &.loading {
    .notion-database-gallery {
      opacity: 0.6;
      pointer-events: none;
    }
  }
}

// 画廊卡片动画
.notion-gallery-card {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 瀑布流布局支持（可选）
.notion-database-view-gallery {
  &.masonry {
    .notion-database-gallery {
      column-count: auto;
      column-width: 280px;
      column-gap: 20px;

      .notion-gallery-card {
        break-inside: avoid;
        margin-bottom: 20px;
      }
    }
  }
}
