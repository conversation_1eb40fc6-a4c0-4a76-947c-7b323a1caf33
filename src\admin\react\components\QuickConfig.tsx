/**
 * 快速配置组件
 */

import { useState } from 'react';
import { useSettings, useAppActions } from '../context/AppContext';
import { useWordPressAjax, useI18n } from '../hooks/useWordPress';
import { Select, FormRow } from './Input';
import { Button } from './Button';

// 推荐配置类型
interface RecommendationItem {
  description: string;
  config: Record<string, unknown>;
}

interface Recommendations {
  performance?: RecommendationItem;
  field_mapping?: RecommendationItem;
  sync_schedule?: RecommendationItem;
}

export function QuickConfig() {
  const settings = useSettings();
  const { updateSettings, showToast } = useAppActions();
  const { request } = useWordPressAjax();
  const { __ } = useI18n();

  const [isGettingRecommendations, setIsGettingRecommendations] = useState(false);
  const [recommendations, setRecommendations] = useState<Recommendations | null>(null);

  // 性能级别选项
  const performanceLevels = [
    { value: 'conservative', label: __('保守模式 - 适合配置较低的服务器', '保守模式 - 适合配置较低的服务器') },
    { value: 'balanced', label: __('平衡模式 - 推荐的默认配置', '平衡模式 - 推荐的默认配置') },
    { value: 'aggressive', label: __('激进模式 - 适合高性能服务器', '激进模式 - 适合高性能服务器') },
  ];

  // 字段模板选项
  const fieldTemplates = [
    { value: 'english', label: __('英文模板 - 适合英文Notion数据库', '英文模板 - 适合英文Notion数据库') },
    { value: 'chinese', label: __('中文模板 - 适合中文Notion数据库', '中文模板 - 适合中文Notion数据库') },
    { value: 'mixed', label: __('混合模板 - 中英文兼容', '混合模板 - 中英文兼容') },
    { value: 'custom', label: __('自定义 - 手动配置所有字段', '自定义 - 手动配置所有字段') },
  ];

  // 获取智能推荐
  const handleGetRecommendations = async () => {
    setIsGettingRecommendations(true);
    try {
      const response = await request('notion_to_wordpress_get_smart_recommendations', {
        api_key: settings.notion_api_key,
        database_id: settings.notion_database_id,
      });
      
      if (response.success && response.data) {
        setRecommendations(response.data);
        showToast({
          type: 'success',
          message: '智能推荐已生成',
        });
      } else {
        showToast({
          type: 'error',
          message: response.message || '获取推荐失败',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: '获取推荐失败：' + (error instanceof Error ? error.message : '未知错误'),
      });
    } finally {
      setIsGettingRecommendations(false);
    }
  };

  // 应用推荐配置
  const handleApplyRecommendation = (type: keyof Recommendations) => {
    if (!recommendations || !recommendations[type]) return;

    const config = recommendations[type]?.config;
    if (config) {
      updateSettings(config);
    }
    
    showToast({
      type: 'success',
      message: `已应用${type}推荐配置`,
    });
  };

  return (
    <div className="notion-wp-settings-section">
      <h2>{__('🚀 快速配置', '🚀 快速配置')}</h2>
      <p className="description">
        {__('使用预设模板快速配置插件，适合大多数用户。高级用户可以在其他标签页进行详细配置。', '使用预设模板快速配置插件，适合大多数用户。高级用户可以在其他标签页进行详细配置。')}
      </p>

      <table className="form-table">
        <tbody>
          <FormRow
            label={__('性能级别', '性能级别')}
            description={__('选择适合您服务器配置的性能级别。系统会自动设置最优的API分页大小、基准并发数等参数，并根据实时性能动态调整。', '选择适合您服务器配置的性能级别。系统会自动设置最优的API分页大小、基准并发数等参数，并根据实时性能动态调整。')}
          >
            <Select
              value={settings.performance_level}
              options={performanceLevels}
              onChange={(value) => updateSettings({ performance_level: value })}
            />
          </FormRow>

          <FormRow
            label={__('字段映射模板', '字段映射模板')}
            description={__('选择与您的Notion数据库语言匹配的字段映射模板。选择"自定义"可在字段映射标签页进行详细配置。', '选择与您的Notion数据库语言匹配的字段映射模板。选择"自定义"可在字段映射标签页进行详细配置。')}
          >
            <Select
              value={settings.field_template}
              options={fieldTemplates}
              onChange={(value) => updateSettings({ field_template: value })}
            />
          </FormRow>
        </tbody>
      </table>

      {/* 智能推荐 */}
      <div className="notion-wp-smart-recommendations">
        <h3>{__('💡 智能推荐', '💡 智能推荐')}</h3>
        <div className="config-recommendations">
          <Button
            variant="secondary"
            loading={isGettingRecommendations}
            onClick={handleGetRecommendations}
            disabled={!settings.notion_api_key || !settings.notion_database_id}
          >
            {__('获取配置建议', '获取配置建议')}
          </Button>
          
          {recommendations && (
            <div className="recommendations-result">
              <h4>{__('推荐配置', '推荐配置')}</h4>
              
              {recommendations.performance && (
                <div className="recommendation-item">
                  <h5>{__('性能优化建议', '性能优化建议')}</h5>
                  <p>{recommendations.performance.description}</p>
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => handleApplyRecommendation('performance')}
                  >
                    {__('应用此配置', '应用此配置')}
                  </Button>
                </div>
              )}
              
              {recommendations.field_mapping && (
                <div className="recommendation-item">
                  <h5>{__('字段映射建议', '字段映射建议')}</h5>
                  <p>{recommendations.field_mapping.description}</p>
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => handleApplyRecommendation('field_mapping')}
                  >
                    {__('应用此配置', '应用此配置')}
                  </Button>
                </div>
              )}
              
              {recommendations.sync_schedule && (
                <div className="recommendation-item">
                  <h5>{__('同步计划建议', '同步计划建议')}</h5>
                  <p>{recommendations.sync_schedule.description}</p>
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => handleApplyRecommendation('sync_schedule')}
                  >
                    {__('应用此配置', '应用此配置')}
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
