/**
 * SCSS变量定义
 */

// 颜色系统
$primary-color: #0073aa;
$secondary-color: #005177;
$accent-color: #00a0d2;
$success-color: #46b450;
$warning-color: #ffb900;
$error-color: #dc3232;
$info-color: #00a0d2;

// 灰度色彩
$white: #ffffff;
$gray-50: #f9f9f9;
$gray-100: #f1f1f1;
$gray-200: #e1e1e1;
$gray-300: #d1d1d1;
$gray-400: #c1c1c1;
$gray-500: #a1a1a1;
$gray-600: #818181;
$gray-700: #616161;
$gray-800: #414141;
$gray-900: #212121;
$black: #000000;

// 文本颜色
$text-primary: $gray-900;
$text-secondary: $gray-700;
$text-muted: $gray-500;
$text-light: $gray-400;

// 背景颜色
$bg-primary: $white;
$bg-secondary: $gray-50;
$bg-tertiary: $gray-100;
$bg-dark: $gray-800;

// 边框颜色
$border-color: $gray-200;
$border-color-light: $gray-100;
$border-color-dark: $gray-300;

// 字体系统
$font-family-base:
  -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu,
  Cantarell, "Helvetica Neue", sans-serif;
$font-family-mono: Consolas, Monaco, "Courier New", monospace;

// 字体大小
$font-size-xs: 0.75rem; // 12px
$font-size-sm: 0.875rem; // 14px
$font-size-base: 1rem; // 16px
$font-size-lg: 1.125rem; // 18px
$font-size-xl: 1.25rem; // 20px
$font-size-2xl: 1.5rem; // 24px
$font-size-3xl: 1.875rem; // 30px
$font-size-4xl: 2.25rem; // 36px

// 字体权重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// 间距系统
$spacing-0: 0;
$spacing-1: 0.25rem; // 4px
$spacing-2: 0.5rem; // 8px
$spacing-3: 0.75rem; // 12px
$spacing-4: 1rem; // 16px
$spacing-5: 1.25rem; // 20px
$spacing-6: 1.5rem; // 24px
$spacing-8: 2rem; // 32px
$spacing-10: 2.5rem; // 40px
$spacing-12: 3rem; // 48px
$spacing-16: 4rem; // 64px
$spacing-20: 5rem; // 80px

// 圆角
$border-radius-none: 0;
$border-radius-sm: 0.125rem; // 2px
$border-radius-base: 0.25rem; // 4px
$border-radius-md: 0.375rem; // 6px
$border-radius-lg: 0.5rem; // 8px
$border-radius-xl: 0.75rem; // 12px
$border-radius-2xl: 1rem; // 16px
$border-radius-full: 9999px;

// 阴影
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-base:
  0 1px 3px 0 rgba(0, 0, 0, 0.1),
  0 1px 2px 0 rgba(0, 0, 0, 0.06);
$shadow-md:
  0 4px 6px -1px rgba(0, 0, 0, 0.1),
  0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg:
  0 10px 15px -3px rgba(0, 0, 0, 0.1),
  0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl:
  0 20px 25px -5px rgba(0, 0, 0, 0.1),
  0 10px 10px -5px rgba(0, 0, 0, 0.04);

// 断点
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// WordPress特定颜色
$wp-blue: #0073aa;
$wp-blue-dark: #005177;
$wp-green: #46b450;
$wp-red: #dc3232;
$wp-orange: #ffb900;
$wp-gray: #646970;

// 组件特定变量
$header-height: 60px;
$sidebar-width: 250px;
$content-max-width: 1200px;

// 动画
$transition-fast: 0.15s ease-in-out;
$transition-base: 0.3s ease-in-out;
$transition-slow: 0.5s ease-in-out;

// Z-index层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 表单控件
$input-height: 40px;
$input-padding-x: $spacing-3;
$input-padding-y: $spacing-2;
$input-border-width: 1px;
$input-border-color: $border-color;
$input-border-radius: $border-radius-base;
$input-focus-border-color: $primary-color;
$input-focus-box-shadow: 0 0 0 2px rgba($primary-color, 0.2);

// 按钮
$button-height: 40px;
$button-padding-x: $spacing-4;
$button-padding-y: $spacing-2;
$button-border-radius: $border-radius-base;
$button-font-weight: $font-weight-medium;

// 卡片
$card-padding: $spacing-6;
$card-border-radius: $border-radius-lg;
$card-border-color: $border-color;
$card-shadow: $shadow-sm;

// 通知
$notification-padding: $spacing-4;
$notification-border-radius: $border-radius-base;
$notification-border-width: 1px;
