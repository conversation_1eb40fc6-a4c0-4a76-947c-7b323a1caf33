/**
 * Notion to WordPress 管理界面主应用
 */

import { useEffect } from 'react';
import { AppProvider } from './context/AppContext';
import { TabManager } from './components/TabManager';
import { ToastContainer } from './components/ToastContainer';
import { LoadingOverlay } from './components/LoadingOverlay';
import { useWordPressSettings, useDebugMode } from './hooks/useWordPress';

// 主应用组件
function AppContent() {
  const { loadSettings } = useWordPressSettings();
  const { log } = useDebugMode();

  useEffect(() => {
    log('🚀 Notion to WordPress React App initializing...');
    
    // 初始化时加载设置
    loadSettings().then(response => {
      if (response.success && response.data) {
        // 这里会通过Context更新设置
        log('✅ Settings loaded successfully');
      }
    }).catch(error => {
      console.error('Failed to load settings:', error);
    });
  }, [loadSettings, log]);

  return (
    <div className="notion-to-wordpress-react-app">
      <div className="notion-wp-layout">
        <TabManager />
        <ToastContainer />
        <LoadingOverlay />
      </div>
    </div>
  );
}

// 根应用组件（包含Provider）
export function App() {
  return (
    <AppProvider>
      <AppContent />
    </AppProvider>
  );
}

export default App;
