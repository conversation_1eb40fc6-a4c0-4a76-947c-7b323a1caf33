/**
 * 数据库看板视图增强样式
 * 
 * 为React看板组件提供额外的交互和视觉增强
 */

// 看板视图增强
.notion-database-view-board {
  .notion-database-board {
    // 横向滚动优化
    scrollbar-width: thin;
    scrollbar-color: var(--database-border-color) transparent;
    
    &::-webkit-scrollbar {
      height: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background: var(--database-border-color);
      border-radius: 4px;
      
      &:hover {
        background: var(--database-primary-color);
      }
    }

    .notion-board-column {
      // 列的增强样式
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .notion-board-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .column-title {
          font-weight: 600;
          color: var(--database-text-color);
        }
        
        .column-count {
          background: var(--database-primary-color);
          color: white;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }
      }

      .notion-board-list {
        // 列表容器优化
        min-height: 100px;
        max-height: 70vh;
        overflow-y: auto;
        
        // 自定义滚动条
        scrollbar-width: thin;
        scrollbar-color: var(--database-border-color) transparent;
        
        &::-webkit-scrollbar {
          width: 6px;
        }
        
        &::-webkit-scrollbar-track {
          background: transparent;
        }
        
        &::-webkit-scrollbar-thumb {
          background: var(--database-border-color);
          border-radius: 3px;
          
          &:hover {
            background: var(--database-primary-color);
          }
        }

        // 空列状态
        .empty-column {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px 20px;
          color: #999;
          border: 2px dashed var(--database-border-color);
          border-radius: var(--database-border-radius);
          margin: 8px 0;

          .empty-icon {
            font-size: 32px;
            margin-bottom: 8px;
            opacity: 0.5;
          }

          .empty-message {
            font-size: 14px;
            font-weight: 500;
          }
        }

        .notion-board-card {
          // 卡片增强样式
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          
          &.notion-board-card-interactive {
            &:hover,
            &.hovered {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              transform: translateY(-2px);
              border-color: var(--database-primary-color);
            }

            // 键盘导航支持
            &:focus {
              outline: 2px solid var(--database-primary-color);
              outline-offset: 2px;
            }
          }

          .notion-record-title {
            // 标题样式优化
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
            
            // 标题截断
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .notion-record-properties {
            .notion-record-property {
              // 属性样式优化
              margin-bottom: 3px;
              
              .notion-property-label {
                font-size: 11px;
                font-weight: 500;
                color: #666;
                min-width: 50px;
              }

              .notion-property-value {
                font-size: 11px;
                
                &.property-type-select {
                  display: inline-block;
                  padding: 1px 6px;
                  background: var(--database-secondary-color);
                  border-radius: 8px;
                  font-size: 10px;
                  font-weight: 500;
                }

                &.property-type-multi_select {
                  .select-tag {
                    display: inline-block;
                    padding: 1px 4px;
                    margin: 1px 1px;
                    background: var(--database-secondary-color);
                    border-radius: 6px;
                    font-size: 9px;
                    font-weight: 500;
                  }
                }

                &.property-type-checkbox {
                  font-size: 12px;
                  color: var(--database-primary-color);
                }

                &.property-type-number {
                  font-family: 'Courier New', monospace;
                  font-weight: 500;
                }

                &.property-type-date {
                  color: #666;
                  font-size: 10px;
                }
              }
            }
          }

          // 卡片状态指示器
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--database-primary-color);
            border-radius: 0 2px 2px 0;
            opacity: 0;
            transition: opacity 0.2s ease;
          }

          &:hover::before {
            opacity: 1;
          }
        }
      }
    }
  }

  // 空状态样式
  .empty-board {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    color: #666;

    .empty-icon {
      font-size: 64px;
      margin-bottom: 20px;
      opacity: 0.5;
    }

    .empty-message {
      font-size: 18px;
      font-weight: 500;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .notion-database-view-board {
    .notion-database-board {
      padding: 12px;
      gap: 12px;

      .notion-board-column {
        min-width: 260px;
        padding: 12px;
      }
    }
  }
}

@media (max-width: 768px) {
  .notion-database-view-board {
    .notion-database-board {
      padding: 8px;
      gap: 8px;

      .notion-board-column {
        min-width: 240px;
        padding: 10px;

        .notion-board-header {
          font-size: 14px;
          margin-bottom: 12px;
          padding-bottom: 6px;
          
          .column-count {
            font-size: 11px;
            padding: 1px 6px;
          }
        }

        .notion-board-list {
          gap: 8px;
          max-height: 60vh;

          .empty-column {
            padding: 30px 15px;

            .empty-icon {
              font-size: 24px;
              margin-bottom: 6px;
            }

            .empty-message {
              font-size: 12px;
            }
          }

          .notion-board-card {
            padding: 8px;

            .notion-record-title {
              font-size: 13px;
              margin-bottom: 6px;
            }

            .notion-record-properties {
              .notion-record-property {
                margin-bottom: 2px;

                .notion-property-label {
                  font-size: 10px;
                  min-width: 45px;
                }

                .notion-property-value {
                  font-size: 10px;
                }
              }
            }
          }
        }
      }
    }

    .empty-board {
      padding: 60px 20px;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      .empty-message {
        font-size: 16px;
      }
    }
  }
}

@media (max-width: 480px) {
  .notion-database-view-board {
    .notion-database-board {
      padding: 6px;
      gap: 6px;

      .notion-board-column {
        min-width: 200px;
        padding: 8px;

        .notion-board-list {
          max-height: 50vh;

          .notion-board-card {
            padding: 6px;

            .notion-record-title {
              font-size: 12px;
              margin-bottom: 4px;
            }
          }
        }
      }
    }

    .empty-board {
      padding: 40px 15px;

      .empty-icon {
        font-size: 36px;
        margin-bottom: 12px;
      }

      .empty-message {
        font-size: 14px;
      }
    }
  }
}

// 加载状态
.notion-database-view-board {
  &.loading {
    .notion-database-board {
      opacity: 0.6;
      pointer-events: none;
    }
  }
}

// 看板卡片动画
.notion-board-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 拖拽支持（为未来功能预留）
.notion-database-view-board {
  &.draggable {
    .notion-board-card {
      &.dragging {
        opacity: 0.5;
        transform: rotate(2deg);
        z-index: 1000;
      }

      &.drag-over {
        border-top: 2px solid var(--database-primary-color);
      }
    }

    .notion-board-column {
      &.drag-over {
        background: rgba(0, 115, 170, 0.05);
        border: 2px dashed var(--database-primary-color);
      }
    }
  }
}
