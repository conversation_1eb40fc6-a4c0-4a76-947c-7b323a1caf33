<?php
declare(strict_types=1);

namespace NTWP\Core\Foundation;

/**
 * 插件统一异常类
 *
 * 简化的异常处理，提供统一的错误信息和上下文
 *
 * @since      2.0.0-beta.2
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 */

// 如果直接访问此文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

class Exception extends \Exception {
    
    /**
     * 异常上下文信息
     */
    protected array $context = [];
    
    /**
     * 错误类型
     */
    protected string $errorType = '';
    
    /**
     * 构造函数
     * 
     * @param string $message 异常消息
     * @param string $errorType 错误类型 (API_ERROR, IMPORT_ERROR, VALIDATION_ERROR等)
     * @param array $context 上下文信息
     * @param \Throwable|null $previous 前一个异常
     */
    public function __construct(
        string $message = '', 
        string $errorType = 'GENERAL_ERROR', 
        array $context = [], 
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, 0, $previous);
        $this->errorType = $errorType;
        $this->context = $context;
    }
    
    /**
     * 获取上下文信息
     */
    public function getContext(): array {
        return $this->context;
    }
    
    /**
     * 获取错误类型
     */
    public function getErrorType(): string {
        return $this->errorType;
    }
    
    /**
     * 转换为数组格式
     */
    public function toArray(): array {
        return [
            'error' => true,
            'message' => $this->getMessage(),
            'error_type' => $this->getErrorType(),
            'context' => $this->getContext(),
            'file' => basename($this->getFile()),
            'line' => $this->getLine()
        ];
    }
    
    /**
     * 转换为WP_Error对象
     */
    public function toWpError(): \WP_Error {
        return new \WP_Error(
            strtolower($this->getErrorType()),
            $this->getMessage(),
            $this->toArray()
        );
    }
    
    /**
     * 记录异常日志
     */
    public function logException(string $prefix = 'Exception'): void {
        $log_message = sprintf(
            '[%s] %s | 文件: %s:%d',
            $this->getErrorType(),
            $this->getMessage(),
            basename($this->getFile()),
            $this->getLine()
        );
        
        if (!empty($this->context)) {
            $log_message .= ' | 上下文: ' . json_encode($this->context, JSON_UNESCAPED_UNICODE);
        }
        
        Logger::errorLog($log_message, $prefix);
    }
    
    // === 静态工厂方法 ===
    
    /**
     * 创建API异常
     */
    public static function api(string $message, array $context = []): self {
        return new self($message, 'API_ERROR', $context);
    }
    
    /**
     * 创建导入异常
     */
    public static function import(string $message, array $context = []): self {
        return new self($message, 'IMPORT_ERROR', $context);
    }
    
    /**
     * 创建验证异常
     */
    public static function validation(string $message, array $context = []): self {
        return new self($message, 'VALIDATION_ERROR', $context);
    }
    
    /**
     * 创建网络异常
     */
    public static function network(string $message, array $context = []): self {
        return new self($message, 'NETWORK_ERROR', $context);
    }
    
    /**
     * 创建认证异常
     */
    public static function auth(string $message, array $context = []): self {
        return new self($message, 'AUTH_ERROR', $context);
    }
    
    /**
     * 从其他异常创建
     */
    public static function from(\Throwable $exception, string $errorType = 'GENERAL_ERROR', array $context = []): self {
        return new self(
            $exception->getMessage(),
            $errorType,
            array_merge($context, [
                'original_file' => $exception->getFile(),
                'original_line' => $exception->getLine(),
                'original_code' => $exception->getCode()
            ]),
            $exception
        );
    }
}
