<?php
declare(strict_types=1);

namespace NTWP\Core\Foundation;

use NTWP\Core\Task\ModernAsyncEngine;
use NTWP\Core\Task\AsyncTaskScheduler;
use NTWP\Infrastructure\Concurrency\TaskQueue;
use NTWP\Core\Performance\ProgressTracker;

/**
 * 异步处理器
 *
 * 提供统一的异步处理接口，封装现代异步引擎的复杂性
 * 支持状态管理、操作控制和进度跟踪
 *
 * @since      2.0.0-beta.1
 * @version    2.0.0-beta.2
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-Loong/Notion-to-WordPress
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class AsyncProcessor {
    
    /**
     * 状态常量
     */
    public const STATUS_IDLE = 'idle';
    public const STATUS_RUNNING = 'running';
    public const STATUS_PAUSED = 'paused';
    public const STATUS_ERROR = 'error';
    public const STATUS_COMPLETED = 'completed';
    
    /**
     * 操作类型常量
     */
    public const OPERATION_IMPORT = 'import';
    public const OPERATION_SYNC = 'sync';
    public const OPERATION_UPDATE = 'update';
    public const OPERATION_CLEANUP = 'cleanup';
    
    /**
     * 异步处理器实例
     */
    private static ?self $instance = null;
    
    /**
     * 当前状态
     */
    private string $status = self::STATUS_IDLE;
    
    /**
     * 当前操作
     */
    private string $operation = '';
    
    /**
     * 开始时间
     */
    private int $started_at = 0;
    
    /**
     * 更新时间
     */
    private int $updated_at = 0;
    
    /**
     * 数据量
     */
    private int $data_count = 0;
    
    /**
     * 进度百分比
     */
    private int $progress = 0;
    
    /**
     * 详细信息
     */
    private array $details = [];
    
    /**
     * 构造函数（私有，单例模式）
     */
    private function __construct() {
        $this->initializeInstance();
    }
    
    /**
     * 获取单例实例
     */
    public static function getInstance(): self {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 初始化异步处理器
     */
    public static function init(): void {
        self::getInstance();
        Logger::infoLog('异步处理器已初始化', 'Async Processor');
    }
    
    /**
     * 初始化实例
     */
    private function initializeInstance(): void {
        $this->status = self::STATUS_IDLE;
        $this->operation = '';
        $this->started_at = 0;
        $this->updated_at = time();
        $this->data_count = 0;
        $this->progress = 0;
        $this->details = [];
    }
    
    /**
     * 获取异步状态
     *
     * @return array 异步状态信息
     */
    public static function getAsyncStatus(): array {
        try {
            $instance = self::getInstance();
            
            return [
                'status' => $instance->status,
                'operation' => $instance->operation,
                'started_at' => $instance->started_at > 0 ? date('Y-m-d H:i:s', $instance->started_at) : '',
                'updated_at' => $instance->updated_at > 0 ? date('Y-m-d H:i:s', $instance->updated_at) : '',
                'data_count' => $instance->data_count,
                'progress' => $instance->progress,
                'details' => $instance->details
            ];
            
        } catch (\Exception $e) {
            Logger::errorLog(
                sprintf('获取异步状态失败: %s', $e->getMessage()),
                'Async Processor'
            );
            
            return [
                'status' => self::STATUS_ERROR,
                'operation' => '',
                'started_at' => '',
                'updated_at' => date('Y-m-d H:i:s'),
                'data_count' => 0,
                'progress' => 0,
                'details' => ['error' => $e->getMessage()]
            ];
        }
    }
    
    /**
     * 暂停异步操作
     *
     * @return bool 是否成功暂停
     */
    public static function pauseAsyncOperation(): bool {
        try {
            $instance = self::getInstance();
            
            if ($instance->status === self::STATUS_RUNNING) {
                $instance->status = self::STATUS_PAUSED;
                $instance->updated_at = time();
                
                // 暂停相关组件
                if (class_exists('NTWP\\Core\\Task\\ModernAsyncEngine')) {
                    // 这里可以添加暂停ModernAsyncEngine的逻辑
                }
                
                Logger::infoLog('异步操作已暂停', 'Async Processor');
                return true;
            }
            
            return false;
            
        } catch (\Exception $e) {
            Logger::errorLog(
                sprintf('暂停异步操作失败: %s', $e->getMessage()),
                'Async Processor'
            );
            return false;
        }
    }
    
    /**
     * 恢复异步操作
     *
     * @return bool 是否成功恢复
     */
    public static function resumeAsyncOperation(): bool {
        try {
            $instance = self::getInstance();
            
            if ($instance->status === self::STATUS_PAUSED) {
                $instance->status = self::STATUS_RUNNING;
                $instance->updated_at = time();
                
                // 恢复相关组件
                if (class_exists('NTWP\\Core\\Task\\ModernAsyncEngine')) {
                    // 这里可以添加恢复ModernAsyncEngine的逻辑
                }
                
                Logger::infoLog('异步操作已恢复', 'Async Processor');
                return true;
            }
            
            return false;
            
        } catch (\Exception $e) {
            Logger::errorLog(
                sprintf('恢复异步操作失败: %s', $e->getMessage()),
                'Async Processor'
            );
            return false;
        }
    }
    
    /**
     * 停止异步操作
     *
     * @return bool 是否成功停止
     */
    public static function stopAsyncOperation(): bool {
        try {
            $instance = self::getInstance();
            
            if (in_array($instance->status, [self::STATUS_RUNNING, self::STATUS_PAUSED])) {
                $instance->status = self::STATUS_IDLE;
                $instance->operation = '';
                $instance->started_at = 0;
                $instance->updated_at = time();
                $instance->data_count = 0;
                $instance->progress = 0;
                $instance->details = [];
                
                // 停止相关组件
                if (class_exists('NTWP\\Core\\Task\\ModernAsyncEngine')) {
                    // 这里可以添加停止ModernAsyncEngine的逻辑
                }
                
                Logger::infoLog('异步操作已停止', 'Async Processor');
                return true;
            }
            
            return false;
            
        } catch (\Exception $e) {
            Logger::errorLog(
                sprintf('停止异步操作失败: %s', $e->getMessage()),
                'Async Processor'
            );
            return false;
        }
    }
    
    /**
     * 开始异步操作
     *
     * @deprecated 2.0.0-beta.2 请使用 ModernAsyncEngine::execute()，AsyncProcessor将被移除
     * @see \NTWP\Core\Task\ModernAsyncEngine::execute()
     * @param string $operation 操作类型
     * @param int $data_count 数据量
     * @param array $details 详细信息
     * @return bool 是否成功开始
     */
    public static function startAsyncOperation(string $operation, int $data_count = 0, array $details = []): bool {
        try {
            $instance = self::getInstance();

            $instance->status = self::STATUS_RUNNING;
            $instance->operation = $operation;
            $instance->started_at = time();
            $instance->updated_at = time();
            $instance->data_count = $data_count;
            $instance->progress = 0;
            $instance->details = $details;

            Logger::infoLog(
                sprintf('异步操作已开始: %s, 数据量: %d (注意: AsyncProcessor已废弃)', $operation, $data_count),
                'Async Processor'
            );

            return true;

        } catch (\Exception $e) {
            Logger::errorLog(
                sprintf('开始异步操作失败: %s', $e->getMessage()),
                'Async Processor'
            );
            return false;
        }
    }
    
    /**
     * 更新进度
     *
     * @deprecated 2.0.0-beta.2 请使用 ModernAsyncEngine 的进度跟踪，AsyncProcessor将被移除
     * @see \NTWP\Core\Task\ModernAsyncEngine::getProgress()
     * @param int $progress 进度百分比 (0-100)
     * @param array $details 详细信息
     * @return bool 是否成功更新
     */
    public static function updateProgress(int $progress, array $details = []): bool {
        try {
            $instance = self::getInstance();

            if ($instance->status === self::STATUS_RUNNING) {
                $instance->progress = max(0, min(100, $progress));
                $instance->updated_at = time();
                $instance->details = array_merge($instance->details, $details);

                Logger::debugLog(
                    sprintf('进度更新: %d%% (注意: AsyncProcessor已废弃)', $progress),
                    'Async Processor'
                );

                return true;
            }

            return false;

        } catch (\Exception $e) {
            Logger::errorLog(
                sprintf('更新进度失败: %s', $e->getMessage()),
                'Async Processor'
            );
            return false;
        }
    }
    
    /**
     * 完成异步操作
     *
     * @deprecated 2.0.0-beta.2 请使用 ModernAsyncEngine 的任务管理，AsyncProcessor将被移除
     * @see \NTWP\Core\Task\ModernAsyncEngine
     * @param array $details 详细信息
     * @return bool 是否成功完成
     */
    public static function completeAsyncOperation(array $details = []): bool {
        try {
            $instance = self::getInstance();

            $instance->status = self::STATUS_COMPLETED;
            $instance->progress = 100;
            $instance->updated_at = time();
            $instance->details = array_merge($instance->details, $details);

            Logger::infoLog(
                sprintf('异步操作已完成: %s (注意: AsyncProcessor已废弃)', $instance->operation),
                'Async Processor'
            );

            return true;

        } catch (\Exception $e) {
            Logger::errorLog(
                sprintf('完成异步操作失败: %s', $e->getMessage()),
                'Async Processor'
            );
            return false;
        }
    }
    
    /**
     * 设置错误状态
     *
     * @param string $error_message 错误信息
     * @param array $details 详细信息
     * @return bool 是否成功设置
     */
    public static function setError(string $error_message, array $details = []): bool {
        try {
            $instance = self::getInstance();
            
            $instance->status = self::STATUS_ERROR;
            $instance->updated_at = time();
            $instance->details = array_merge($instance->details, $details, ['error' => $error_message]);
            
            Logger::errorLog(
                sprintf('异步操作出错: %s', $error_message),
                'Async Processor'
            );
            
            return true;
            
        } catch (\Exception $e) {
            Logger::errorLog(
                sprintf('设置错误状态失败: %s', $e->getMessage()),
                'Async Processor'
            );
            return false;
        }
    }
    
    /**
     * 检查是否正在运行
     *
     * @return bool 是否正在运行
     */
    public static function isRunning(): bool {
        $instance = self::getInstance();
        return $instance->status === self::STATUS_RUNNING;
    }
    
    /**
     * 检查是否已暂停
     *
     * @return bool 是否已暂停
     */
    public static function isPaused(): bool {
        $instance = self::getInstance();
        return $instance->status === self::STATUS_PAUSED;
    }
    
    /**
     * 检查是否有错误
     *
     * @return bool 是否有错误
     */
    public static function hasError(): bool {
        $instance = self::getInstance();
        return $instance->status === self::STATUS_ERROR;
    }
} 