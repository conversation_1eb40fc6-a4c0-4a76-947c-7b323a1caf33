<?php
declare(strict_types=1);

namespace NTWP\Utils;

/**
 * WordPress集成工具类
 *
 * 专门处理与WordPress核心功能的集成，
 * 提供路径管理、通知显示、钩子注册等WordPress特定功能。
 *
 * 职责范围：
 * - WordPress路径和URL管理
 * - 管理界面通知显示
 * - WordPress钩子和过滤器管理
 * - 插件生命周期管理
 * - WordPress环境检测
 *
 * @since      2.0.0-beta.2
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-<PERSON>ong/Notion-to-WordPress
 */

// 如果直接访问此文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

class WordPressHelper {

    /**
     * 通知类型常量
     * 
     * @since 2.0.0-beta.2
     */
    const NOTICE_SUCCESS = 'success';
    const NOTICE_WARNING = 'warning';
    const NOTICE_ERROR = 'error';
    const NOTICE_INFO = 'info';

    /**
     * 已注册的通知队列
     * 
     * @since 2.0.0-beta.2
     */
    private static array $notice_queue = [];

    /**
     * 获取插件文件或目录的绝对服务器路径
     *
     * @since 2.0.0-beta.2
     * @param string $path （可选）相对于插件根目录的路径
     * @return string 绝对服务器路径
     */
    public static function pluginPath(string $path = ''): string {
        // 规范化输入路径
        $normalized_path = str_replace(['/', '\\'], DIRECTORY_SEPARATOR, ltrim($path, '/\\'));

        if (defined('NOTION_TO_WORDPRESS_FILE') && function_exists('plugin_dir_path')) {
            // WordPress环境下的标准方式
            $base_path = plugin_dir_path(NOTION_TO_WORDPRESS_FILE);
        } elseif (defined('NOTION_TO_WORDPRESS_FILE')) {
            // 非WordPress环境下，基于常量文件路径
            $base_path = dirname(NOTION_TO_WORDPRESS_FILE) . DIRECTORY_SEPARATOR;
        } else {
            // 最后的回退方式 - 基于当前文件位置推算
            // 当前文件在 includes/utils/，需要回到插件根目录
            $plugin_dir = dirname(dirname(dirname(__FILE__)));
            $base_path = $plugin_dir . DIRECTORY_SEPARATOR;
        }

        // 确保base_path以目录分隔符结尾
        if (!empty($normalized_path) && !str_ends_with($base_path, DIRECTORY_SEPARATOR)) {
            $base_path .= DIRECTORY_SEPARATOR;
        }

        return $base_path . $normalized_path;
    }

    /**
     * 获取插件文件或目录的URL
     *
     * @since 2.0.0-beta.2
     * @param string $path （可选）相对于插件根目录的路径
     * @return string 插件URL
     */
    public static function pluginUrl(string $path = ''): string {
        if (!defined('NOTION_TO_WORDPRESS_FILE')) {
            // 回退到当前文件的URL推算
            $plugin_url = plugins_url('', dirname(dirname(dirname(__FILE__))) . '/notion-to-wordpress.php');
            return $plugin_url . '/' . ltrim($path, '/\\');
        }
        
        return plugin_dir_url(NOTION_TO_WORDPRESS_FILE) . ltrim($path, '/\\');
    }

    /**
     * 获取插件的基础名称
     *
     * @since 2.0.0-beta.2
     * @return string 插件基础名称
     */
    public static function pluginBasename(): string {
        if (!defined('NOTION_TO_WORDPRESS_FILE')) {
            return 'notion-to-wordpress/notion-to-wordpress.php';
        }
        
        return plugin_basename(NOTION_TO_WORDPRESS_FILE);
    }

    /**
     * 在后台显示通知消息
     *
     * @since 2.0.0-beta.2
     * @param string $message 要显示的消息
     * @param string $type 通知类型 ('success', 'warning', 'error', 'info')
     * @param bool $dismissible 是否可关闭
     * @param bool $immediate 是否立即显示（否则加入队列）
     */
    public static function adminNotice(
        string $message, 
        string $type = self::NOTICE_ERROR, 
        bool $dismissible = true,
        bool $immediate = false
    ): void {
        // 验证通知类型
        $valid_types = [self::NOTICE_SUCCESS, self::NOTICE_WARNING, self::NOTICE_ERROR, self::NOTICE_INFO];
        if (!in_array($type, $valid_types, true)) {
            $type = self::NOTICE_ERROR;
        }

        $notice = [
            'message' => $message,
            'type' => $type,
            'dismissible' => $dismissible,
            'timestamp' => time()
        ];

        if ($immediate) {
            self::displayNotice($notice);
        } else {
            self::$notice_queue[] = $notice;
            
            // 确保钩子只注册一次
            if (count(self::$notice_queue) === 1) {
                add_action('admin_notices', [self::class, 'displayQueuedNotices']);
            }
        }
    }

    /**
     * 显示队列中的所有通知
     *
     * @since 2.0.0-beta.2
     */
    public static function displayQueuedNotices(): void {
        foreach (self::$notice_queue as $notice) {
            self::displayNotice($notice);
        }
        
        // 清空队列
        self::$notice_queue = [];
    }

    /**
     * 显示单个通知
     *
     * @since 2.0.0-beta.2
     * @param array $notice 通知数据
     */
    private static function displayNotice(array $notice): void {
        $dismissible_class = $notice['dismissible'] ? ' is-dismissible' : '';
        $notice_class = 'notice notice-' . esc_attr($notice['type']) . $dismissible_class;
        
        printf(
            '<div class="%s"><p>%s</p></div>',
            esc_attr($notice_class),
            wp_kses($notice['message'], [
                'strong' => [],
                'em' => [],
                'a' => ['href' => [], 'target' => []],
                'code' => [],
                'br' => []
            ])
        );
    }

    /**
     * 检查当前是否在WordPress管理后台
     *
     * @since 2.0.0-beta.2
     * @return bool 是否在管理后台
     */
    public static function isAdmin(): bool {
        return is_admin() && !wp_doing_ajax() && !wp_doing_cron();
    }

    /**
     * 检查当前是否在AJAX请求中
     *
     * @since 2.0.0-beta.2
     * @return bool 是否在AJAX请求中
     */
    public static function isAjax(): bool {
        return wp_doing_ajax();
    }

    /**
     * 检查当前是否在Cron任务中
     *
     * @since 2.0.0-beta.2
     * @return bool 是否在Cron任务中
     */
    public static function isCron(): bool {
        return wp_doing_cron();
    }

    /**
     * 检查当前用户是否有指定权限
     *
     * @since 2.0.0-beta.2
     * @param string $capability 权限名称
     * @return bool 是否有权限
     */
    public static function currentUserCan(string $capability): bool {
        return current_user_can($capability);
    }

    /**
     * 安全地获取当前用户ID
     *
     * @since 2.0.0-beta.2
     * @return int 用户ID，如果未登录则返回0
     */
    public static function getCurrentUserId(): int {
        return get_current_user_id();
    }

    /**
     * 获取WordPress版本
     *
     * @since 2.0.0-beta.2
     * @return string WordPress版本号
     */
    public static function getWordPressVersion(): string {
        global $wp_version;
        return $wp_version ?? '0.0.0';
    }

    /**
     * 检查WordPress版本是否满足最低要求
     *
     * @since 2.0.0-beta.2
     * @param string $min_version 最低版本要求
     * @return bool 是否满足要求
     */
    public static function checkWordPressVersion(string $min_version): bool {
        return version_compare(self::getWordPressVersion(), $min_version, '>=');
    }

    /**
     * 获取WordPress语言设置
     *
     * @since 2.0.0-beta.2
     * @return string 语言代码
     */
    public static function getLocale(): string {
        return get_locale();
    }

    /**
     * 检查是否为中文环境
     *
     * @since 2.0.0-beta.2
     * @return bool 是否为中文环境
     */
    public static function isChineseLocale(): bool {
        $locale = self::getLocale();
        return strpos($locale, 'zh') === 0;
    }

    /**
     * 安全地重定向到指定URL
     *
     * @since 2.0.0-beta.2
     * @param string $url 目标URL
     * @param int $status HTTP状态码
     * @param string $x_redirect_by 重定向来源标识
     */
    public static function safeRedirect(string $url, int $status = 302, string $x_redirect_by = 'Notion-to-WordPress'): void {
        wp_safe_redirect($url, $status, $x_redirect_by);
        exit;
    }

    /**
     * 获取管理页面URL
     *
     * @since 2.0.0-beta.2
     * @param string $page 页面slug
     * @param array $args 额外参数
     * @return string 管理页面URL
     */
    public static function getAdminUrl(string $page = '', array $args = []): string {
        $url = admin_url('admin.php');
        
        if (!empty($page)) {
            $args['page'] = $page;
        }
        
        if (!empty($args)) {
            $url = add_query_arg($args, $url);
        }
        
        return $url;
    }

    /**
     * 创建nonce
     *
     * @since 2.0.0-beta.2
     * @param string $action nonce动作
     * @return string nonce值
     */
    public static function createNonce(string $action): string {
        return wp_create_nonce($action);
    }

    /**
     * 验证nonce
     *
     * @since 2.0.0-beta.2
     * @param string $nonce nonce值
     * @param string $action nonce动作
     * @return bool 是否验证通过
     */
    public static function verifyNonce(string $nonce, string $action): bool {
        return wp_verify_nonce($nonce, $action) !== false;
    }

    /**
     * 获取插件信息
     *
     * @since 2.0.0-beta.2
     * @return array 插件信息
     */
    public static function getPluginInfo(): array {
        $plugin_file = self::pluginPath('notion-to-wordpress.php');
        
        if (!function_exists('get_plugin_data')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }
        
        return get_plugin_data($plugin_file, false, false);
    }

    /**
     * 检查插件是否已激活
     *
     * @since 2.0.0-beta.2
     * @param string $plugin 插件路径
     * @return bool 是否已激活
     */
    public static function isPluginActive(string $plugin): bool {
        if (!function_exists('is_plugin_active')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }
        
        return is_plugin_active($plugin);
    }

    /**
     * 清除所有通知队列
     *
     * @since 2.0.0-beta.2
     */
    public static function clearNoticeQueue(): void {
        self::$notice_queue = [];
    }
}
