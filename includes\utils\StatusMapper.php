<?php
declare(strict_types=1);

namespace NTWP\Utils;

/**
 * 状态映射工具类
 *
 * 专门处理Notion状态到WordPress状态的映射转换，
 * 提供标准化的状态映射规则和转换方法。
 *
 * 职责范围：
 * - Notion状态到WordPress状态的映射
 * - 状态映射规则的管理和维护
 * - 状态标准化和验证
 * - 自定义状态映射的支持
 *
 * @since      2.0.0-beta.2
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-Loong/Notion-to-WordPress
 */

// 如果直接访问此文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

class StatusMapper {

    /**
     * 默认状态映射规则
     * 
     * @since 2.0.0-beta.2
     */
    private const DEFAULT_STATUS_MAPPING = [
        // 已发布状态
        'published' => 'publish',
        '已发布' => 'publish',
        'publish' => 'publish',
        'public' => 'publish',
        '公开' => 'publish',
        'live' => 'publish',
        '上线' => 'publish',

        // 私密状态
        'private' => 'private',
        'privacy' => 'private',
        '私密' => 'private',
        'private_post' => 'private',

        // 草稿状态
        'draft' => 'draft',
        '草稿' => 'draft',
        'unpublished' => 'draft',
        '未发布' => 'draft',
        
        // 待审核状态
        'pending' => 'pending',
        'review' => 'pending',
        '待审核' => 'pending',
        '审核中' => 'pending',
        
        // 自动保存草稿
        'auto-draft' => 'auto-draft',
        '自动草稿' => 'auto-draft',
        
        // 继承状态（用于附件）
        'inherit' => 'inherit',
        '继承' => 'inherit',
        
        // 垃圾箱状态
        'trash' => 'trash',
        '垃圾箱' => 'trash',
        '已删除' => 'trash'
    ];

    /**
     * 缓存的自定义映射规则
     * 
     * @since 2.0.0-beta.2
     */
    private static ?array $custom_mapping_cache = null;

    /**
     * 获取完整的状态映射规则
     *
     * @since 2.0.0-beta.2
     * @param bool $include_custom 是否包含自定义映射
     * @return array 状态映射数组
     */
    public static function getStatusMapping(bool $include_custom = true): array {
        $mapping = self::DEFAULT_STATUS_MAPPING;
        
        if ($include_custom) {
            $custom_mapping = self::getCustomMapping();
            if (!empty($custom_mapping)) {
                $mapping = array_merge($mapping, $custom_mapping);
            }
        }
        
        return $mapping;
    }

    /**
     * 标准化Notion状态到WordPress状态
     *
     * @since 2.0.0-beta.2
     * @param string $notion_status Notion中的状态值
     * @param string $default_status 默认状态（当映射失败时使用）
     * @return string WordPress标准状态
     */
    public static function notionToWordPress(string $notion_status, string $default_status = 'draft'): string {
        // 清理输入
        $status_clean = strtolower(trim($notion_status));
        
        // 空值处理
        if (empty($status_clean)) {
            return $default_status;
        }
        
        // 获取映射规则
        $mapping = self::getStatusMapping();
        
        // 直接映射
        if (isset($mapping[$status_clean])) {
            return $mapping[$status_clean];
        }
        
        // 模糊匹配（处理可能的变体）
        $fuzzy_match = self::fuzzyMatch($status_clean, $mapping);
        if ($fuzzy_match !== null) {
            return $fuzzy_match;
        }
        
        // 记录未知状态
        if (class_exists('\\NTWP\\Core\\Foundation\\Logger')) {
            \NTWP\Core\Foundation\Logger::warningLog(
                "未知的Notion状态: '{$notion_status}'，使用默认状态: '{$default_status}'",
                'Status Mapper'
            );
        }
        
        return $default_status;
    }

    /**
     * 验证WordPress状态是否有效
     *
     * @since 2.0.0-beta.2
     * @param string $status WordPress状态
     * @return bool 是否为有效状态
     */
    public static function isValidWordPressStatus(string $status): bool {
        $valid_statuses = [
            'publish', 'private', 'draft', 'pending', 
            'auto-draft', 'inherit', 'trash'
        ];
        
        return in_array($status, $valid_statuses, true);
    }

    /**
     * 获取所有可能的WordPress状态
     *
     * @since 2.0.0-beta.2
     * @return array WordPress状态列表
     */
    public static function getWordPressStatuses(): array {
        return [
            'publish' => '已发布',
            'private' => '私密',
            'draft' => '草稿',
            'pending' => '待审核',
            'auto-draft' => '自动草稿',
            'inherit' => '继承',
            'trash' => '垃圾箱'
        ];
    }

    /**
     * 获取自定义状态映射
     *
     * @since 2.0.0-beta.2
     * @return array 自定义映射规则
     */
    private static function getCustomMapping(): array {
        // 使用缓存避免重复读取
        if (self::$custom_mapping_cache !== null) {
            return self::$custom_mapping_cache;
        }
        
        // 从WordPress选项中读取自定义映射
        $options = get_option('notion_to_wordpress_options', []);
        $custom_mapping = $options['custom_status_mapping'] ?? [];
        
        // 验证自定义映射
        $validated_mapping = [];
        if (is_array($custom_mapping)) {
            foreach ($custom_mapping as $notion_status => $wp_status) {
                if (is_string($notion_status) && is_string($wp_status) && 
                    self::isValidWordPressStatus($wp_status)) {
                    $validated_mapping[strtolower(trim($notion_status))] = $wp_status;
                }
            }
        }
        
        // 缓存结果
        self::$custom_mapping_cache = $validated_mapping;
        
        return $validated_mapping;
    }

    /**
     * 模糊匹配状态
     *
     * @since 2.0.0-beta.2
     * @param string $input 输入状态
     * @param array $mapping 映射规则
     * @return string|null 匹配的WordPress状态，如果没有匹配则返回null
     */
    private static function fuzzyMatch(string $input, array $mapping): ?string {
        // 尝试部分匹配
        foreach ($mapping as $notion_status => $wp_status) {
            // 检查是否包含关键词
            if (strpos($input, $notion_status) !== false || 
                strpos($notion_status, $input) !== false) {
                return $wp_status;
            }
        }
        
        // 尝试英文关键词匹配
        $keywords = [
            'publish' => ['pub', 'live', 'online', 'active'],
            'draft' => ['temp', 'work', 'progress', 'wip'],
            'private' => ['priv', 'secret', 'hidden'],
            'pending' => ['wait', 'review', 'check']
        ];
        
        foreach ($keywords as $wp_status => $keyword_list) {
            foreach ($keyword_list as $keyword) {
                if (strpos($input, $keyword) !== false) {
                    return $wp_status;
                }
            }
        }
        
        return null;
    }

    /**
     * 清除自定义映射缓存
     *
     * @since 2.0.0-beta.2
     */
    public static function clearCache(): void {
        self::$custom_mapping_cache = null;
    }

    /**
     * 添加自定义状态映射
     *
     * @since 2.0.0-beta.2
     * @param string $notion_status Notion状态
     * @param string $wp_status WordPress状态
     * @return bool 是否添加成功
     */
    public static function addCustomMapping(string $notion_status, string $wp_status): bool {
        // 验证WordPress状态
        if (!self::isValidWordPressStatus($wp_status)) {
            return false;
        }
        
        // 获取当前选项
        $options = get_option('notion_to_wordpress_options', []);
        if (!isset($options['custom_status_mapping'])) {
            $options['custom_status_mapping'] = [];
        }
        
        // 添加映射
        $options['custom_status_mapping'][strtolower(trim($notion_status))] = $wp_status;
        
        // 保存选项
        $result = update_option('notion_to_wordpress_options', $options);
        
        // 清除缓存
        if ($result) {
            self::clearCache();
        }
        
        return $result;
    }

    /**
     * 移除自定义状态映射
     *
     * @since 2.0.0-beta.2
     * @param string $notion_status 要移除的Notion状态
     * @return bool 是否移除成功
     */
    public static function removeCustomMapping(string $notion_status): bool {
        $options = get_option('notion_to_wordpress_options', []);
        
        if (!isset($options['custom_status_mapping'])) {
            return true; // 没有自定义映射，视为成功
        }
        
        $key = strtolower(trim($notion_status));
        if (isset($options['custom_status_mapping'][$key])) {
            unset($options['custom_status_mapping'][$key]);
            
            $result = update_option('notion_to_wordpress_options', $options);
            
            if ($result) {
                self::clearCache();
            }
            
            return $result;
        }
        
        return true; // 映射不存在，视为成功
    }
}
