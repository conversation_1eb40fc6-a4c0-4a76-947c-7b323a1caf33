/**
 * 锚点导航React Hook
 * 
 * 将前端AnchorNavigation功能包装为React Hook，提供平滑滚动和锚点导航功能
 */

import { useEffect, useRef, useCallback, useState } from 'react';
import { AnchorNavigation, type AnchorNavigationConfig, type ScrollTarget } from '../../../frontend/components/AnchorNavigation';
import { on, off } from '../../../shared/core/EventBus';

// Hook配置选项
export interface UseAnchorNavigationOptions extends Partial<AnchorNavigationConfig> {
  enabled?: boolean;
  autoInit?: boolean;
  smoothScroll?: boolean;
  offset?: number;
}

// Hook返回值接口
export interface UseAnchorNavigationReturn {
  scrollToAnchor: (anchor: string, options?: { offset?: number; behavior?: ScrollBehavior }) => Promise<boolean>;
  scrollToElement: (element: HTMLElement, options?: { offset?: number; behavior?: ScrollBehavior }) => Promise<boolean>;
  getCurrentAnchor: () => string | null;
  getScrollTargets: () => ScrollTarget[];
  highlightElement: (element: HTMLElement, duration?: number) => void;
  updateHeaderOffset: () => void;
  isSupported: boolean;
}

/**
 * 锚点导航Hook
 */
export function useAnchorNavigation(options: UseAnchorNavigationOptions = {}): UseAnchorNavigationReturn {
  const {
    enabled = true,
    autoInit = true,
    ...navConfig
  } = options;

  const [isSupported, setIsSupported] = useState(false);
  const navigationRef = useRef<AnchorNavigation | null>(null);
  const mountedRef = useRef(true);

  // 安全更新状态
  const safeSetState = useCallback((updater: () => void) => {
    if (mountedRef.current) {
      updater();
    }
  }, []);

  // 初始化锚点导航
  const initNavigation = useCallback(() => {
    if (!enabled || navigationRef.current) return;

    try {
      navigationRef.current = AnchorNavigation.getInstance(navConfig);
      
      safeSetState(() => {
        setIsSupported('scrollBehavior' in document.documentElement.style);
      });

      console.log('⚓ [useAnchorNavigation] 锚点导航已初始化');
    } catch (error) {
      console.error('⚓ [useAnchorNavigation] 初始化失败:', error);
    }
  }, [enabled, navConfig, safeSetState]);

  // 滚动到锚点
  const scrollToAnchor = useCallback(async (
    anchor: string,
    _options?: { offset?: number; behavior?: ScrollBehavior }
  ): Promise<boolean> => {
    if (!navigationRef.current) return false;

    try {
      // AnchorNavigation.scrollToAnchor只接受一个参数
      const result = navigationRef.current.scrollToAnchor(anchor);
      return result;
    } catch (error) {
      console.error('⚓ [useAnchorNavigation] 滚动到锚点失败:', error);
      return false;
    }
  }, []);

  // 滚动到元素
  const scrollToElement = useCallback(async (
    element: HTMLElement,
    _options?: { offset?: number; behavior?: ScrollBehavior }
  ): Promise<boolean> => {
    if (!navigationRef.current || !element.id) return false;

    try {
      // 使用元素的ID作为锚点
      const anchor = `#${element.id}`;
      const result = navigationRef.current.scrollToAnchor(anchor);
      return result;
    } catch (error) {
      console.error('⚓ [useAnchorNavigation] 滚动到元素失败:', error);
      return false;
    }
  }, []);

  // 获取当前锚点
  const getCurrentAnchor = useCallback((): string | null => {
    // 从URL获取当前锚点
    return window.location.hash || null;
  }, []);

  // 获取滚动目标
  const getScrollTargets = useCallback((): ScrollTarget[] => {
    // 查找页面中的所有锚点元素
    const elements = document.querySelectorAll('[id^="notion-block-"]');
    return Array.from(elements).map(element => ({
      id: element.id,
      element: element as HTMLElement,
      rect: element.getBoundingClientRect(),
    }));
  }, []);

  // 高亮元素（简化实现）
  const highlightElement = useCallback((element: HTMLElement, duration = 2000) => {
    try {
      element.style.transition = 'background-color 0.3s ease';
      element.style.backgroundColor = 'rgba(255, 255, 0, 0.3)';

      setTimeout(() => {
        element.style.backgroundColor = '';
        setTimeout(() => {
          element.style.transition = '';
        }, 300);
      }, duration);
    } catch (error) {
      console.error('⚓ [useAnchorNavigation] 高亮元素失败:', error);
    }
  }, []);

  // 更新头部偏移（简化实现）
  const updateHeaderOffset = useCallback(() => {
    // 这个功能在AnchorNavigation类中是私有的，这里提供一个空实现
    console.log('⚓ [useAnchorNavigation] 更新头部偏移');
  }, []);

  // 事件监听器
  useEffect(() => {
    if (!enabled) return;

    // 监听锚点导航事件
    const handleNavigationInitialized = (event: any) => {
      const { smoothScrollSupported } = event.detail || event;
      safeSetState(() => {
        setIsSupported(smoothScrollSupported);
      });
    };

    const handleAnchorChanged = (event: any) => {
      const { anchor } = event.detail || event;
      console.log('⚓ [useAnchorNavigation] 锚点变化:', anchor);
    };

    const handleScrollCompleted = (event: any) => {
      const { target } = event.detail || event;
      if (target?.id) {
        console.log('⚓ [useAnchorNavigation] 滚动完成:', target.id);
      }
    };

    on('anchor:navigation:initialized', handleNavigationInitialized);
    on('anchor:changed', handleAnchorChanged);
    on('anchor:scroll:completed', handleScrollCompleted);

    return () => {
      off('anchor:navigation:initialized', handleNavigationInitialized);
      off('anchor:changed', handleAnchorChanged);
      off('anchor:scroll:completed', handleScrollCompleted);
    };
  }, [enabled, safeSetState]);

  // 自动初始化
  useEffect(() => {
    if (autoInit && enabled) {
      initNavigation();
    }
  }, [autoInit, enabled, initNavigation]);

  // 组件卸载清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      // 注意：不自动销毁导航器，因为它是单例，可能被其他组件使用
    };
  }, []);

  return {
    scrollToAnchor,
    scrollToElement,
    getCurrentAnchor,
    getScrollTargets,
    highlightElement,
    updateHeaderOffset,
    isSupported,
  };
}

/**
 * 锚点链接Hook
 * 
 * 专门用于处理锚点链接的点击事件
 */
export function useAnchorLink(href: string, options: UseAnchorNavigationOptions = {}) {
  const { scrollToAnchor } = useAnchorNavigation(options);
  const [isScrolling, setIsScrolling] = useState(false);

  const handleClick = useCallback(async (event: React.MouseEvent) => {
    event.preventDefault();
    
    // 提取锚点
    const anchor = href.startsWith('#') ? href.slice(1) : href;
    if (!anchor) return;

    setIsScrolling(true);
    try {
      await scrollToAnchor(anchor);
    } finally {
      setIsScrolling(false);
    }
  }, [href, scrollToAnchor]);

  return {
    handleClick,
    isScrolling,
  };
}

/**
 * 滚动监听Hook
 * 
 * 监听页面滚动，自动更新当前激活的锚点
 */
export function useScrollSpy(targets: string[] = [], options: UseAnchorNavigationOptions = {}) {
  const [activeAnchor, setActiveAnchor] = useState<string | null>(null);
  const { getScrollTargets } = useAnchorNavigation(options);

  useEffect(() => {
    if (targets.length === 0) return;

    const handleScroll = () => {
      const scrollTargets = getScrollTargets();
      const currentScrollY = window.scrollY + 100; // 偏移量

      // 找到当前可见的锚点
      let currentAnchor: string | null = null;
      
      for (const target of scrollTargets) {
        if (targets.includes(target.id) && target.rect.top <= currentScrollY) {
          currentAnchor = target.id;
        }
      }

      setActiveAnchor(currentAnchor);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // 初始检查

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [targets, getScrollTargets]);

  return {
    activeAnchor,
  };
}
