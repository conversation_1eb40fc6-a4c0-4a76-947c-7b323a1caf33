/**
 * 表格视图React组件
 * 
 * 支持动态列显示、排序、行选择和响应式设计
 */

import { useState, useMemo, useCallback } from 'react';
import type {
  TableViewProps,
  DatabaseRecord,
  DatabaseInfo,
  PropertyConfig
} from '../types';

// 排序配置接口
interface SortConfig {
  property: string;
  direction: 'asc' | 'desc';
}

// 表格头部组件Props
interface TableHeaderProps {
  properties: PropertyConfig[];
  sortConfig: SortConfig | null;
  onSort: (property: string) => void;
}

// 表格行组件Props
interface TableRowProps {
  record: DatabaseRecord;
  properties: PropertyConfig[];
  onClick?: (record: DatabaseRecord) => void;
  isSelected?: boolean;
}

// 表格单元格组件Props
interface TableCellProps {
  record: DatabaseRecord;
  property: PropertyConfig;
}

/**
 * 表格头部组件
 */
function TableHeader({ properties, sortConfig, onSort }: TableHeaderProps) {
  return (
    <div className="notion-table-header">
      {properties.map((property) => (
        <div
          key={property.name}
          className={`notion-table-header-cell ${sortConfig?.property === property.name ? 'sorted' : ''}`}
          style={{ width: property.width }}
          onClick={() => onSort(property.name)}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              onSort(property.name);
            }
          }}
        >
          <span className="header-text">{property.name}</span>
          {sortConfig?.property === property.name && (
            <span className="sort-indicator">
              {sortConfig.direction === 'asc' ? '↑' : '↓'}
            </span>
          )}
        </div>
      ))}
    </div>
  );
}

/**
 * 表格单元格组件
 */
function TableCell({ record, property }: TableCellProps) {
  const value = record.properties[property.name];
  
  // 格式化属性值
  const formatValue = useCallback((val: unknown, prop: PropertyConfig): string => {
    if (val === null || val === undefined) {
      return '';
    }

    switch (prop.type) {
      case 'title':
        return val?.title?.[0]?.plain_text || val?.plain_text || String(val);
      
      case 'rich_text':
        if (Array.isArray(val)) {
          return val.map(item => item.plain_text || '').join('');
        }
        return val?.plain_text || String(val);
      
      case 'number':
        return typeof val === 'number' ? val.toLocaleString() : String(val);
      
      case 'select':
        return val?.name || String(val);
      
      case 'multi_select':
        if (Array.isArray(val)) {
          return val.map(item => item.name || item).join(', ');
        }
        return String(val);
      
      case 'date':
        if (val?.start) {
          return new Date(val.start).toLocaleDateString();
        }
        return String(val);
      
      case 'checkbox':
        return val ? '✓' : '✗';
      
      case 'url':
        return val ? String(val) : '';
      
      case 'email':
        return val ? String(val) : '';
      
      case 'phone_number':
        return val ? String(val) : '';
      
      default:
        return String(val);
    }
  }, []);

  const formattedValue = formatValue(value, property);

  return (
    <div 
      className="notion-table-cell"
      style={{ width: property.width }}
      title={formattedValue}
    >
      {property.type === 'url' && formattedValue ? (
        <a 
          href={formattedValue} 
          target="_blank" 
          rel="noopener noreferrer"
          onClick={(e) => e.stopPropagation()}
        >
          {formattedValue}
        </a>
      ) : property.type === 'email' && formattedValue ? (
        <a 
          href={`mailto:${formattedValue}`}
          onClick={(e) => e.stopPropagation()}
        >
          {formattedValue}
        </a>
      ) : (
        <span className={`cell-content cell-type-${property.type}`}>
          {formattedValue}
        </span>
      )}
    </div>
  );
}

/**
 * 表格行组件
 */
function TableRow({ record, properties, onClick, isSelected = false }: TableRowProps) {
  const handleClick = useCallback(() => {
    onClick?.(record);
  }, [record, onClick]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  }, [handleClick]);

  return (
    <div
      className={`notion-table-row ${onClick ? 'notion-table-row-interactive' : ''} ${isSelected ? 'selected' : ''}`}
      onClick={handleClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? handleKeyDown : undefined}
      data-record-id={record.id}
    >
      {properties.map((property) => (
        <TableCell
          key={property.name}
          record={record}
          property={property}
        />
      ))}
    </div>
  );
}

/**
 * 获取可见属性
 */
function getVisibleProperties(databaseInfo: DatabaseInfo | null, options?: { showProperties?: string[]; hideProperties?: string[] }): PropertyConfig[] {
  if (!databaseInfo?.properties) {
    return [];
  }

  const properties: PropertyConfig[] = [];
  const showProperties = options?.showProperties || [];
  const hideProperties = options?.hideProperties || [];

  Object.entries(databaseInfo.properties).forEach(([name, config]: [string, { type?: string; [key: string]: unknown }]) => {
    // 检查是否应该显示此属性
    const shouldShow = showProperties.length === 0 || showProperties.includes(name);
    const shouldHide = hideProperties.includes(name);

    if (shouldShow && !shouldHide) {
      properties.push({
        name,
        type: config.type || 'text',
        visible: true,
        width: getPropertyWidth(config.type),
        format: getPropertyFormat(config.type),
      });
    }
  });

  return properties;
}

/**
 * 获取属性宽度
 */
function getPropertyWidth(type: string): string {
  switch (type) {
    case 'checkbox':
      return '60px';
    case 'number':
      return '100px';
    case 'date':
      return '120px';
    case 'select':
      return '150px';
    case 'title':
      return '200px';
    default:
      return '150px';
  }
}

/**
 * 获取属性格式
 */
function getPropertyFormat(type: string): string {
  return type;
}

/**
 * 表格视图主组件
 */
export function TableView({
  records,
  databaseInfo,
  options,
  sortConfig,
  onSort,
  onRecordClick,
  className = '',
  children,
  ...props
}: TableViewProps) {
  // 本地排序状态（如果没有外部排序配置）
  const [localSortConfig, setLocalSortConfig] = useState<SortConfig | null>(null);
  
  // 使用外部排序配置或本地排序配置
  const currentSortConfig = sortConfig || localSortConfig;

  // 获取可见属性
  const visibleProperties = useMemo(() => {
    return getVisibleProperties(databaseInfo, options);
  }, [databaseInfo, options]);

  // 排序记录
  const sortedRecords = useMemo(() => {
    if (!currentSortConfig) {
      return records;
    }

    return [...records].sort((a, b) => {
      const aValue = a.properties[currentSortConfig.property];
      const bValue = b.properties[currentSortConfig.property];
      
      // 处理空值
      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;
      
      // 根据类型进行比较
      let comparison = 0;
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        comparison = aValue.localeCompare(bValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue;
      } else {
        comparison = String(aValue).localeCompare(String(bValue));
      }
      
      return currentSortConfig.direction === 'asc' ? comparison : -comparison;
    });
  }, [records, currentSortConfig]);

  // 处理排序
  const handleSort = useCallback((property: string) => {
    if (onSort) {
      // 使用外部排序处理
      onSort(property);
    } else {
      // 使用本地排序
      setLocalSortConfig(prev => ({
        property,
        direction: prev?.property === property && prev.direction === 'asc' ? 'desc' : 'asc'
      }));
    }
  }, [onSort]);

  // 如果没有记录，显示空状态
  if (records.length === 0) {
    return (
      <div className={`notion-database-view-table ${className}`} {...props}>
        <div className="empty-table">
          <div className="empty-icon">📋</div>
          <div className="empty-message">暂无数据</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`notion-database-view-table ${className}`} {...props}>
      <div className="notion-database-table">
        <TableHeader
          properties={visibleProperties}
          sortConfig={currentSortConfig}
          onSort={handleSort}
        />
        <div className="notion-table-body">
          {sortedRecords.map((record) => (
            <TableRow
              key={record.id}
              record={record}
              properties={visibleProperties}
              onClick={onRecordClick}
            />
          ))}
        </div>
      </div>
      {children}
    </div>
  );
}

export default TableView;
