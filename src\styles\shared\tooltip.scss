/**
 * 工具提示样式 - SCSS版本
 * 
 * 从原有tooltip.css迁移，包括：
 * - 可定制的工具提示
 * - 跨浏览器兼容性
 * - 多种位置和主题
 * - 响应式设计
 */

@use "variables" as *;

// 工具提示变量
$tooltip-bg: $gray-800;
$tooltip-text: $white;
$tooltip-arrow-size: 6px;
$tooltip-max-width: 200px;
$tooltip-z-index: $z-index-tooltip;

// 基础工具提示样式
[data-tooltip] {
  position: relative;
  cursor: help;

  &:before,
  &:after {
    position: absolute;
    visibility: hidden;
    opacity: 0;
    pointer-events: none;
    transition: opacity $transition-fast, visibility $transition-fast;
    z-index: $tooltip-z-index;
  }

  // 工具提示内容
  &:before {
    content: attr(data-tooltip);
    background: $tooltip-bg;
    color: $tooltip-text;
    padding: $spacing-2 $spacing-3;
    border-radius: $border-radius-sm;
    font-size: $font-size-sm;
    line-height: $line-height-tight;
    white-space: nowrap;
    max-width: $tooltip-max-width;
    word-wrap: break-word;
    text-align: center;
    box-shadow: $shadow-md;
  }

  // 工具提示箭头
  &:after {
    content: '';
    width: 0;
    height: 0;
    border: $tooltip-arrow-size solid transparent;
  }

  // 显示工具提示
  &:hover:before,
  &:hover:after,
  &:focus:before,
  &:focus:after {
    visibility: visible;
    opacity: 1;
  }
}

// 顶部工具提示（默认）
[data-tooltip]:not([data-tooltip-pos]),
[data-tooltip][data-tooltip-pos="top"] {
  &:before {
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: $tooltip-arrow-size;
  }

  &:after {
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-top-color: $tooltip-bg;
  }
}

// 底部工具提示
[data-tooltip][data-tooltip-pos="bottom"] {
  &:before {
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: $tooltip-arrow-size;
  }

  &:after {
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-bottom-color: $tooltip-bg;
  }
}

// 左侧工具提示
[data-tooltip][data-tooltip-pos="left"] {
  &:before {
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
    margin-right: $tooltip-arrow-size;
  }

  &:after {
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
    border-left-color: $tooltip-bg;
  }
}

// 右侧工具提示
[data-tooltip][data-tooltip-pos="right"] {
  &:before {
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    margin-left: $tooltip-arrow-size;
  }

  &:after {
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border-right-color: $tooltip-bg;
  }
}

// 工具提示主题变体
[data-tooltip][data-tooltip-theme="light"] {
  &:before {
    background: $white;
    color: $text-primary;
    border: 1px solid $border-color;
  }

  &[data-tooltip-pos="top"]:after {
    border-top-color: $white;
  }

  &[data-tooltip-pos="bottom"]:after {
    border-bottom-color: $white;
  }

  &[data-tooltip-pos="left"]:after {
    border-left-color: $white;
  }

  &[data-tooltip-pos="right"]:after {
    border-right-color: $white;
  }
}

[data-tooltip][data-tooltip-theme="error"] {
  &:before {
    background: $error-color;
    color: $white;
  }

  &[data-tooltip-pos="top"]:after {
    border-top-color: $error-color;
  }

  &[data-tooltip-pos="bottom"]:after {
    border-bottom-color: $error-color;
  }

  &[data-tooltip-pos="left"]:after {
    border-left-color: $error-color;
  }

  &[data-tooltip-pos="right"]:after {
    border-right-color: $error-color;
  }
}

[data-tooltip][data-tooltip-theme="success"] {
  &:before {
    background: $success-color;
    color: $white;
  }

  &[data-tooltip-pos="top"]:after {
    border-top-color: $success-color;
  }

  &[data-tooltip-pos="bottom"]:after {
    border-bottom-color: $success-color;
  }

  &[data-tooltip-pos="left"]:after {
    border-left-color: $success-color;
  }

  &[data-tooltip-pos="right"]:after {
    border-right-color: $success-color;
  }
}

// 多行工具提示
[data-tooltip][data-tooltip-multiline] {
  &:before {
    white-space: pre-line;
    max-width: $tooltip-max-width;
    text-align: left;
  }
}

// 响应式设计
@media (max-width: $breakpoint-sm) {
  [data-tooltip] {
    &:before {
      font-size: $font-size-xs;
      padding: $spacing-1 $spacing-2;
      max-width: 150px;
    }

    &:after {
      border-width: 4px;
    }
  }

  // 在小屏幕上简化工具提示位置
  [data-tooltip][data-tooltip-pos="left"],
  [data-tooltip][data-tooltip-pos="right"] {
    &:before {
      bottom: 100%;
      left: 50%;
      right: auto;
      top: auto;
      transform: translateX(-50%);
      margin: 0 0 4px 0;
    }

    &:after {
      bottom: 100%;
      left: 50%;
      right: auto;
      top: auto;
      transform: translateX(-50%);
      border: 4px solid transparent;
      border-top-color: $tooltip-bg;
    }
  }
}

// 工具提示动画增强
@media (prefers-reduced-motion: no-preference) {
  [data-tooltip] {
    &:before,
    &:after {
      transition: opacity $transition-base, 
                  visibility $transition-base,
                  transform $transition-base;
    }

    &:hover:before,
    &:focus:before {
      transform: translateX(-50%) translateY(-2px);
    }

    &[data-tooltip-pos="bottom"]:hover:before,
    &[data-tooltip-pos="bottom"]:focus:before {
      transform: translateX(-50%) translateY(2px);
    }

    &[data-tooltip-pos="left"]:hover:before,
    &[data-tooltip-pos="left"]:focus:before {
      transform: translateY(-50%) translateX(-2px);
    }

    &[data-tooltip-pos="right"]:hover:before,
    &[data-tooltip-pos="right"]:focus:before {
      transform: translateY(-50%) translateX(2px);
    }
  }
}