<?php
declare(strict_types=1);

namespace NTWP\Core\Foundation;

/**
 * 服务提供者类
 *
 * 负责注册和启动所有核心服务，
 * 管理服务的生命周期和依赖关系。
 *
 * 职责范围：
 * - 服务注册和绑定
 * - 依赖关系配置
 * - 服务启动和初始化
 * - 生命周期管理
 * - 性能监控集成
 *
 * @since      2.0.0-beta.2
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-<PERSON>ong/Notion-to-WordPress
 */

// 如果直接访问此文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

class ServiceProvider {

    /**
     * 服务注册状态
     * 
     * @since 2.0.0-beta.2
     */
    private static bool $registered = false;

    /**
     * 服务启动状态
     * 
     * @since 2.0.0-beta.2
     */
    private static bool $booted = false;

    /**
     * 注册的服务列表
     * 
     * @since 2.0.0-beta.2
     */
    private static array $services = [];

    /**
     * 注册所有核心服务
     *
     * @since 2.0.0-beta.2
     */
    public static function register(): void {
        if (self::$registered) {
            return;
        }

        // 注册核心服务到Container
        self::registerCoreServices();
        
        // 注册基础设施服务
        self::registerInfrastructureServices();
        
        // 注册业务服务
        self::registerBusinessServices();
        
        // 注册工具服务
        self::registerUtilityServices();

        self::$registered = true;
        
        // 记录注册完成
        if (class_exists('\\NTWP\\Core\\Foundation\\Logger')) {
            Logger::infoLog('所有服务注册完成', 'Service Provider');
        }
    }

    /**
     * 启动所有服务
     *
     * @since 2.0.0-beta.2
     */
    public static function boot(): void {
        if (self::$booted) {
            return;
        }

        // 确保服务已注册
        if (!self::$registered) {
            self::register();
        }

        // 预热关键服务
        self::warmupCriticalServices();
        
        // 初始化性能监控
        self::initializePerformanceMonitoring();
        
        // 注册WordPress钩子
        self::registerWordPressHooks();

        self::$booted = true;
        
        // 记录启动完成
        if (class_exists('\\NTWP\\Core\\Foundation\\Logger')) {
            Logger::infoLog('所有服务启动完成', 'Service Provider');
        }
    }

    /**
     * 注册核心服务
     *
     * @since 2.0.0-beta.2
     */
    private static function registerCoreServices(): void {
        // NotionApi服务
        Container::bind('notion_api', function() {
            return ServiceFactory::createNotionApi();
        }, true);

        // ImportService服务
        Container::bind('import_service', function() {
            return ServiceFactory::createImportService();
        }, true);

        // ContentConverter服务
        Container::bind('content_converter', function() {
            return ServiceFactory::createContentConverter();
        }, true);

        // ImportHandler服务
        Container::bind('import_handler', function() {
            return ServiceFactory::createImportHandler();
        }, true);

        self::$services['core'] = ['notion_api', 'import_service', 'content_converter', 'import_handler'];
    }

    /**
     * 注册基础设施服务
     *
     * @since 2.0.0-beta.2
     */
    private static function registerInfrastructureServices(): void {
        // 缓存管理器（统一缓存）
        Container::bind('cache_manager', function() {
            return new \NTWP\Infrastructure\Cache\CacheManager();
        }, true);

        // 数据库管理器（避免与Container中的重复）
        if (!Container::bound('database_manager')) {
            Container::bind('database_manager', function() {
                return new \NTWP\Infrastructure\Database\DatabaseManager();
            }, true);
        }

        // 内存管理器
        Container::bind('memory_manager', function() {
            return new \NTWP\Infrastructure\Memory\MemoryManager();
        }, true);

        // 并发管理器
        Container::bind('concurrency_manager', function() {
            return new \NTWP\Infrastructure\Concurrency\ConcurrencyManager();
        }, true);

        self::$services['infrastructure'] = ['cache_manager', 'database_manager', 'memory_manager', 'concurrency_manager'];
    }

    /**
     * 注册业务服务
     *
     * @since 2.0.0-beta.2
     */
    private static function registerBusinessServices(): void {
        // 同步管理器（静态类，不需要实例化）
        Container::bind('sync_manager', function() {
            return \NTWP\Services\Sync\SyncManager::class;
        });

        // 任务服务
        Container::bind('task_service', function() {
            return new \NTWP\Services\TaskService();
        }, true);

        self::$services['business'] = ['sync_manager', 'task_service'];
    }

    /**
     * 注册工具服务
     *
     * @since 2.0.0-beta.2
     */
    private static function registerUtilityServices(): void {
        // 状态映射器（静态类）
        Container::bind('status_mapper', function() {
            return \NTWP\Utils\StatusMapper::class;
        });

        // WordPress助手（静态类）
        Container::bind('wordpress_helper', function() {
            return \NTWP\Utils\WordPressHelper::class;
        });

        // 验证器
        Container::bind('validator', function() {
            return new \NTWP\Utils\Validator();
        }, true);

        self::$services['utility'] = ['status_mapper', 'wordpress_helper', 'validator'];
    }

    /**
     * 预热关键服务
     *
     * @since 2.0.0-beta.2
     */
    private static function warmupCriticalServices(): void {
        try {
            // 🚀 修复：通过Container预热服务，确保使用统一的实例管理
            $critical_services = ['notion_api', 'import_service', 'content_converter', 'cache_manager'];

            foreach ($critical_services as $service) {
                if (Container::bound($service)) {
                    Container::resolve($service);

                    if (class_exists('\\NTWP\\Core\\Foundation\\Logger')) {
                        Logger::debugLog("预热服务: {$service}", 'Service Provider');
                    }
                }
            }

            // 记录预热完成
            if (class_exists('\\NTWP\\Core\\Foundation\\Logger')) {
                Logger::infoLog('关键服务预热完成', 'Service Provider');
            }

        } catch (\Exception $e) {
            if (class_exists('\\NTWP\\Core\\Foundation\\Logger')) {
                Logger::warningLog('服务预热失败: ' . $e->getMessage(), 'Service Provider');
            }
        }
    }

    /**
     * 初始化性能监控
     *
     * @since 2.0.0-beta.2
     */
    private static function initializePerformanceMonitoring(): void {
        // 初始化增强性能监控
        if (class_exists('\\NTWP\\Core\\Performance\\PerformanceMonitor')) {
            \NTWP\Core\Performance\PerformanceMonitor::initEnhancedMonitoring();
        }

        // 注册性能监控钩子
        add_action('wp_footer', function() {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                $stats = ServiceFactory::getPerformanceStats();
                if ($stats['service_creation_count'] > 0) {
                    echo '<!-- Notion-to-WordPress Service Performance: ' .
                         json_encode($stats) . ' -->';
                }
            }
        });
    }

    /**
     * 注册WordPress钩子
     *
     * @since 2.0.0-beta.2
     */
    private static function registerWordPressHooks(): void {
        // 插件停用时清理服务
        register_deactivation_hook(NOTION_TO_WORDPRESS_FILE ?? __FILE__, function() {
            self::cleanup();
        });

        // 定期清理服务缓存
        add_action('wp_scheduled_delete', function() {
            ServiceFactory::clearCache();
        });
    }

    /**
     * 获取已注册的服务列表
     *
     * @since 2.0.0-beta.2
     * @return array 服务列表
     */
    public static function getRegisteredServices(): array {
        return self::$services;
    }

    /**
     * 检查服务是否已注册
     *
     * @since 2.0.0-beta.2
     * @param string $service_name 服务名称
     * @return bool 是否已注册
     */
    public static function isServiceRegistered(string $service_name): bool {
        foreach (self::$services as $category => $services) {
            if (in_array($service_name, $services, true)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取服务注册状态
     *
     * @since 2.0.0-beta.2
     * @return array 状态信息
     */
    public static function getStatus(): array {
        return [
            'registered' => self::$registered,
            'booted' => self::$booted,
            'services_count' => array_sum(array_map('count', self::$services)),
            'services' => self::$services,
            'performance' => ServiceFactory::getPerformanceStats()
        ];
    }

    /**
     * 清理所有服务
     *
     * @since 2.0.0-beta.2
     */
    public static function cleanup(): void {
        // 清理ServiceFactory缓存
        ServiceFactory::clearCache();
        
        // 清理Container
        Container::flush();
        
        // 重置状态
        self::$registered = false;
        self::$booted = false;
        self::$services = [];
        
        if (class_exists('\\NTWP\\Core\\Foundation\\Logger')) {
            Logger::infoLog('服务清理完成', 'Service Provider');
        }
    }

    /**
     * 重新启动服务系统
     *
     * @since 2.0.0-beta.2
     */
    public static function restart(): void {
        self::cleanup();
        self::register();
        self::boot();
    }

    /**
     * 验证架构完整性
     *
     * @since 2.0.0-beta.2
     * @return array 验证结果
     */
    public static function validateArchitecture(): array {
        $issues = [];
        $warnings = [];

        // 检查核心服务是否已注册
        $required_services = ['notion_api', 'import_service', 'content_converter', 'import_handler'];
        foreach ($required_services as $service) {
            if (!Container::bound($service)) {
                $issues[] = "核心服务 '{$service}' 未注册";
            }
        }

        // 检查基础设施服务
        $infrastructure_services = ['cache_manager', 'database_manager', 'memory_manager'];
        foreach ($infrastructure_services as $service) {
            if (!Container::bound($service)) {
                $warnings[] = "基础设施服务 '{$service}' 未注册";
            }
        }

        // 检查服务是否可以正常解析
        foreach ($required_services as $service) {
            if (Container::bound($service)) {
                try {
                    Container::resolve($service);
                } catch (\Exception $e) {
                    $issues[] = "服务 '{$service}' 解析失败: " . $e->getMessage();
                }
            }
        }

        // 检查ServiceFactory和Container的一致性
        if (class_exists('\\NTWP\\Core\\Foundation\\ServiceFactory')) {
            $factory_stats = ServiceFactory::getPerformanceStats();
            if ($factory_stats['service_creation_count'] === 0) {
                $warnings[] = 'ServiceFactory未创建任何服务，可能存在架构问题';
            }
        }

        return [
            'status' => empty($issues) ? 'healthy' : 'issues_found',
            'issues' => $issues,
            'warnings' => $warnings,
            'registered_services' => Container::bound('') ? [] : array_keys(Container::get_services()),
            'service_provider_status' => [
                'registered' => self::$registered,
                'booted' => self::$booted,
                'services_count' => array_sum(array_map('count', self::$services))
            ]
        ];
    }
}
