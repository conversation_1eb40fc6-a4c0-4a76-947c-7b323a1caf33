/**
 * 数据库工具栏组件
 * 
 * 提供视图类型选择、搜索、过滤、排序和刷新功能
 */

import { useCallback, useEffect } from 'react';
import { Select, Input } from '../Input';
import { Button, IconButton } from '../Button';
import { useDebounce } from '../../hooks/useDebounce';
import type { DatabaseToolbarProps, ViewType } from './types';

// 视图类型选项
const VIEW_TYPE_OPTIONS = [
  { value: 'table', label: '表格视图' },
  { value: 'list', label: '列表视图' },
  { value: 'gallery', label: '画廊视图' },
  { value: 'board', label: '看板视图' },
  { value: 'calendar', label: '日历视图' },
  { value: 'timeline', label: '时间线视图' },
];

/**
 * 数据库工具栏组件
 */
export function DatabaseToolbar({
  viewType,
  searchTerm,
  enableSearch = true,
  enableFilter = true,
  enableSort = true,
  enableRefresh = true,
  isLoading = false,
  recordCount = 0,
  onViewTypeChange,
  onSearchChange,
  onFilterClick,
  onSortClick,
  onRefreshClick,
  className = '',
  children,
  ...props
}: DatabaseToolbarProps) {
  // 防抖搜索
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // 当防抖搜索词变化时触发搜索
  useEffect(() => {
    if (debouncedSearchTerm !== searchTerm) {
      onSearchChange(debouncedSearchTerm);
    }
  }, [debouncedSearchTerm, searchTerm, onSearchChange]);

  // 处理视图类型变化
  const handleViewTypeChange = useCallback((value: string) => {
    onViewTypeChange(value as ViewType);
  }, [onViewTypeChange]);

  // 处理搜索输入变化
  const handleSearchInputChange = useCallback((value: string) => {
    // 立即更新输入框显示，但通过防抖延迟触发实际搜索
    onSearchChange(value);
  }, [onSearchChange]);

  // 处理过滤按钮点击
  const handleFilterClick = useCallback(() => {
    onFilterClick?.();
  }, [onFilterClick]);

  // 处理排序按钮点击
  const handleSortClick = useCallback(() => {
    onSortClick?.();
  }, [onSortClick]);

  // 处理刷新按钮点击
  const handleRefreshClick = useCallback(() => {
    onRefreshClick?.();
  }, [onRefreshClick]);

  return (
    <div className={`database-toolbar ${className}`} {...props}>
      {/* 左侧工具 */}
      <div className="toolbar-left">
        {/* 视图类型选择器 */}
        <Select
          value={viewType}
          options={VIEW_TYPE_OPTIONS}
          onChange={handleViewTypeChange}
          className="view-type-selector"
          disabled={isLoading}
        />

        {/* 搜索输入框 */}
        {enableSearch && (
          <Input
            type="text"
            value={searchTerm}
            placeholder="搜索记录..."
            onChange={handleSearchInputChange}
            className="search-input"
            disabled={isLoading}
          />
        )}

        {/* 过滤按钮 */}
        {enableFilter && (
          <Button
            variant="secondary"
            size="medium"
            onClick={handleFilterClick}
            disabled={isLoading}
            className="filter-button"
            title="过滤记录"
          >
            <span className="dashicons dashicons-filter" style={{ marginRight: '4px' }} />
            过滤
          </Button>
        )}

        {/* 排序按钮 */}
        {enableSort && (
          <Button
            variant="secondary"
            size="medium"
            onClick={handleSortClick}
            disabled={isLoading}
            className="sort-button"
            title="排序记录"
          >
            <span className="dashicons dashicons-sort" style={{ marginRight: '4px' }} />
            排序
          </Button>
        )}
      </div>

      {/* 右侧工具 */}
      <div className="toolbar-right">
        {/* 刷新按钮 */}
        {enableRefresh && (
          <IconButton
            variant="secondary"
            size="medium"
            icon="update"
            onClick={handleRefreshClick}
            loading={isLoading}
            disabled={isLoading}
            className="refresh-button"
            title="刷新数据"
          />
        )}

        {/* 状态指示器 */}
        <div className="status-indicator">
          {isLoading && (
            <span className="loading-spinner">
              <span className="spinner is-active" style={{ float: 'none', marginRight: '8px' }} />
            </span>
          )}
          <span className="record-count">
            {recordCount} 条记录
          </span>
        </div>
      </div>

      {/* 自定义内容 */}
      {children}
    </div>
  );
}

export default DatabaseToolbar;
