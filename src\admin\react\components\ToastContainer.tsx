/**
 * Toast通知容器组件
 */

import { useEffect } from 'react';
import { useToasts, useAppActions } from '../context/AppContext';
import type { ToastMessage } from '../types';

// 单个Toast组件
function Toast({ toast }: { toast: ToastMessage }) {
  const { hideToast } = useAppActions();

  useEffect(() => {
    if (toast.duration !== 0) {
      const timer = setTimeout(() => {
        hideToast(toast.id);
      }, toast.duration || 5000);

      return () => clearTimeout(timer);
    }
    return undefined;
  }, [toast.id, toast.duration, hideToast]);

  const getToastIcon = () => {
    switch (toast.type) {
      case 'success':
        return 'yes-alt';
      case 'error':
        return 'dismiss';
      case 'warning':
        return 'warning';
      case 'info':
      default:
        return 'info';
    }
  };

  const getToastClass = () => {
    return `notice notice-${toast.type} is-dismissible notion-wp-toast`;
  };

  return (
    <div className={getToastClass()}>
      <div className="toast-content">
        <span className={`dashicons dashicons-${getToastIcon()}`} />
        <div className="toast-message">
          {toast.title && <strong>{toast.title}</strong>}
          <p>{toast.message}</p>
        </div>
      </div>
      
      {toast.actions && toast.actions.length > 0 && (
        <div className="toast-actions">
          {toast.actions.map((action, index) => (
            <button
              key={index}
              type="button"
              className="button button-small"
              onClick={action.onClick}
            >
              {action.label}
            </button>
          ))}
        </div>
      )}
      
      <button
        type="button"
        className="notice-dismiss"
        onClick={() => hideToast(toast.id)}
      >
        <span className="screen-reader-text">关闭通知</span>
      </button>
    </div>
  );
}

// Toast容器组件
export function ToastContainer() {
  const toasts = useToasts();

  if (toasts.length === 0) {
    return null;
  }

  return (
    <div className="notion-wp-toast-container">
      {toasts.map(toast => (
        <Toast key={toast.id} toast={toast} />
      ))}
    </div>
  );
}
