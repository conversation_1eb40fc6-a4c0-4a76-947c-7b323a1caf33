<?php
declare(strict_types=1);

namespace NTWP\Utils;

/**
 * 网络错误重试机制类。
 *
 * 提供智能的网络错误重试功能，支持指数退避策略和错误类型分类。
 *
 * @since      1.9.0-beta.1
 * @version    2.0.0-beta.2
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-<PERSON>ong/Notion-to-WordPress
 */

// 如果直接访问此文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

class NetworkRetry {

    /**
     * 初始化cURL常量（如果未定义）
     */
    private static function initCurlConstants() {
        if (!defined('CURLE_OPERATION_TIMEOUTED')) define('CURLE_OPERATION_TIMEOUTED', 28);
        if (!defined('CURLE_COULDNT_CONNECT')) define('CURLE_COULDNT_CONNECT', 7);
        if (!defined('CURLE_COULDNT_RESOLVE_HOST')) define('CURLE_COULDNT_RESOLVE_HOST', 6);
        if (!defined('CURLE_RECV_ERROR')) define('CURLE_RECV_ERROR', 56);
        if (!defined('CURLE_SEND_ERROR')) define('CURLE_SEND_ERROR', 55);
        if (!defined('CURLE_GOT_NOTHING')) define('CURLE_GOT_NOTHING', 52);
        if (!defined('CURLE_PARTIAL_FILE')) define('CURLE_PARTIAL_FILE', 18);
        if (!defined('CURLE_URL_MALFORMAT')) define('CURLE_URL_MALFORMAT', 3);
        if (!defined('CURLE_UNSUPPORTED_PROTOCOL')) define('CURLE_UNSUPPORTED_PROTOCOL', 1);
        if (!defined('CURLE_SSL_CONNECT_ERROR')) define('CURLE_SSL_CONNECT_ERROR', 35);
        if (!defined('CURLE_SSL_PEER_CERTIFICATE')) define('CURLE_SSL_PEER_CERTIFICATE', 51);
    }

    /**
     * 初始化错误类型数组
     */
    private static function initErrorArrays() {
        self::initCurlConstants();

        if (self::$temporary_errors === null) {
            self::$temporary_errors = [
                // cURL错误
                CURLE_OPERATION_TIMEOUTED,      // 28 - 操作超时
                CURLE_COULDNT_CONNECT,          // 7  - 无法连接
                CURLE_COULDNT_RESOLVE_HOST,     // 6  - 无法解析主机
                CURLE_RECV_ERROR,               // 56 - 接收数据错误
                CURLE_SEND_ERROR,               // 55 - 发送数据错误
                CURLE_GOT_NOTHING,              // 52 - 服务器未返回任何内容
                CURLE_PARTIAL_FILE,             // 18 - 文件传输不完整

                // HTTP状态码
                429, // Too Many Requests
                500, // Internal Server Error
                502, // Bad Gateway
                503, // Service Unavailable
                504, // Gateway Timeout
            ];
        }

        if (self::$permanent_errors === null) {
            self::$permanent_errors = [
                // HTTP状态码
                400, // Bad Request
                401, // Unauthorized
                403, // Forbidden
                404, // Not Found
                405, // Method Not Allowed
                406, // Not Acceptable
                410, // Gone
                422, // Unprocessable Entity

                // cURL错误
                CURLE_URL_MALFORMAT,            // 3  - URL格式错误
                CURLE_UNSUPPORTED_PROTOCOL,     // 1  - 不支持的协议
                CURLE_SSL_CONNECT_ERROR,        // 35 - SSL连接错误
                CURLE_SSL_PEER_CERTIFICATE,     // 51 - SSL证书验证失败
            ];
        }
    }

    /**
     * 默认最大重试次数
     *
     * @since    1.1.2
     * @access   public
     * @var      int    DEFAULT_MAX_RETRIES    默认最大重试次数
     */
    const DEFAULT_MAX_RETRIES = 3;

    /**
     * 默认基础延迟时间（毫秒）
     *
     * @since    1.1.2
     * @access   public
     * @var      int    DEFAULT_BASE_DELAY    默认基础延迟时间
     */
    const DEFAULT_BASE_DELAY = 1000;

    /**
     * 临时性错误类型
     *
     * @since    1.1.2
     * @access   private
     * @var      array    $temporary_errors    临时性错误类型数组
     */
    private static $temporary_errors;

    /**
     * 永久性错误类型
     *
     * @since    1.1.2
     * @access   private
     * @var      array    $permanent_errors    永久性错误类型数组
     */
    private static $permanent_errors;

    /**
     * 重试统计信息
     *
     * @since    1.1.2
     * @access   private
     * @var      array    $retry_stats    重试统计信息
     */
    private static $retry_stats = [
        'total_attempts' => 0,
        'successful_retries' => 0,
        'failed_retries' => 0,
        'permanent_errors' => 0,
        'total_delay_time' => 0,
        'smart_retries' => 0,
        'rate_limit_retries' => 0,
        'server_error_retries' => 0,
        'network_error_retries' => 0,
        'avg_delay_time' => 0
    ];

    /**
     * 带重试机制执行回调函数
     *
     * @since    1.1.2
     * @param    callable    $callback       要重试的回调函数
     * @param    int         $max_retries    最大重试次数
     * @param    int         $base_delay     基础延迟时间（毫秒）
     * @return   mixed                       回调函数的返回值
     * @throws   Exception                   如果所有重试都失败
     */
    public static function withRetry(callable $callback, $max_retries = self::DEFAULT_MAX_RETRIES, $base_delay = self::DEFAULT_BASE_DELAY) {
        // 初始化错误数组
        self::initErrorArrays();

        $last_exception = null;
        $start_time = microtime(true);
        
        for ($attempt = 0; $attempt <= $max_retries; $attempt++) {
            self::$retry_stats['total_attempts']++;
            
            try {
                $result = call_user_func($callback);
                
                // 成功执行
                if ($attempt > 0) {
                    self::$retry_stats['successful_retries']++;
                    
                    \NTWP\Core\Foundation\Logger::debugLog(
                        sprintf(
                            '重试成功：第 %d 次尝试成功，总耗时: %.2f秒',
                            $attempt + 1,
                            microtime(true) - $start_time
                        ),
                        'Network Retry'
                    );
                }

                // 集成性能监控数据（成功情况）
                self::integrateWithPerformanceMonitor();

                return $result;
                
            } catch (Exception $e) {
                $last_exception = $e;
                
                // 检查是否为永久性错误（使用增强版本）
                if (self::isPermanentErrorEnhanced($e)) {
                    self::$retry_stats['permanent_errors']++;
                    
                    \NTWP\Core\Foundation\Logger::debugLog(
                        sprintf(
                            '检测到永久性错误，停止重试: %s',
                            $e->getMessage()
                        ),
                        'Network Retry'
                    );
                    
                    throw $e;
                }
                
                // 如果已达到最大重试次数
                if ($attempt >= $max_retries) {
                    self::$retry_stats['failed_retries']++;
                    
                    \NTWP\Core\Foundation\Logger::errorLog(
                        sprintf(
                            '重试失败：已达到最大重试次数 %d，最后错误: %s',
                            $max_retries,
                            $e->getMessage()
                        ),
                        'Network Retry'
                    );
                    
                    break;
                }
                
                // 使用智能延迟计算
                $delay = self::calculateSmartDelay($attempt, $e, $base_delay);
                self::$retry_stats['total_delay_time'] += $delay;
                self::$retry_stats['smart_retries']++;

                // 记录错误类型统计
                self::recordErrorTypeStats($e);
                
                \NTWP\Core\Foundation\Logger::debugLog(
                    sprintf(
                        '重试 %d/%d 失败: %s，%d毫秒后重试',
                        $attempt + 1,
                        $max_retries,
                        $e->getMessage(),
                        $delay
                    ),
                    'Network Retry'
                );
                
                // 延迟执行
                usleep($delay * 1000); // 转换为微秒
            }
        }

        // 集成性能监控数据
        self::integrateWithPerformanceMonitor();

        // 所有重试都失败，抛出最后一个异常
        throw $last_exception;
    }

    /**
     * 检查是否为永久性错误
     *
     * @since    1.1.2
     * @param    Exception   $exception      异常对象
     * @return   bool                        是否为永久性错误
     */
    public static function isPermanentError($exception) {
        self::initErrorArrays();
        $message = $exception->getMessage();
        
        // 检查HTTP状态码
        if (preg_match('/HTTP\/\d\.\d\s+(\d+)/', $message, $matches)) {
            $status_code = (int)$matches[1];
            return in_array($status_code, self::$permanent_errors);
        }
        
        // 检查cURL错误码
        if (preg_match('/cURL error (\d+)/', $message, $matches)) {
            $curl_code = (int)$matches[1];
            return in_array($curl_code, self::$permanent_errors);
        }
        
        // 检查特定的错误消息
        $permanent_error_messages = [
            'Unauthorized',
            'Forbidden',
            'Not Found',
            'Method Not Allowed',
            'Unprocessable Entity',
            'Bad Request',
            'SSL certificate',
            'SSL connection',
            'URL malformed',
            'Unsupported protocol'
        ];
        
        foreach ($permanent_error_messages as $error_msg) {
            if (stripos($message, $error_msg) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查是否为临时性错误
     *
     * @since    1.1.2
     * @param    Exception   $exception      异常对象
     * @return   bool                        是否为临时性错误
     */
    public static function isTemporaryError($exception) {
        self::initErrorArrays();
        $message = $exception->getMessage();
        
        // 检查HTTP状态码
        if (preg_match('/HTTP\/\d\.\d\s+(\d+)/', $message, $matches)) {
            $status_code = (int)$matches[1];
            return in_array($status_code, self::$temporary_errors);
        }
        
        // 检查cURL错误码
        if (preg_match('/cURL error (\d+)/', $message, $matches)) {
            $curl_code = (int)$matches[1];
            return in_array($curl_code, self::$temporary_errors);
        }
        
        // 检查特定的错误消息
        $temporary_error_messages = [
            'timeout',
            'connection',
            'resolve host',
            'receive',
            'send',
            'got nothing',
            'partial file',
            'Too Many Requests',
            'Internal Server Error',
            'Bad Gateway',
            'Service Unavailable',
            'Gateway Timeout'
        ];
        
        foreach ($temporary_error_messages as $error_msg) {
            if (stripos($message, $error_msg) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取重试统计信息
     *
     * @since    1.1.2
     * @return   array                        重试统计信息
     */
    public static function getRetryStats() {
        return self::$retry_stats;
    }

    /**
     * 重置重试统计信息
     *
     * @since    1.1.2
     */
    public static function resetRetryStats() {
        self::$retry_stats = [
            'total_attempts' => 0,
            'successful_retries' => 0,
            'failed_retries' => 0,
            'permanent_errors' => 0,
            'total_delay_time' => 0,
            'smart_retries' => 0,
            'error_type_stats' => []
        ];
    }

    /**
     * 添加临时错误类型
     *
     * @since    1.1.2
     * @param    int         $error_code     错误码
     */
    public static function addTemporaryError($error_code) {
        self::initErrorArrays();
        
        if (!in_array($error_code, self::$temporary_errors)) {
            self::$temporary_errors[] = $error_code;
            
            \NTWP\Core\Foundation\Logger::debugLog(
                sprintf('添加临时错误类型: %d', $error_code),
                'Network Retry'
            );
        }
    }

    /**
     * 添加永久错误类型
     *
     * @since    1.1.2
     * @param    int         $error_code     错误码
     */
    public static function addPermanentError($error_code) {
        self::initErrorArrays();
        
        if (!in_array($error_code, self::$permanent_errors)) {
            self::$permanent_errors[] = $error_code;
            
            \NTWP\Core\Foundation\Logger::debugLog(
                sprintf('添加永久错误类型: %d', $error_code),
                'Network Retry'
            );
        }
    }

    /**
     * 带重试机制的wp_remote_request调用
     *
     * @since    1.1.2
     * @param    string      $url            请求URL
     * @param    array       $args           请求参数
     * @param    int         $max_retries    最大重试次数
     * @param    int         $base_delay     基础延迟时间（毫秒）
     * @return   array|WP_Error              响应结果
     */
    public static function wpRemoteRequestWithRetry($url, $args = [], $max_retries = self::DEFAULT_MAX_RETRIES, $base_delay = self::DEFAULT_BASE_DELAY) {
        return self::withRetry(function() use ($url, $args) {
            $response = wp_remote_request($url, $args);
            
            if (is_wp_error($response)) {
                throw new Exception($response->get_error_message(), $response->get_error_code());
            }
            
            $status_code = wp_remote_retrieve_response_code($response);
            if ($status_code >= 400) {
                throw new Exception("HTTP错误 {$status_code}: " . wp_remote_retrieve_response_message($response));
            }
            
            return $response;
        }, $max_retries, $base_delay);
    }

    /**
     * 带重试机制的wp_remote_get调用
     *
     * @since    1.1.2
     * @param    string      $url            请求URL
     * @param    array       $args           请求参数
     * @param    int         $max_retries    最大重试次数
     * @param    int         $base_delay     基础延迟时间（毫秒）
     * @return   array|WP_Error              响应结果
     */
    public static function wpRemoteGetWithRetry($url, $args = [], $max_retries = self::DEFAULT_MAX_RETRIES, $base_delay = self::DEFAULT_BASE_DELAY) {
        $args['method'] = 'GET';
        return self::wpRemoteRequestWithRetry($url, $args, $max_retries, $base_delay);
    }

    /**
     * 带重试机制的wp_remote_post调用
     *
     * @since    1.1.2
     * @param    string      $url            请求URL
     * @param    array       $args           请求参数
     * @param    int         $max_retries    最大重试次数
     * @param    int         $base_delay     基础延迟时间（毫秒）
     * @return   array|WP_Error              响应结果
     */
    public static function wpRemotePostWithRetry($url, $args = [], $max_retries = self::DEFAULT_MAX_RETRIES, $base_delay = self::DEFAULT_BASE_DELAY) {
        $args['method'] = 'POST';
        return self::wpRemoteRequestWithRetry($url, $args, $max_retries, $base_delay);
    }

    /**
     * 计算延迟时间（指数退避）
     *
     * @since    1.1.2
     * @param    int         $attempt        当前尝试次数
     * @param    int         $base_delay     基础延迟时间（毫秒）
     * @return   int                         计算后的延迟时间（毫秒）
     */
    public static function calculateDelay($attempt, $base_delay = self::DEFAULT_BASE_DELAY) {
        return $base_delay * pow(2, $attempt);
    }

    /**
     * 获取错误类型描述
     *
     * @since    1.1.2
     * @param    Exception   $exception      异常对象
     * @return   string                      错误类型描述
     */
    public static function getErrorTypeDescription($exception) {
        if (self::isPermanentError($exception)) {
            return '永久性错误';
        } elseif (self::isTemporaryError($exception)) {
            return '临时性错误';
        } else {
            return '未知错误类型';
        }
    }

    /**
     * 记录重试尝试
     *
     * @since    1.1.2
     * @param    int         $attempt        当前尝试次数
     * @param    int         $max_retries    最大重试次数
     * @param    Exception   $exception      异常对象
     * @param    int         $delay          延迟时间（毫秒）
     */
    public static function logRetryAttempt($attempt, $max_retries, $exception, $delay = 0) {
        $error_type = self::getErrorTypeDescription($exception);
        
        \NTWP\Core\Foundation\Logger::debugLog(
            sprintf(
                '重试尝试 %d/%d: %s (延迟: %d毫秒)',
                $attempt + 1,
                $max_retries,
                $error_type,
                $delay
            ),
            'Network Retry'
        );
    }

    /**
     * 计算智能延迟时间
     *
     * @since    1.1.2
     * @param    int         $attempt        当前尝试次数
     * @param    Exception   $exception      异常对象
     * @param    int         $base_delay     基础延迟时间（毫秒）
     * @return   int                         计算后的延迟时间（毫秒）
     */
    private static function calculateSmartDelay($attempt, $exception, $base_delay = self::DEFAULT_BASE_DELAY) {
        // 基础指数退避
        $delay = $base_delay * pow(2, $attempt);
        
        // 根据错误类型调整延迟
        $message = strtolower($exception->getMessage());
        
        // 速率限制错误需要更长的延迟
        if (strpos($message, 'rate limit') !== false || strpos($message, 'too many requests') !== false) {
            $delay *= 2;
        }
        
        // 服务器错误可能需要更长的延迟
        if (strpos($message, 'server error') !== false || strpos($message, 'service unavailable') !== false) {
            $delay *= 1.5;
        }
        
        // 网络连接错误可能需要更短的延迟
        if (strpos($message, 'connection') !== false || strpos($message, 'timeout') !== false) {
            $delay *= 0.8;
        }
        
        // 确保延迟在合理范围内
        $delay = max(100, min($delay, 30000)); // 100ms到30秒
        
        return (int)$delay;
    }

    /**
     * 记录错误类型统计
     *
     * @since    1.1.2
     * @param    Exception   $exception      异常对象
     */
    private static function recordErrorTypeStats($exception) {
        $error_type = self::getErrorTypeDescription($exception);
        
        if (!isset(self::$retry_stats['error_type_stats'][$error_type])) {
            self::$retry_stats['error_type_stats'][$error_type] = 0;
        }
        
        self::$retry_stats['error_type_stats'][$error_type]++;
    }

    /**
     * 获取增强的重试统计信息
     *
     * @since    2.0.0-beta.1
     * @return   array                        增强的统计信息
     */
    public static function getEnhancedRetryStats() {
        $stats = self::$retry_stats;
        
        // 计算平均延迟时间
        if ($stats['total_attempts'] > 0) {
            $stats['avg_delay_time'] = $stats['total_delay_time'] / $stats['total_attempts'];
        } else {
            $stats['avg_delay_time'] = 0;
        }
        
        // 计算成功率
        if ($stats['total_attempts'] > 0) {
            $stats['success_rate'] = ($stats['successful_retries'] / $stats['total_attempts']) * 100;
        } else {
            $stats['success_rate'] = 0;
        }
        
        // 添加系统信息
        $stats['system_info'] = [
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'time' => microtime(true)
        ];
        
        return $stats;
    }

    /**
     * 与性能监控系统集成
     *
     * @since    2.0.0-beta.1
     */
    public static function integrateWithPerformanceMonitor() {
        if (class_exists('\\NTWP\\Core\\Performance\\PerformanceMonitor')) {
            $stats = self::getEnhancedRetryStats();
            
            \NTWP\Core\Performance\PerformanceMonitor::recordMetric(
                'network_retry_stats',
                $stats,
                'Network Retry'
            );
        }
    }

    /**
     * 增强的永久性错误检查
     *
     * @since    2.0.0-beta.1
     * @param    Exception   $exception      异常对象
     * @return   bool                        是否为永久性错误
     */
    public static function isPermanentErrorEnhanced($exception) {
        // 首先使用原有的判断逻辑
        if (self::isPermanentError($exception)) {
            return true;
        }
        
        // 检查是否为WordPress错误
        if (is_wp_error($exception)) {
            $error_code = $exception->get_error_code();
            
            // 认证相关错误通常是永久性的
            if (in_array($error_code, ['invalid_url', 'invalid_credentials', 'forbidden'])) {
                return true;
            }
        }
        
        // 检查错误消息中的关键词
        $message = strtolower($exception->getMessage());
        $permanent_keywords = [
            'unauthorized',
            'forbidden',
            'not found',
            'bad request',
            'invalid',
            'malformed',
            'authentication',
            'permission',
            '认证失败',
            '授权失败',
            '权限不足',
            '无效',
            '格式错误'
        ];
        
        foreach ($permanent_keywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }
}
