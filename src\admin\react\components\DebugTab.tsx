/**
 * 调试工具标签页组件
 */

import { useState } from 'react';
import { useWordPressAjax, useI18n } from '../hooks/useWordPress';
import { Button } from './Button';

export function DebugTab() {
  const { request } = useWordPressAjax();
  const { __ } = useI18n();

  const [systemInfo, setSystemInfo] = useState<Record<string, unknown> | null>(null);
  const [logs, setLogs] = useState<string>('');
  const [isLoadingInfo, setIsLoadingInfo] = useState(false);
  const [isLoadingLogs, setIsLoadingLogs] = useState(false);

  // 获取系统信息
  const handleGetSystemInfo = async () => {
    setIsLoadingInfo(true);
    try {
      const response = await request('notion_to_wordpress_get_system_info');
      
      if (response.success) {
        setSystemInfo(response.data);
      }
    } catch (error) {
      console.error('Failed to get system info:', error);
    } finally {
      setIsLoadingInfo(false);
    }
  };

  // 获取日志
  const handleGetLogs = async () => {
    setIsLoadingLogs(true);
    try {
      const response = await request('notion_to_wordpress_get_logs');
      
      if (response.success) {
        setLogs(response.data.logs || '');
      }
    } catch (error) {
      console.error('Failed to get logs:', error);
    } finally {
      setIsLoadingLogs(false);
    }
  };

  // 清理日志
  const handleClearLogs = async () => {
    if (!confirm(__('确定要清理所有日志吗？', '确定要清理所有日志吗？'))) {
      return;
    }

    try {
      const response = await request('notion_to_wordpress_clear_logs');
      
      if (response.success) {
        setLogs('');
        alert(__('日志已清理', '日志已清理'));
      }
    } catch (error) {
      console.error('Failed to clear logs:', error);
    }
  };

  // 下载日志
  const handleDownloadLogs = () => {
    if (!logs) {
      alert(__('没有日志可下载', '没有日志可下载'));
      return;
    }

    const blob = new Blob([logs], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `notion-to-wordpress-logs-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 复制系统信息
  const handleCopySystemInfo = () => {
    if (!systemInfo) {
      alert(__('请先获取系统信息', '请先获取系统信息'));
      return;
    }

    const infoText = JSON.stringify(systemInfo, null, 2);
    navigator.clipboard.writeText(infoText).then(() => {
      alert(__('系统信息已复制到剪贴板', '系统信息已复制到剪贴板'));
    });
  };

  return (
    <div className="notion-wp-settings-section">
      <h2>{__('🐞 调试工具', '🐞 调试工具')}</h2>
      <p className="description">
        {__('用于诊断问题和获取技术支持信息的调试工具。', '用于诊断问题和获取技术支持信息的调试工具。')}
      </p>

      {/* 系统信息 */}
      <div className="notion-wp-settings-section">
        <h3>{__('💻 系统信息', '💻 系统信息')}</h3>
        <p className="description">
          {__('获取服务器和插件的详细信息，用于问题诊断。', '获取服务器和插件的详细信息，用于问题诊断。')}
        </p>
        
        <div className="debug-actions">
          <Button
            variant="secondary"
            loading={isLoadingInfo}
            onClick={handleGetSystemInfo}
          >
            <span className="dashicons dashicons-info" style={{ marginRight: '4px' }} />
            {__('获取系统信息', '获取系统信息')}
          </Button>
          
          {systemInfo && (
            <Button
              variant="secondary"
              onClick={handleCopySystemInfo}
            >
              <span className="dashicons dashicons-clipboard" style={{ marginRight: '4px' }} />
              {__('复制信息', '复制信息')}
            </Button>
          )}
        </div>

        {systemInfo && (
          <div className="system-info-display">
            <h4>{__('系统信息详情', '系统信息详情')}</h4>
            <div className="info-grid">
              <div className="info-section">
                <h5>{__('WordPress信息', 'WordPress信息')}</h5>
                <ul>
                  <li><strong>{__('版本', '版本')}:</strong> {systemInfo.wordpress?.version}</li>
                  <li><strong>{__('语言', '语言')}:</strong> {systemInfo.wordpress?.language}</li>
                  <li><strong>{__('主题', '主题')}:</strong> {systemInfo.wordpress?.theme}</li>
                  <li><strong>{__('调试模式', '调试模式')}:</strong> {systemInfo.wordpress?.debug ? __('开启', '开启') : __('关闭', '关闭')}</li>
                </ul>
              </div>
              
              <div className="info-section">
                <h5>{__('服务器信息', '服务器信息')}</h5>
                <ul>
                  <li><strong>PHP {__('版本', '版本')}:</strong> {systemInfo.server?.php_version}</li>
                  <li><strong>{__('内存限制', '内存限制')}:</strong> {systemInfo.server?.memory_limit}</li>
                  <li><strong>{__('最大执行时间', '最大执行时间')}:</strong> {systemInfo.server?.max_execution_time}s</li>
                  <li><strong>{__('操作系统', '操作系统')}:</strong> {systemInfo.server?.os}</li>
                </ul>
              </div>
              
              <div className="info-section">
                <h5>{__('插件信息', '插件信息')}</h5>
                <ul>
                  <li><strong>{__('版本', '版本')}:</strong> {systemInfo.plugin?.version}</li>
                  <li><strong>{__('数据库版本', '数据库版本')}:</strong> {systemInfo.plugin?.db_version}</li>
                  <li><strong>{__('最后同步', '最后同步')}:</strong> {systemInfo.plugin?.last_sync}</li>
                  <li><strong>{__('同步状态', '同步状态')}:</strong> {systemInfo.plugin?.sync_status}</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 日志查看器 */}
      <div className="notion-wp-settings-section">
        <h3>{__('📋 日志查看器', '📋 日志查看器')}</h3>
        <p className="description">
          {__('查看插件运行日志，帮助诊断同步问题。', '查看插件运行日志，帮助诊断同步问题。')}
        </p>
        
        <div className="debug-actions">
          <Button
            variant="secondary"
            loading={isLoadingLogs}
            onClick={handleGetLogs}
          >
            <span className="dashicons dashicons-media-text" style={{ marginRight: '4px' }} />
            {__('获取日志', '获取日志')}
          </Button>
          
          {logs && (
            <>
              <Button
                variant="secondary"
                onClick={handleDownloadLogs}
              >
                <span className="dashicons dashicons-download" style={{ marginRight: '4px' }} />
                {__('下载日志', '下载日志')}
              </Button>
              
              <Button
                variant="danger"
                onClick={handleClearLogs}
              >
                <span className="dashicons dashicons-trash" style={{ marginRight: '4px' }} />
                {__('清理日志', '清理日志')}
              </Button>
            </>
          )}
        </div>

        {logs && (
          <div className="log-viewer">
            <h4>{__('最新日志', '最新日志')}</h4>
            <textarea
              className="log-content"
              value={logs}
              readOnly
              rows={20}
              style={{
                width: '100%',
                fontFamily: 'monospace',
                fontSize: '12px',
                backgroundColor: '#f1f1f1',
                border: '1px solid #ddd',
                padding: '10px',
              }}
            />
          </div>
        )}
      </div>

      {/* 连接测试 */}
      <div className="notion-wp-settings-section">
        <h3>{__('🔗 连接测试', '🔗 连接测试')}</h3>
        <p className="description">
          {__('测试与Notion API的连接状态和响应时间。', '测试与Notion API的连接状态和响应时间。')}
        </p>
        
        <div className="connection-tests">
          <div className="test-item">
            <h4>{__('API连接测试', 'API连接测试')}</h4>
            <p>{__('测试基本的API连接', '测试基本的API连接')}</p>
            <Button variant="secondary">
              {__('开始测试', '开始测试')}
            </Button>
          </div>
          
          <div className="test-item">
            <h4>{__('数据库访问测试', '数据库访问测试')}</h4>
            <p>{__('测试对指定数据库的访问权限', '测试对指定数据库的访问权限')}</p>
            <Button variant="secondary">
              {__('开始测试', '开始测试')}
            </Button>
          </div>
          
          <div className="test-item">
            <h4>{__('Webhook测试', 'Webhook测试')}</h4>
            <p>{__('测试Webhook端点的可访问性', '测试Webhook端点的可访问性')}</p>
            <Button variant="secondary">
              {__('开始测试', '开始测试')}
            </Button>
          </div>
        </div>
      </div>

      {/* 故障排除 */}
      <div className="notion-wp-settings-section">
        <h3>{__('🔧 故障排除', '🔧 故障排除')}</h3>
        
        <div className="troubleshooting-guide">
          <div className="trouble-item">
            <h4>{__('常见问题', '常见问题')}</h4>
            <ul>
              <li>{__('同步失败：检查API密钥和数据库ID是否正确', '同步失败：检查API密钥和数据库ID是否正确')}</li>
              <li>{__('内容不完整：检查字段映射配置', '内容不完整：检查字段映射配置')}</li>
              <li>{__('同步缓慢：调整性能配置参数', '同步缓慢：调整性能配置参数')}</li>
              <li>{__('Webhook不工作：检查服务器防火墙设置', 'Webhook不工作：检查服务器防火墙设置')}</li>
            </ul>
          </div>
          
          <div className="trouble-item">
            <h4>{__('获取支持', '获取支持')}</h4>
            <p>{__('如果问题仍然存在，请：', '如果问题仍然存在，请：')}</p>
            <ol>
              <li>{__('复制系统信息', '复制系统信息')}</li>
              <li>{__('下载最新日志', '下载最新日志')}</li>
              <li>{__('访问插件支持页面', '访问插件支持页面')}</li>
            </ol>
            <Button variant="primary">
              <span className="dashicons dashicons-external" style={{ marginRight: '4px' }} />
              {__('访问支持页面', '访问支持页面')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
