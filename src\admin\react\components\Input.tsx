/**
 * 输入组件
 */

import { useState, useCallback } from 'react';
import type { InputProps, SelectProps, CheckboxProps } from '../types';
import { IconButton } from './Button';

// 基础输入组件
export function Input({
  type = 'text',
  value = '',
  placeholder,
  disabled = false,
  required = false,
  validation,
  onChange,
  onBlur,
  className = '',
  ...props
}: InputProps) {
  const [isValid, setIsValid] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');

  const validateInput = useCallback((inputValue: string) => {
    if (!validation) return true;

    switch (validation) {
      case 'api-key': {
        const isValidApiKey = /^secret_[a-zA-Z0-9]{43}$/.test(inputValue);
        if (!isValidApiKey && inputValue.length > 0) {
          setErrorMessage('API密钥格式不正确，应以"secret_"开头');
          setIsValid(false);
          return false;
        }
        break;
      }

      case 'database-id': {
        const isValidDbId = /^[a-f0-9]{32}$/.test(inputValue.replace(/-/g, ''));
        if (!isValidDbId && inputValue.length > 0) {
          setErrorMessage('数据库ID格式不正确');
          setIsValid(false);
          return false;
        }
        break;
      }

      case 'email': {
        const isValidEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(inputValue);
        if (!isValidEmail && inputValue.length > 0) {
          setErrorMessage('邮箱格式不正确');
          setIsValid(false);
          return false;
        }
        break;
      }
    }

    setIsValid(true);
    setErrorMessage('');
    return true;
  }, [validation]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    validateInput(newValue);
    onChange?.(newValue);
  };

  const handleBlur = () => {
    validateInput(value);
    onBlur?.();
  };

  const inputClasses = [
    'regular-text',
    'notion-wp-validated-input',
    !isValid && 'invalid',
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className="input-with-validation">
      <input
        type={type}
        value={value}
        placeholder={placeholder}
        disabled={disabled}
        required={required}
        className={inputClasses}
        onChange={handleChange}
        onBlur={handleBlur}
        data-validation={validation}
        {...props}
      />
      {!isValid && errorMessage && (
        <div className="validation-feedback error">
          {errorMessage}
        </div>
      )}
    </div>
  );
}

// 密码输入组件
export function PasswordInput({
  showToggle = true,
  ...props
}: InputProps & { showToggle?: boolean }) {
  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="input-with-button">
      <Input
        {...props}
        type={showPassword ? 'text' : 'password'}
        className={`password-input ${props.className || ''}`}
      />
      {showToggle && (
        <IconButton
          variant="secondary"
          size="medium"
          icon={showPassword ? 'hidden' : 'visibility'}
          title={showPassword ? '隐藏密码' : '显示密码'}
          onClick={togglePasswordVisibility}
          className="show-hide-password"
        />
      )}
    </div>
  );
}

// 选择器组件
export function Select({
  value = '',
  options,
  disabled = false,
  required = false,
  onChange,
  className = '',
  ...props
}: SelectProps) {
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange?.(e.target.value);
  };

  return (
    <select
      value={value}
      disabled={disabled}
      required={required}
      className={`regular-text ${className}`}
      onChange={handleChange}
      {...props}
    >
      {options.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
}

// 复选框组件
export function Checkbox({
  checked = false,
  disabled = false,
  label,
  description,
  onChange,
  className = '',
  children,
  ...props
}: CheckboxProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e.target.checked);
  };

  return (
    <div className={`checkbox-wrapper ${className}`}>
      <label className="checkbox-with-label">
        <input
          type="checkbox"
          checked={checked}
          disabled={disabled}
          onChange={handleChange}
          {...props}
        />
        {label && <span className="checkbox-label">{label}</span>}
        {children}
      </label>
      {description && (
        <p className="description">{description}</p>
      )}
    </div>
  );
}

// 字段集组件
export function Fieldset({
  legend,
  children,
  className = '',
}: {
  legend?: string;
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <fieldset className={className}>
      {legend && <legend>{legend}</legend>}
      {children}
    </fieldset>
  );
}

// 表单行组件
export function FormRow({
  label,
  required = false,
  description,
  children,
  className = '',
}: {
  label?: string;
  required?: boolean;
  description?: string;
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <tr className={className}>
      {label && (
        <th scope="row">
          <label>
            {label}
            {required && <span className="required">*</span>}
          </label>
        </th>
      )}
      <td>
        {children}
        {description && (
          <p className="description">{description}</p>
        )}
      </td>
    </tr>
  );
}
