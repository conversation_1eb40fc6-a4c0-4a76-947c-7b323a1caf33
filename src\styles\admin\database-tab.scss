/**
 * 数据库标签页样式
 * 
 * 为管理界面中的数据库视图标签页提供样式
 */

// 数据库标签页基础样式
.notion-wp-settings-section {
  .database-tab-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;

    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #23282d;
    }

    .description {
      margin: 0;
      color: #666;
      font-size: 14px;
      line-height: 1.5;

      code {
        background: #f1f1f1;
        padding: 2px 6px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 13px;
      }
    }
  }

  .database-tab-content {
    // 确保数据库视图在管理界面中正确显示
    .admin-database-view {
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 4px;
      overflow: hidden;

      // 工具栏样式调整
      .notion-database-toolbar {
        background: #f9f9f9;
        border-bottom: 1px solid #ddd;
        padding: 12px 16px;

        .toolbar-left {
          .view-type-selector {
            .view-type-button {
              &.active {
                background: #0073aa;
                color: white;
              }
            }
          }
        }

        .toolbar-right {
          .search-input {
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 6px 10px;
            font-size: 13px;

            &:focus {
              border-color: #0073aa;
              box-shadow: 0 0 0 1px #0073aa;
            }
          }

          .toolbar-button {
            background: #f7f7f7;
            border: 1px solid #ddd;
            color: #555;
            padding: 6px 12px;
            border-radius: 3px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: #0073aa;
              color: white;
              border-color: #0073aa;
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }
        }
      }

      // 视图容器样式
      .database-view-container {
        min-height: 400px;
        max-height: 70vh;
        overflow: auto;
      }

      // 分页样式
      .database-pagination {
        background: #f9f9f9;
        border-top: 1px solid #ddd;
        padding: 12px 16px;
        text-align: center;

        .load-more-button {
          background: #0073aa;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 3px;
          cursor: pointer;
          font-size: 13px;
          transition: background 0.2s ease;

          &:hover {
            background: #005a87;
          }

          &:disabled {
            background: #ccc;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}

// 设置指导样式
.notion-wp-setup-guide {
  .setup-guide-header {
    text-align: center;
    margin-bottom: 30px;

    h2 {
      margin: 0 0 10px 0;
      font-size: 28px;
      color: #23282d;
    }

    .description {
      font-size: 16px;
      color: #666;
      margin: 0;
    }
  }

  .setup-guide-content {
    max-width: 800px;
    margin: 0 auto;

    .notice {
      margin: 20px 0;
      padding: 15px;
      border-left: 4px solid #ffb900;
      background: #fff8e5;

      h3 {
        margin: 0 0 10px 0;
        font-size: 16px;
        color: #23282d;
      }

      p {
        margin: 8px 0;
        line-height: 1.6;
      }

      ol {
        margin: 10px 0;
        padding-left: 20px;

        li {
          margin: 5px 0;
          line-height: 1.5;

          strong {
            color: #0073aa;
          }
        }
      }
    }

    .setup-guide-features {
      margin-top: 30px;

      h3 {
        text-align: center;
        margin: 0 0 20px 0;
        font-size: 20px;
        color: #23282d;
      }

      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 20px;

        .feature-item {
          background: #fff;
          border: 1px solid #ddd;
          border-radius: 6px;
          padding: 20px;
          text-align: center;
          transition: box-shadow 0.2s ease;

          &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          .feature-icon {
            font-size: 32px;
            margin-bottom: 12px;
          }

          .feature-content {
            h4 {
              margin: 0 0 8px 0;
              font-size: 16px;
              color: #23282d;
            }

            p {
              margin: 0;
              font-size: 14px;
              color: #666;
              line-height: 1.5;
            }
          }
        }
      }
    }
  }
}

// 错误状态样式
.notion-wp-error-state {
  text-align: center;
  max-width: 600px;
  margin: 40px auto;

  .error-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 10px 0;
      font-size: 24px;
      color: #d63638;
    }
  }

  .error-content {
    .notice {
      margin: 20px 0;
      padding: 15px;
      border-left: 4px solid #d63638;
      background: #fef7f7;
      text-align: left;

      p {
        margin: 8px 0;
        line-height: 1.6;

        strong {
          color: #d63638;
        }
      }
    }

    .error-actions {
      text-align: left;
      margin-top: 20px;

      p {
        margin: 10px 0;
        line-height: 1.6;
      }

      ul {
        margin: 10px 0;
        padding-left: 20px;

        li {
          margin: 5px 0;
          line-height: 1.5;
        }
      }

      strong {
        color: #0073aa;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .notion-wp-settings-section {
    .database-tab-content {
      .admin-database-view {
        .notion-database-toolbar {
          flex-direction: column;
          gap: 10px;

          .toolbar-left,
          .toolbar-right {
            width: 100%;
            justify-content: center;
          }
        }

        .database-view-container {
          max-height: 50vh;
        }
      }
    }
  }

  .notion-wp-setup-guide {
    .setup-guide-content {
      .setup-guide-features {
        .features-grid {
          grid-template-columns: 1fr;
          gap: 15px;
        }
      }
    }
  }
}
