<?php
declare(strict_types=1);

namespace NTWP\Core\Foundation;

use NTWP\Services\Api\NotionApi;
use NTWP\Services\Import\ImportService;
use NTWP\Services\Content\ContentConverter;
use NTWP\Handlers\ImportHandler;

/**
 * 统一服务工厂类
 *
 * 提供标准化的服务创建和依赖注入管理，
 * 实现服务的统一配置、生命周期管理和性能优化。
 *
 * 设计原则：
 * - 工厂模式：统一服务创建逻辑
 * - 依赖注入：自动解析和注入依赖
 * - 单例管理：合理控制服务实例生命周期
 * - 配置驱动：基于配置创建服务实例
 * - 性能优化：延迟加载和实例复用
 *
 * @since      2.0.0-beta.2
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-Loong/Notion-to-WordPress
 */

// 如果直接访问此文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

class ServiceFactory {

    /**
     * 服务实例缓存
     * 
     * @since 2.0.0-beta.2
     */
    private static array $instances = [];

    /**
     * 服务配置缓存
     * 
     * @since 2.0.0-beta.2
     */
    private static ?array $config_cache = null;

    /**
     * 性能监控数据
     * 
     * @since 2.0.0-beta.2
     */
    private static array $performance_stats = [
        'service_creation_count' => 0,
        'cache_hits' => 0,
        'cache_misses' => 0,
        'creation_times' => []
    ];

    /**
     * 创建NotionApi服务实例
     *
     * @since 2.0.0-beta.2
     * @param string $api_key API密钥，为空时从配置读取
     * @param array $options 额外选项
     * @return NotionApi NotionApi实例
     */
    public static function createNotionApi(string $api_key = '', array $options = []): NotionApi {
        $start_time = microtime(true);
        
        // 生成缓存键
        $cache_key = 'notion_api_' . md5($api_key . serialize($options));
        
        // 检查缓存
        if (isset(self::$instances[$cache_key])) {
            self::$performance_stats['cache_hits']++;
            return self::$instances[$cache_key];
        }
        
        self::$performance_stats['cache_misses']++;
        
        // 获取API密钥
        if (empty($api_key)) {
            $config = self::getConfig();
            $api_key = $config['notion_api_key'] ?? '';
        }
        
        // 创建实例
        $instance = new NotionApi($api_key);
        
        // 应用配置选项
        if (!empty($options['sync_mode'])) {
            $instance->setSyncMode($options['sync_mode']);
        }
        
        // 缓存实例
        self::$instances[$cache_key] = $instance;
        
        // 记录性能数据
        $creation_time = microtime(true) - $start_time;
        self::$performance_stats['service_creation_count']++;
        self::$performance_stats['creation_times']['NotionApi'][] = $creation_time;
        
        return $instance;
    }

    /**
     * 创建ImportService服务实例
     *
     * @since 2.0.0-beta.2
     * @param NotionApi|null $notion_api NotionApi实例，为空时自动创建
     * @param ContentConverter|null $content_converter ContentConverter实例，为空时自动创建
     * @return ImportService ImportService实例
     */
    public static function createImportService(
        ?NotionApi $notion_api = null, 
        ?ContentConverter $content_converter = null
    ): ImportService {
        $start_time = microtime(true);
        
        // 生成缓存键
        $cache_key = 'import_service_' . spl_object_hash($notion_api ?: new \stdClass()) . 
                     '_' . spl_object_hash($content_converter ?: new \stdClass());
        
        // 检查缓存
        if (isset(self::$instances[$cache_key])) {
            self::$performance_stats['cache_hits']++;
            return self::$instances[$cache_key];
        }
        
        self::$performance_stats['cache_misses']++;
        
        // 自动创建依赖
        if ($notion_api === null) {
            $notion_api = self::createNotionApi();
        }
        
        if ($content_converter === null) {
            $content_converter = self::createContentConverter();
        }
        
        // 创建实例
        $instance = new ImportService($notion_api, $content_converter);
        
        // 缓存实例
        self::$instances[$cache_key] = $instance;
        
        // 记录性能数据
        $creation_time = microtime(true) - $start_time;
        self::$performance_stats['service_creation_count']++;
        self::$performance_stats['creation_times']['ImportService'][] = $creation_time;
        
        return $instance;
    }

    /**
     * 创建ContentConverter服务实例
     *
     * @since 2.0.0-beta.2
     * @param array $options 配置选项
     * @return ContentConverter ContentConverter实例
     */
    public static function createContentConverter(array $options = []): ContentConverter {
        $start_time = microtime(true);
        
        // 生成缓存键
        $cache_key = 'content_converter_' . md5(serialize($options));
        
        // 检查缓存
        if (isset(self::$instances[$cache_key])) {
            self::$performance_stats['cache_hits']++;
            return self::$instances[$cache_key];
        }
        
        self::$performance_stats['cache_misses']++;
        
        // 创建实例
        $instance = new ContentConverter();
        
        // 应用配置选项
        if (!empty($options['enable_preloader'])) {
            // 启用预加载器（如果ContentConverter支持）
            if (method_exists($instance, 'enablePreloader')) {
                $instance->enablePreloader();
            }
        }
        
        // 缓存实例
        self::$instances[$cache_key] = $instance;
        
        // 记录性能数据
        $creation_time = microtime(true) - $start_time;
        self::$performance_stats['service_creation_count']++;
        self::$performance_stats['creation_times']['ContentConverter'][] = $creation_time;
        
        return $instance;
    }

    /**
     * 创建ImportHandler服务实例
     *
     * @since 2.0.0-beta.2
     * @param ImportService|null $import_service ImportService实例，为空时自动创建
     * @return ImportHandler ImportHandler实例
     */
    public static function createImportHandler(?ImportService $import_service = null): ImportHandler {
        $start_time = microtime(true);
        
        // 生成缓存键
        $cache_key = 'import_handler_' . spl_object_hash($import_service ?: new \stdClass());
        
        // 检查缓存
        if (isset(self::$instances[$cache_key])) {
            self::$performance_stats['cache_hits']++;
            return self::$instances[$cache_key];
        }
        
        self::$performance_stats['cache_misses']++;
        
        // 自动创建依赖
        if ($import_service === null) {
            $import_service = self::createImportService();
        }
        
        // 创建实例
        $instance = new ImportHandler($import_service);
        
        // 缓存实例
        self::$instances[$cache_key] = $instance;
        
        // 记录性能数据
        $creation_time = microtime(true) - $start_time;
        self::$performance_stats['service_creation_count']++;
        self::$performance_stats['creation_times']['ImportHandler'][] = $creation_time;
        
        return $instance;
    }

    /**
     * 获取插件配置
     *
     * @since 2.0.0-beta.2
     * @return array 配置数组
     */
    private static function getConfig(): array {
        if (self::$config_cache === null) {
            self::$config_cache = get_option('notion_to_wordpress_options', []);
        }
        
        return self::$config_cache;
    }

    /**
     * 清除服务实例缓存
     *
     * @since 2.0.0-beta.2
     * @param string|null $service_type 服务类型，为空时清除所有
     */
    public static function clearCache(?string $service_type = null): void {
        if ($service_type === null) {
            self::$instances = [];
        } else {
            $pattern = strtolower($service_type) . '_';
            foreach (array_keys(self::$instances) as $key) {
                if (strpos($key, $pattern) === 0) {
                    unset(self::$instances[$key]);
                }
            }
        }
        
        // 清除配置缓存
        self::$config_cache = null;
    }

    /**
     * 获取性能统计数据
     *
     * @since 2.0.0-beta.2
     * @return array 性能统计
     */
    public static function getPerformanceStats(): array {
        $stats = self::$performance_stats;
        
        // 计算平均创建时间
        foreach ($stats['creation_times'] as $service => $times) {
            if (!empty($times)) {
                $stats['avg_creation_time'][$service] = array_sum($times) / count($times);
                $stats['max_creation_time'][$service] = max($times);
                $stats['min_creation_time'][$service] = min($times);
            }
        }
        
        // 计算缓存命中率
        $total_requests = $stats['cache_hits'] + $stats['cache_misses'];
        $stats['cache_hit_rate'] = $total_requests > 0 ? 
            round(($stats['cache_hits'] / $total_requests) * 100, 2) : 0;
        
        return $stats;
    }

    /**
     * 重置性能统计
     *
     * @since 2.0.0-beta.2
     */
    public static function resetPerformanceStats(): void {
        self::$performance_stats = [
            'service_creation_count' => 0,
            'cache_hits' => 0,
            'cache_misses' => 0,
            'creation_times' => []
        ];
    }

    /**
     * 预热服务缓存
     *
     * @since 2.0.0-beta.2
     * @param array $services 要预热的服务列表
     */
    public static function warmupCache(array $services = ['NotionApi', 'ImportService', 'ContentConverter']): void {
        foreach ($services as $service) {
            switch ($service) {
                case 'NotionApi':
                    self::createNotionApi();
                    break;
                case 'ImportService':
                    self::createImportService();
                    break;
                case 'ContentConverter':
                    self::createContentConverter();
                    break;
                case 'ImportHandler':
                    self::createImportHandler();
                    break;
            }
        }
    }
}
