<?php
/**
 * 管理界面显示 - React版本
 *
 * 这个文件提供了插件管理界面的显示逻辑，现在主要作为React应用的容器。
 *
 * @link       https://github.com/Frank-<PERSON><PERSON>/Notion-to-WordPress
 * @since      2.0.0
 *
 * @package    Notion_To_WordPress
 * @subpackage Notion_To_WordPress/admin/Views
 */

// 如果直接访问此文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

// 引入Helper类
use NTWP\Utils\Helper;

// 一次性获取所有选项
$options = get_option('notion_to_wordpress_options', []);

// 从选项数组中安全获取值，带默认值
$api_key               = $options['notion_api_key'] ?? '';
$database_id           = $options['notion_database_id'] ?? '';
$sync_schedule         = $options['sync_schedule'] ?? 'manual';
$delete_on_uninstall   = $options['delete_on_uninstall'] ?? 0;
?>

<div class="wrap notion-wp-admin">
    <div class="notion-wp-header">
        <div class="notion-wp-header-content">
            <div class="notion-wp-header-left">
                <img src="<?php echo esc_url(Helper::pluginUrl('assets/icon.svg')); ?>" alt="Notion to WordPress" class="notion-wp-header-icon">
                <div class="notion-wp-header-text">
                    <h1><?php esc_html_e('Notion to WordPress', 'notion-to-wordpress'); ?></h1>
                    <p class="notion-wp-header-subtitle"><?php esc_html_e('将您的Notion内容无缝同步到WordPress', 'notion-to-wordpress'); ?></p>
                </div>
            </div>
            <div class="notion-wp-header-right">
                <span class="notion-wp-version">v<?php echo esc_html(NOTION_TO_WORDPRESS_VERSION); ?></span>
            </div>
        </div>
    </div>

    <div class="notion-wp-container">
        <div class="notion-wp-layout" data-component="tab-manager" data-active-class="active" data-content-class="notion-wp-tab-content" data-storage-key="notion_wp_active_tab" data-default-tab="api-settings">
            <!-- 传统菜单栏 - 仅在React应用加载失败时显示 -->
            <div class="notion-wp-sidebar" id="notion-wp-traditional-sidebar" style="display: none;">
                <div class="notion-wp-menu">
                    <button class="notion-wp-menu-item active" data-tab="api-settings">
                        <?php esc_html_e('🔄 同步设置', 'notion-to-wordpress'); ?>
                    </button>
                    <button class="notion-wp-menu-item" data-tab="field-mapping">
                        <?php esc_html_e('🔗 字段映射', 'notion-to-wordpress'); ?>
                    </button>
                    <button class="notion-wp-menu-item" data-tab="performance-config">
                        <?php esc_html_e('⚡ 性能配置', 'notion-to-wordpress'); ?>
                    </button>
                    <button class="notion-wp-menu-item" data-tab="performance">
                        <?php esc_html_e('📊 性能监控', 'notion-to-wordpress'); ?>
                    </button>
                    <button class="notion-wp-menu-item" data-tab="other-settings">
                        <?php esc_html_e('⚙️ 其他设置', 'notion-to-wordpress'); ?>
                    </button>
                    <button class="notion-wp-menu-item" data-tab="debug">
                        <?php esc_html_e('🐞 调试工具', 'notion-to-wordpress'); ?>
                    </button>
                    <button class="notion-wp-menu-item" data-tab="help">
                        <?php esc_html_e('📖 使用帮助', 'notion-to-wordpress'); ?>
                    </button>
                    <button class="notion-wp-menu-item" data-tab="about-author">
                        <?php esc_html_e('👨‍💻 关于作者', 'notion-to-wordpress'); ?>
                    </button>
                </div>
            </div>
            
            <div class="notion-wp-content">
                <!-- React应用挂载点 -->
                <div id="notion-to-wordpress-react-root" class="notion-to-wordpress-react-app"></div>

                <!-- 备用PHP表单（当React应用加载失败时显示） -->
                <form id="notion-to-wordpress-settings-form" method="post" action="admin-post.php" style="display: none;">
                    <input type="hidden" name="action" value="notion_to_wordpress_options">
                    <?php wp_nonce_field('notion_to_wordpress_options_update', 'notion_to_wordpress_options_nonce'); ?>

                    <div class="notion-wp-tab-content active" id="api-settings">
                        <div class="notion-wp-settings-section">
                            <h2><?php esc_html_e('Notion API 设置', 'notion-to-wordpress'); ?></h2>
                            
                            <div class="notion-stats-grid">
                                <div class="stat-card">
                                    <h3 class="stat-imported-count">0</h3>
                                    <span><?php esc_html_e('已导入页面', 'notion-to-wordpress'); ?></span>
                                </div>
                                <div class="stat-card">
                                    <h3 class="stat-published-count">0</h3>
                                    <span><?php esc_html_e('已发布页面', 'notion-to-wordpress'); ?></span>
                                </div>
                                <div class="stat-card">
                                    <h3 class="stat-last-update"><?php esc_html_e('从未', 'notion-to-wordpress'); ?></h3>
                                    <span><?php esc_html_e('最后同步', 'notion-to-wordpress'); ?></span>
                                </div>
                                <div class="stat-card">
                                    <h3 class="stat-next-run"><?php esc_html_e('未计划', 'notion-to-wordpress'); ?></h3>
                                    <span><?php esc_html_e('下次同步', 'notion-to-wordpress'); ?></span>
                                </div>
                            </div>
                            
                            <div class="notice notice-info">
                                <p><strong><?php esc_html_e('正在使用传统界面', 'notion-to-wordpress'); ?></strong></p>
                                <p><?php esc_html_e('React应用加载失败，已切换到备用界面。请刷新页面重试，或联系管理员。', 'notion-to-wordpress'); ?></p>
                            </div>
                            
                            <p class="description">
                                <?php esc_html_e('连接到您的Notion数据库所需的设置。', 'notion-to-wordpress'); ?>
                                <a href="https://developers.notion.com/docs/getting-started" target="_blank"><?php esc_html_e('了解如何获取API密钥', 'notion-to-wordpress'); ?></a>
                            </p>
                            
                            <table class="form-table">
                                <tbody>
                                    <tr>
                                        <th scope="row"><label for="notion_to_wordpress_api_key"><?php esc_html_e('API密钥', 'notion-to-wordpress'); ?></label></th>
                                        <td>
                                            <div class="input-with-validation">
                                                <div class="input-with-button">
                                                    <input type="password" id="notion_to_wordpress_api_key" name="notion_to_wordpress_api_key" value="<?php echo esc_attr($api_key); ?>" class="regular-text notion-wp-validated-input" autocomplete="off" placeholder="<?php esc_attr_e('输入您的Notion API密钥', 'notion-to-wordpress'); ?>" data-validation="api-key">
                                                    <button type="button" class="button button-secondary show-hide-password" title="<?php esc_attr_e('显示/隐藏密钥', 'notion-to-wordpress'); ?>"><span class="dashicons dashicons-visibility"></span></button>
                                                </div>
                                                <div class="validation-feedback" id="api-key-feedback"></div>
                                            </div>
                                            <p class="description"><?php esc_html_e('在Notion的"我的集成"页面创建并获取API密钥。', 'notion-to-wordpress'); ?></p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row"><label for="notion_to_wordpress_database_id"><?php esc_html_e('数据库ID', 'notion-to-wordpress'); ?></label></th>
                                        <td>
                                            <div class="input-with-validation">
                                                <input type="text" id="notion_to_wordpress_database_id" name="notion_to_wordpress_database_id" value="<?php echo esc_attr($database_id); ?>" class="regular-text notion-wp-validated-input" placeholder="<?php esc_attr_e('输入您的Notion数据库ID', 'notion-to-wordpress'); ?>" data-validation="database-id">
                                                <div class="validation-feedback" id="database-id-feedback"></div>
                                            </div>
                                            <p class="description"><?php echo wp_kses( __('可以从Notion数据库URL中找到，格式如：https://www.notion.so/xxx/<strong>数据库ID</strong>?v=xxx', 'notion-to-wordpress'), ['strong' => []] ); ?></p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row"><label for="sync_schedule"><?php esc_html_e('自动同步频率', 'notion-to-wordpress'); ?></label></th>
                                        <td>
                                            <select id="sync_schedule" name="sync_schedule" class="regular-text">
                                                <?php
                                                $schedules = [
                                                    'manual'     => __('手动同步', 'notion-to-wordpress'),
                                                    'twicedaily' => __('每天两次', 'notion-to-wordpress'),
                                                    'daily'      => __('每天一次', 'notion-to-wordpress'),
                                                    'weekly'     => __('每周一次', 'notion-to-wordpress'),
                                                    'biweekly'   => __('每两周一次', 'notion-to-wordpress'),
                                                    'monthly'    => __('每月一次', 'notion-to-wordpress'),
                                                ];
                                                foreach ($schedules as $value => $label) {
                                                    echo '<option value="' . esc_attr($value) . '" ' . selected($sync_schedule, $value, false) . '>' . esc_html($label) . '</option>';
                                                }
                                                ?>
                                            </select>
                                            <p class="description"><?php esc_html_e('选择 "手动同步" 以禁用定时任务。', 'notion-to-wordpress'); ?></p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row"><?php esc_html_e('卸载设置', 'notion-to-wordpress'); ?></th>
                                        <td>
                                            <fieldset>
                                                <legend class="screen-reader-text"><span><?php esc_html_e('卸载时删除所有同步内容', 'notion-to-wordpress'); ?></span></legend>
                                                <label for="delete_on_uninstall" class="checkbox-with-label">
                                                    <input type="checkbox" id="delete_on_uninstall" name="delete_on_uninstall" value="1" <?php checked(1, $delete_on_uninstall); ?>>
                                                    <span><?php esc_html_e('卸载插件时，删除所有从Notion同步的文章和页面', 'notion-to-wordpress'); ?></span>
                                                </label>
                                                <p class="description"><?php esc_html_e('警告：启用此选项后，卸载插件时将永久删除所有同步的内容，此操作不可撤销。', 'notion-to-wordpress'); ?></p>
                                            </fieldset>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="notion-wp-button-row">
                                <button type="button" id="notion-test-connection" class="button button-secondary">
                                    <span class="dashicons dashicons-admin-network"></span> <?php esc_html_e('测试连接', 'notion-to-wordpress'); ?>
                                </button>
                            </div>

                            <!-- 同步操作按钮 -->
                            <div class="notion-wp-sync-actions">
                                <h3><?php esc_html_e('同步操作', 'notion-to-wordpress'); ?></h3>
                                <div class="sync-buttons">
                                    <button type="button" class="button button-primary notion-wp-sync-btn" id="notion-manual-import"
                                            data-sync-type="manual" data-original-text="<?php esc_attr_e('智能同步', 'notion-to-wordpress'); ?>">
                                        <span class="dashicons dashicons-lightbulb"></span>
                                        <span class="button-text"><?php esc_html_e('智能同步', 'notion-to-wordpress'); ?></span>
                                    </button>
                                    <button type="button" class="button button-secondary notion-wp-sync-btn" id="notion-full-import"
                                            data-sync-type="full" data-original-text="<?php esc_attr_e('完全同步', 'notion-to-wordpress'); ?>">
                                        <span class="dashicons dashicons-update"></span>
                                        <span class="button-text"><?php esc_html_e('完全同步', 'notion-to-wordpress'); ?></span>
                                    </button>
                                </div>

                                <!-- 同步进度指示器 -->
                                <div class="sync-progress notion-wp-hidden" id="sync-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span class="current-step">准备同步...</span>
                                        <span class="progress-percentage">0%</span>
                                    </div>
                                </div>

                                <div class="sync-info">
                                    <p><strong><?php esc_html_e('智能同步', 'notion-to-wordpress'); ?></strong>: <?php esc_html_e('只同步有变化的页面，速度更快', 'notion-to-wordpress'); ?></p>
                                    <p><strong><?php esc_html_e('完全同步', 'notion-to-wordpress'); ?></strong>: <?php esc_html_e('同步所有页面，确保数据一致性', 'notion-to-wordpress'); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="notion-wp-actions-bar">
                        <?php submit_button(__('保存设置', 'notion-to-wordpress'), 'primary', 'submit', false, array('id' => 'notion-save-settings')); ?>
                        <button type="button" class="button button-secondary" onclick="location.reload();"><?php esc_html_e('刷新页面', 'notion-to-wordpress'); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Toast提示组件 -->
<div id="notion-wp-toast" class="notion-wp-toast">
    <div class="notion-wp-toast-icon">
        <span class="dashicons"></span>
    </div>
    <div class="notion-wp-toast-content"></div>
    <button class="notion-wp-toast-close">
        <span class="dashicons dashicons-no-alt"></span>
    </button>
</div>

<div id="loading-overlay" class="notion-wp-hidden">
    <div class="loading-message">
        <span class="spinner is-active"></span>
        <?php esc_html_e('处理中，请稍候...', 'notion-to-wordpress'); ?>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // 检测React应用加载状态
    let reactLoadTimeout;
    let reactCheckAttempts = 0;
    const maxReactCheckAttempts = 50; // 最多检查5秒（50次 × 100ms）
    
    function checkReactAppLoaded() {
        const reactRoot = document.getElementById('notion-to-wordpress-react-root');
        const hasReactContent = reactRoot && (
            reactRoot.children.length > 0 || 
            reactRoot.innerHTML.trim().length > 0 ||
            reactRoot.hasChildNodes()
        );
        
        reactCheckAttempts++;
        
        if (hasReactContent) {
            // React应用已加载成功
            console.log('React应用加载成功');
            clearTimeout(reactLoadTimeout);
            // 确保传统界面隐藏
            $('#notion-wp-traditional-sidebar').hide();
            $('#notion-to-wordpress-settings-form').hide();
        } else if (reactCheckAttempts >= maxReactCheckAttempts) {
            // React应用加载失败，显示传统界面
            console.log('React应用加载失败，切换到传统界面');
            $('#notion-wp-traditional-sidebar').show();
            $('#notion-to-wordpress-settings-form').show();
            
            // 显示加载失败提示
            if (window.showToast) {
                window.showToast('error', '<?php esc_html_e('React应用加载失败，已切换到备用界面。请刷新页面重试，或联系管理员。', 'notion-to-wordpress'); ?>');
            }
        } else {
            // 继续检查
            reactLoadTimeout = setTimeout(checkReactAppLoaded, 100);
        }
    }
    
    // 开始检查React应用加载状态
    reactLoadTimeout = setTimeout(checkReactAppLoaded, 100);
    
    // Toast提示函数 - 定义为全局函数
    window.showToast = function(type, message) {
        var $ = jQuery;
        var toast = $('#notion-wp-toast');
        var icon = toast.find('.dashicons');

        // 设置图标
        icon.removeClass().addClass('dashicons');
        if (type === 'success') {
            icon.addClass('dashicons-yes-alt');
            toast.removeClass('error').addClass('success');
        } else {
            icon.addClass('dashicons-warning');
            toast.removeClass('success').addClass('error');
        }

        // 设置消息
        toast.find('.notion-wp-toast-content').text(message);

        // 显示toast
        toast.addClass('show');

        // 3秒后自动隐藏
        setTimeout(function() {
            toast.removeClass('show');
        }, 3000);
    };

    // 关闭toast
    $('.notion-wp-toast-close').on('click', function() {
        $('#notion-wp-toast').removeClass('show');
    });

    // 传统界面功能（当React加载失败时启用）
    function initTraditionalInterface() {
        // 加载统计信息
        loadStats();
        
        // 测试连接功能
        $('#notion-test-connection').on('click', function() {
            var button = $(this);
            var apiKey = $('#notion_to_wordpress_api_key').val();
            var databaseId = $('#notion_to_wordpress_database_id').val();
            
            if (!apiKey || !databaseId) {
                window.showToast('error', '<?php esc_html_e('请输入API密钥和数据库ID', 'notion-to-wordpress'); ?>');
                return;
            }
            
            button.prop('disabled', true).html('<span class="dashicons dashicons-admin-network"></span> <?php esc_html_e('测试中...', 'notion-to-wordpress'); ?>');
            
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'notion_to_wordpress_test_connection',
                    api_key: apiKey,
                    database_id: databaseId,
                    nonce: '<?php echo wp_create_nonce('notion_to_wordpress_nonce'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        window.showToast('success', response.data.message);
                    } else {
                        window.showToast('error', response.data.message);
                    }
                },
                error: function() {
                    window.showToast('error', '<?php esc_html_e('测试连接时发生错误', 'notion-to-wordpress'); ?>');
                },
                complete: function() {
                    button.prop('disabled', false).html('<span class="dashicons dashicons-admin-network"></span> <?php esc_html_e('测试连接', 'notion-to-wordpress'); ?>');
                }
            });
        });
        
        // 同步操作功能
        $('.notion-wp-sync-btn').on('click', function() {
            var button = $(this);
            var syncType = button.data('sync-type');
            var originalText = button.data('original-text');
            
            var apiKey = $('#notion_to_wordpress_api_key').val();
            var databaseId = $('#notion_to_wordpress_database_id').val();
            
            if (!apiKey || !databaseId) {
                window.showToast('error', '<?php esc_html_e('请先配置API密钥和数据库ID', 'notion-to-wordpress'); ?>');
                return;
            }
            
            var confirmMessage = syncType === 'manual' ? 
                '<?php esc_html_e('确定要执行智能同步吗？（仅同步有变化的内容）', 'notion-to-wordpress'); ?>' : 
                '<?php esc_html_e('确定要执行完全同步吗？（同步所有内容，耗时较长）', 'notion-to-wordpress'); ?>';
            
            if (!confirm(confirmMessage)) {
                return;
            }
            
            // 禁用所有同步按钮
            $('.notion-wp-sync-btn').prop('disabled', true);
            button.find('.button-text').text('<?php esc_html_e('同步中...', 'notion-to-wordpress'); ?>');
            
            // 显示进度条
            $('#sync-progress').removeClass('notion-wp-hidden');
            
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'notion_to_wordpress_manual_sync',
                    incremental: syncType === 'manual' ? 1 : 0,
                    check_deletions: 1,
                    nonce: '<?php echo wp_create_nonce('notion_to_wordpress_nonce'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        window.showToast('success', response.data.message || '<?php esc_html_e('同步完成', 'notion-to-wordpress'); ?>');
                        loadStats(); // 重新加载统计信息
                    } else {
                        window.showToast('error', response.data.message || '<?php esc_html_e('同步失败', 'notion-to-wordpress'); ?>');
                    }
                },
                error: function() {
                    window.showToast('error', '<?php esc_html_e('同步过程中发生错误', 'notion-to-wordpress'); ?>');
                },
                complete: function() {
                    // 恢复按钮状态
                    $('.notion-wp-sync-btn').prop('disabled', false);
                    button.find('.button-text').text(originalText);
                    $('#sync-progress').addClass('notion-wp-hidden');
                }
            });
        });
    }
    
    // 加载统计信息
    function loadStats() {
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'notion_to_wordpress_get_stats',
                nonce: '<?php echo wp_create_nonce('notion_to_wordpress_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    var stats = response.data;
                    $('.stat-imported-count').text(stats.imported_count || '0');
                    $('.stat-published-count').text(stats.published_count || '0');
                    $('.stat-last-update').text(stats.last_update || '<?php esc_html_e('从未', 'notion-to-wordpress'); ?>');
                    $('.stat-next-run').text(stats.next_run || '<?php esc_html_e('未计划', 'notion-to-wordpress'); ?>');
                }
            },
            error: function() {
                console.log('Failed to load stats');
            }
        });
    }
    
    // 当React应用加载失败，传统界面显示时，初始化传统界面功能
    var originalCheckFunction = checkReactAppLoaded;
    checkReactAppLoaded = function() {
        const reactRoot = document.getElementById('notion-to-wordpress-react-root');
        const hasReactContent = reactRoot && (
            reactRoot.children.length > 0 || 
            reactRoot.innerHTML.trim().length > 0 ||
            reactRoot.hasChildNodes()
        );
        
        reactCheckAttempts++;
        
        if (hasReactContent) {
            // React应用已加载成功
            console.log('React应用加载成功');
            clearTimeout(reactLoadTimeout);
            // 确保传统界面隐藏
            $('#notion-wp-traditional-sidebar').hide();
            $('#notion-to-wordpress-settings-form').hide();
        } else if (reactCheckAttempts >= maxReactCheckAttempts) {
            // React应用加载失败，显示传统界面
            console.log('React应用加载失败，切换到传统界面');
            $('#notion-wp-traditional-sidebar').show();
            $('#notion-to-wordpress-settings-form').show();
            
            // 初始化传统界面功能
            initTraditionalInterface();
            
            // 显示加载失败提示
            if (window.showToast) {
                window.showToast('error', '<?php esc_html_e('React应用加载失败，已切换到备用界面。请刷新页面重试，或联系管理员。', 'notion-to-wordpress'); ?>');
            }
        } else {
            // 继续检查
            reactLoadTimeout = setTimeout(checkReactAppLoaded, 100);
        }
    };
});
</script>
