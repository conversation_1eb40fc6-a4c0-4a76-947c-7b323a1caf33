"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunknotion_to_wordpress"] = self["webpackChunknotion_to_wordpress"] || []).push([["katex-mermaid"],{

/***/ "./src/frontend/components/MathRenderer.ts":
/*!*************************************************!*\
  !*** ./src/frontend/components/MathRenderer.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MathRenderer: () => (/* binding */ MathRenderer),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   mathRenderer: () => (/* binding */ mathRenderer)\n/* harmony export */ });\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.symbol.js */ \"./node_modules/core-js/modules/es.symbol.js\");\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */ \"./node_modules/core-js/modules/es.symbol.description.js\");\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.symbol.iterator.js */ \"./node_modules/core-js/modules/es.symbol.iterator.js\");\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.symbol.to-primitive.js */ \"./node_modules/core-js/modules/es.symbol.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ \"./node_modules/core-js/modules/es.array.concat.js\");\n/* harmony import */ var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.array.filter.js */ \"./node_modules/core-js/modules/es.array.filter.js\");\n/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"./node_modules/core-js/modules/es.array.iterator.js\");\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.array.join.js */ \"./node_modules/core-js/modules/es.array.join.js\");\n/* harmony import */ var core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.date.to-primitive.js */ \"./node_modules/core-js/modules/es.date.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.number.constructor.js */ \"./node_modules/core-js/modules/es.number.constructor.js\");\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.object.get-own-property-descriptor.js */ \"./node_modules/core-js/modules/es.object.get-own-property-descriptor.js\");\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core-js/modules/es.object.get-own-property-descriptors.js */ \"./node_modules/core-js/modules/es.object.get-own-property-descriptors.js\");\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.object.get-prototype-of.js */ \"./node_modules/core-js/modules/es.object.get-prototype-of.js\");\n/* harmony import */ var core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! core-js/modules/es.object.keys.js */ \"./node_modules/core-js/modules/es.object.keys.js\");\n/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! core-js/modules/es.object.set-prototype-of.js */ \"./node_modules/core-js/modules/es.object.set-prototype-of.js\");\n/* harmony import */ var core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ \"./node_modules/core-js/modules/es.object.to-string.js\");\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core-js/modules/es.promise.js */ \"./node_modules/core-js/modules/es.promise.js\");\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! core-js/modules/es.regexp.to-string.js */ \"./node_modules/core-js/modules/es.regexp.to-string.js\");\n/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ \"./node_modules/core-js/modules/es.string.iterator.js\");\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! core-js/modules/es.string.starts-with.js */ \"./node_modules/core-js/modules/es.string.starts-with.js\");\n/* harmony import */ var core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_19__);\n/* harmony import */ var core_js_modules_es_string_trim_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! core-js/modules/es.string.trim.js */ \"./node_modules/core-js/modules/es.string.trim.js\");\n/* harmony import */ var core_js_modules_es_string_trim_js__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_trim_js__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! core-js/modules/web.dom-collections.for-each.js */ \"./node_modules/core-js/modules/web.dom-collections.for-each.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ../../shared/core/EventBus */ \"./src/shared/core/EventBus.ts\");\nvar _MathRenderer;\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 数学公式和图表渲染器 - 完整功能迁移版本\n *\n * 从原有katex-mermaid.js完全迁移所有功能，包括：\n * - KaTeX数学公式渲染（支持mhchem化学公式）\n * - Mermaid图表渲染\n * - 资源加载失败备用方案\n * - 智能兼容性检查\n * - 本地资源回退机制\n */\n\n\n\n// KaTeX配置选项\nvar KATEX_OPTIONS = {\n  displayMode: false,\n  throwOnError: false,\n  errorColor: '#cc0000',\n  strict: 'warn',\n  trust: false,\n  macros: {\n    '\\\\f': '#1f(#2)'\n  }\n};\n\n// Mermaid配置选项 - 与原版katex-mermaid.js保持一致\nvar MERMAID_CONFIG = {\n  startOnLoad: false,\n  theme: 'default',\n  securityLevel: 'loose',\n  fontFamily: 'Arial, sans-serif',\n  fontSize: 14,\n  flowchart: {\n    useMaxWidth: false,\n    // 修复：不强制使用最大宽度，让图表保持合适大小\n    htmlLabels: true,\n    curve: 'basis'\n  },\n  er: {\n    useMaxWidth: false // 修复：不强制使用最大宽度\n  },\n  sequence: {\n    useMaxWidth: false,\n    // 修复：不强制使用最大宽度\n    noteFontWeight: '14px',\n    actorFontSize: '14px',\n    messageFontSize: '16px',\n    wrap: true\n  },\n  // 添加全局配置确保图表大小合适\n  maxTextSize: 90000,\n  maxEdges: 100\n};\n\n/**\n * 资源回退管理器\n */\nvar ResourceFallbackManager = /*#__PURE__*/function () {\n  function ResourceFallbackManager() {\n    _classCallCheck(this, ResourceFallbackManager);\n  }\n  return _createClass(ResourceFallbackManager, null, [{\n    key: \"showCompatibilityTips\",\n    value:\n    /**\n     * 显示主题兼容性检查建议\n     */\n    function showCompatibilityTips() {\n      console.group('🔧 [Notion to WordPress] 主题兼容性检查建议');\n      console.info('如果数学公式或图表显示异常，请尝试以下解决方案：');\n      console.info('1. 确认当前主题正确调用了wp_footer()函数');\n      console.info('2. 检查主题是否与其他插件存在JavaScript冲突');\n      console.info('3. 尝试切换到WordPress默认主题（如Twenty Twenty-Three）测试');\n      console.info('4. 检查浏览器控制台是否有其他错误信息');\n      console.info('5. 确认网络连接正常，CDN资源可以正常访问');\n      console.groupEnd();\n    }\n\n    /**\n     * 动态加载CSS文件\n     */\n  }, {\n    key: \"loadFallbackCSS\",\n    value: function loadFallbackCSS(localPath) {\n      return new Promise(function (resolve, reject) {\n        var link = document.createElement('link');\n        link.rel = 'stylesheet';\n        link.type = 'text/css';\n        link.href = localPath;\n        link.onload = function () {\n          console.log('✅ 备用CSS加载成功:', localPath);\n          resolve();\n        };\n        link.onerror = function () {\n          console.error('❌ 备用CSS加载失败:', localPath);\n          reject(new Error('CSS加载失败'));\n        };\n        document.head.appendChild(link);\n      });\n    }\n\n    /**\n     * 动态加载JS文件\n     */\n  }, {\n    key: \"loadFallbackJS\",\n    value: function loadFallbackJS(localPath) {\n      return new Promise(function (resolve, reject) {\n        var script = document.createElement('script');\n        script.type = 'text/javascript';\n        script.src = localPath;\n        script.onload = function () {\n          console.log('✅ 备用JS加载成功:', localPath);\n          resolve();\n        };\n        script.onerror = function () {\n          console.error('❌ 备用JS加载失败:', localPath);\n          reject(new Error('JS加载失败'));\n        };\n        document.head.appendChild(script);\n      });\n    }\n\n    /**\n     * 按顺序加载KaTeX相关文件\n     */\n  }, {\n    key: \"loadKatexFallback\",\n    value: (function () {\n      var _loadKatexFallback = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        var basePath, _t;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.p = _context.n) {\n            case 0:\n              basePath = window.location.origin + '/wp-content/plugins/notion-to-wordpress/assets/vendor/katex/';\n              console.info('📦 [Notion to WordPress] 开始加载KaTeX本地备用资源...');\n              _context.p = 1;\n              _context.n = 2;\n              return this.loadFallbackCSS(basePath + 'katex.min.css');\n            case 2:\n              _context.n = 3;\n              return this.loadFallbackJS(basePath + 'katex.min.js');\n            case 3:\n              _context.n = 4;\n              return this.loadFallbackJS(basePath + 'mhchem.min.js');\n            case 4:\n              console.log('✅ [Notion to WordPress] KaTeX本地资源加载完成');\n              _context.n = 6;\n              break;\n            case 5:\n              _context.p = 5;\n              _t = _context.v;\n              console.error('❌ [Notion to WordPress] KaTeX本地资源加载失败:', _t);\n              throw _t;\n            case 6:\n              return _context.a(2);\n          }\n        }, _callee, this, [[1, 5]]);\n      }));\n      function loadKatexFallback() {\n        return _loadKatexFallback.apply(this, arguments);\n      }\n      return loadKatexFallback;\n    }()\n    /**\n     * 加载Mermaid备用资源\n     */\n    )\n  }, {\n    key: \"loadMermaidFallback\",\n    value: (function () {\n      var _loadMermaidFallback = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {\n        var basePath, _t2;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.p = _context2.n) {\n            case 0:\n              basePath = window.location.origin + '/wp-content/plugins/notion-to-wordpress/assets/vendor/mermaid/';\n              console.info('📦 [Notion to WordPress] 开始加载Mermaid本地备用资源...');\n              _context2.p = 1;\n              _context2.n = 2;\n              return this.loadFallbackJS(basePath + 'mermaid.min.js');\n            case 2:\n              console.log('✅ [Notion to WordPress] Mermaid本地资源加载完成');\n              _context2.n = 4;\n              break;\n            case 3:\n              _context2.p = 3;\n              _t2 = _context2.v;\n              console.error('❌ [Notion to WordPress] Mermaid本地资源加载失败:', _t2);\n              throw _t2;\n            case 4:\n              return _context2.a(2);\n          }\n        }, _callee2, this, [[1, 3]]);\n      }));\n      function loadMermaidFallback() {\n        return _loadMermaidFallback.apply(this, arguments);\n      }\n      return loadMermaidFallback;\n    }())\n  }]);\n}();\nvar MathRenderer = /*#__PURE__*/function () {\n  function MathRenderer() {\n    _classCallCheck(this, MathRenderer);\n    _defineProperty(this, \"katexLoaded\", false);\n    _defineProperty(this, \"mermaidLoaded\", false);\n    _defineProperty(this, \"katexLoadPromise\", null);\n    _defineProperty(this, \"mermaidLoadPromise\", null);\n    if (MathRenderer.instance) {\n      return MathRenderer.instance;\n    }\n    MathRenderer.instance = this;\n    this.init();\n  }\n  return _createClass(MathRenderer, [{\n    key: \"init\",\n    value: function init() {\n      var _this = this;\n      // 监听数学公式渲染事件\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.eventBus.on('frontend:math:render', this.renderMath.bind(this));\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.eventBus.on('frontend:mermaid:render', this.renderMermaid.bind(this));\n\n      // 页面加载完成后自动检测和渲染\n      if (document.readyState === 'loading') {\n        document.addEventListener('DOMContentLoaded', function () {\n          _this.detectAndRender();\n        });\n      } else {\n        this.detectAndRender();\n      }\n      console.log('🧮 [数学渲染器] 已初始化');\n    }\n\n    /**\n     * 检测并渲染页面中的数学公式和图表\n     */\n  }, {\n    key: \"detectAndRender\",\n    value: function detectAndRender() {\n      var _this2 = this;\n      // 检测KaTeX公式 - 支持多种选择器\n      var mathSelectors = ['.notion-equation', '.katex-math', '.math-expression', '[data-math]', '.wp-block-notion-math'];\n      var mathElements = document.querySelectorAll(mathSelectors.join(', '));\n      if (mathElements.length > 0) {\n        console.log(\"\\uD83E\\uDDEE \\u53D1\\u73B0 \".concat(mathElements.length, \" \\u4E2A\\u6570\\u5B66\\u516C\\u5F0F\\u5143\\u7D20\"));\n        this.loadKaTeX().then(function () {\n          mathElements.forEach(function (element) {\n            _this2.renderMathElement(element);\n          });\n        }).catch(function (error) {\n          console.error('KaTeX加载失败:', error);\n          ResourceFallbackManager.showCompatibilityTips();\n        });\n      }\n\n      // 检测Mermaid图表 - 支持多种选择器\n      var mermaidSelectors = ['.notion-mermaid', '.mermaid-chart', '.diagram', '[data-mermaid]', '.wp-block-notion-mermaid'];\n      var mermaidElements = document.querySelectorAll(mermaidSelectors.join(', '));\n      if (mermaidElements.length > 0) {\n        console.log(\"\\uD83D\\uDCCA \\u53D1\\u73B0 \".concat(mermaidElements.length, \" \\u4E2A\\u56FE\\u8868\\u5143\\u7D20\"));\n        this.loadMermaid().then(function () {\n          mermaidElements.forEach(function (element) {\n            _this2.renderMermaidElement(element);\n          });\n        }).catch(function (error) {\n          console.error('Mermaid加载失败:', error);\n          ResourceFallbackManager.showCompatibilityTips();\n        });\n      }\n\n      // 检测化学公式\n      var chemElements = document.querySelectorAll('.notion-chemistry, .chemistry, [data-chemistry]');\n      if (chemElements.length > 0) {\n        console.log(\"\\uD83E\\uDDEA \\u53D1\\u73B0 \".concat(chemElements.length, \" \\u4E2A\\u5316\\u5B66\\u516C\\u5F0F\\u5143\\u7D20\"));\n        this.loadKaTeX().then(function () {\n          chemElements.forEach(function (element) {\n            _this2.renderChemistryElement(element);\n          });\n        }).catch(function (error) {\n          console.error('化学公式渲染失败:', error);\n        });\n      }\n    }\n\n    /**\n     * 加载KaTeX库（支持CDN和本地回退）\n     */\n  }, {\n    key: \"loadKaTeX\",\n    value: (function () {\n      var _loadKaTeX = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {\n        return _regenerator().w(function (_context3) {\n          while (1) switch (_context3.n) {\n            case 0:\n              if (!this.katexLoaded) {\n                _context3.n = 1;\n                break;\n              }\n              return _context3.a(2);\n            case 1:\n              if (!this.katexLoadPromise) {\n                _context3.n = 2;\n                break;\n              }\n              return _context3.a(2, this.katexLoadPromise);\n            case 2:\n              if (!window.katex) {\n                _context3.n = 3;\n                break;\n              }\n              this.katexLoaded = true;\n              return _context3.a(2);\n            case 3:\n              console.log('📦 [KaTeX] 开始加载KaTeX资源...');\n              this.katexLoadPromise = this.performKatexLoad();\n              return _context3.a(2, this.katexLoadPromise);\n          }\n        }, _callee3, this);\n      }));\n      function loadKaTeX() {\n        return _loadKaTeX.apply(this, arguments);\n      }\n      return loadKaTeX;\n    }()\n    /**\n     * 执行KaTeX加载\n     */\n    )\n  }, {\n    key: \"performKatexLoad\",\n    value: (function () {\n      var _performKatexLoad = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4() {\n        var _t3, _t4;\n        return _regenerator().w(function (_context4) {\n          while (1) switch (_context4.p = _context4.n) {\n            case 0:\n              _context4.p = 0;\n              _context4.n = 1;\n              return this.loadKatexFromCDN();\n            case 1:\n              console.log('✅ [KaTeX] CDN资源加载成功');\n              _context4.n = 6;\n              break;\n            case 2:\n              _context4.p = 2;\n              _t3 = _context4.v;\n              console.warn('⚠️ [KaTeX] CDN加载失败，尝试本地资源:', _t3);\n              _context4.p = 3;\n              _context4.n = 4;\n              return ResourceFallbackManager.loadKatexFallback();\n            case 4:\n              console.log('✅ [KaTeX] 本地资源加载成功');\n              _context4.n = 6;\n              break;\n            case 5:\n              _context4.p = 5;\n              _t4 = _context4.v;\n              console.error('❌ [KaTeX] 本地资源也加载失败:', _t4);\n              ResourceFallbackManager.showCompatibilityTips();\n              throw new Error('KaTeX加载完全失败');\n            case 6:\n              if (window.katex) {\n                _context4.n = 7;\n                break;\n              }\n              throw new Error('KaTeX加载后仍不可用');\n            case 7:\n              this.katexLoaded = true;\n              console.log('🎉 [KaTeX] 加载完成并可用');\n            case 8:\n              return _context4.a(2);\n          }\n        }, _callee4, this, [[3, 5], [0, 2]]);\n      }));\n      function performKatexLoad() {\n        return _performKatexLoad.apply(this, arguments);\n      }\n      return performKatexLoad;\n    }()\n    /**\n     * 从CDN加载KaTeX\n     */\n    )\n  }, {\n    key: \"loadKatexFromCDN\",\n    value: (function () {\n      var _loadKatexFromCDN = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5() {\n        var CDN_BASE, cssPromise, jsPromise, mhchemPromise;\n        return _regenerator().w(function (_context5) {\n          while (1) switch (_context5.n) {\n            case 0:\n              CDN_BASE = 'https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/'; // 1. 加载CSS\n              cssPromise = new Promise(function (resolve, reject) {\n                var link = document.createElement('link');\n                link.rel = 'stylesheet';\n                link.href = CDN_BASE + 'katex.min.css';\n                link.onload = function () {\n                  return resolve();\n                };\n                link.onerror = function () {\n                  return reject(new Error('KaTeX CSS加载失败'));\n                };\n                document.head.appendChild(link);\n              }); // 2. 加载主JS\n              jsPromise = new Promise(function (resolve, reject) {\n                var script = document.createElement('script');\n                script.src = CDN_BASE + 'katex.min.js';\n                script.onload = function () {\n                  return resolve();\n                };\n                script.onerror = function () {\n                  return reject(new Error('KaTeX JS加载失败'));\n                };\n                document.head.appendChild(script);\n              }); // 等待CSS和JS都加载完成\n              _context5.n = 1;\n              return Promise.all([cssPromise, jsPromise]);\n            case 1:\n              // 3. 加载mhchem扩展（化学公式支持）\n              mhchemPromise = new Promise(function (resolve) {\n                var script = document.createElement('script');\n                script.src = 'https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/mhchem.min.js';\n                script.onload = function () {\n                  return resolve();\n                };\n                script.onerror = function () {\n                  console.warn('mhchem扩展加载失败，化学公式功能可能不可用');\n                  resolve(); // 不阻塞主要功能\n                };\n                document.head.appendChild(script);\n              });\n              _context5.n = 2;\n              return mhchemPromise;\n            case 2:\n              return _context5.a(2);\n          }\n        }, _callee5);\n      }));\n      function loadKatexFromCDN() {\n        return _loadKatexFromCDN.apply(this, arguments);\n      }\n      return loadKatexFromCDN;\n    }()\n    /**\n     * 加载Mermaid库（支持CDN和本地回退）\n     */\n    )\n  }, {\n    key: \"loadMermaid\",\n    value: (function () {\n      var _loadMermaid = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6() {\n        return _regenerator().w(function (_context6) {\n          while (1) switch (_context6.n) {\n            case 0:\n              if (!this.mermaidLoaded) {\n                _context6.n = 1;\n                break;\n              }\n              return _context6.a(2);\n            case 1:\n              if (!this.mermaidLoadPromise) {\n                _context6.n = 2;\n                break;\n              }\n              return _context6.a(2, this.mermaidLoadPromise);\n            case 2:\n              if (!window.mermaid) {\n                _context6.n = 3;\n                break;\n              }\n              this.mermaidLoaded = true;\n              return _context6.a(2);\n            case 3:\n              console.log('📊 [Mermaid] 开始加载Mermaid资源...');\n              this.mermaidLoadPromise = this.performMermaidLoad();\n              return _context6.a(2, this.mermaidLoadPromise);\n          }\n        }, _callee6, this);\n      }));\n      function loadMermaid() {\n        return _loadMermaid.apply(this, arguments);\n      }\n      return loadMermaid;\n    }()\n    /**\n     * 执行Mermaid加载\n     */\n    )\n  }, {\n    key: \"performMermaidLoad\",\n    value: (function () {\n      var _performMermaidLoad = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee7() {\n        var _t5, _t6;\n        return _regenerator().w(function (_context7) {\n          while (1) switch (_context7.p = _context7.n) {\n            case 0:\n              _context7.p = 0;\n              _context7.n = 1;\n              return this.loadMermaidFromCDN();\n            case 1:\n              console.log('✅ [Mermaid] CDN资源加载成功');\n              _context7.n = 6;\n              break;\n            case 2:\n              _context7.p = 2;\n              _t5 = _context7.v;\n              console.warn('⚠️ [Mermaid] CDN加载失败，尝试本地资源:', _t5);\n              _context7.p = 3;\n              _context7.n = 4;\n              return ResourceFallbackManager.loadMermaidFallback();\n            case 4:\n              console.log('✅ [Mermaid] 本地资源加载成功');\n              _context7.n = 6;\n              break;\n            case 5:\n              _context7.p = 5;\n              _t6 = _context7.v;\n              console.error('❌ [Mermaid] 本地资源也加载失败:', _t6);\n              ResourceFallbackManager.showCompatibilityTips();\n              throw new Error('Mermaid加载完全失败');\n            case 6:\n              if (window.mermaid) {\n                _context7.n = 7;\n                break;\n              }\n              throw new Error('Mermaid加载后仍不可用');\n            case 7:\n              // 初始化Mermaid配置\n              window.mermaid.initialize(MERMAID_CONFIG);\n              this.mermaidLoaded = true;\n              console.log('🎉 [Mermaid] 加载完成并可用');\n            case 8:\n              return _context7.a(2);\n          }\n        }, _callee7, this, [[3, 5], [0, 2]]);\n      }));\n      function performMermaidLoad() {\n        return _performMermaidLoad.apply(this, arguments);\n      }\n      return performMermaidLoad;\n    }()\n    /**\n     * 从CDN加载Mermaid\n     */\n    )\n  }, {\n    key: \"loadMermaidFromCDN\",\n    value: (function () {\n      var _loadMermaidFromCDN = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee8() {\n        return _regenerator().w(function (_context8) {\n          while (1) switch (_context8.n) {\n            case 0:\n              return _context8.a(2, new Promise(function (resolve, reject) {\n                var script = document.createElement('script');\n                script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js';\n                script.onload = function () {\n                  return resolve();\n                };\n                script.onerror = function () {\n                  return reject(new Error('Mermaid CDN加载失败'));\n                };\n                document.head.appendChild(script);\n              }));\n          }\n        }, _callee8);\n      }));\n      function loadMermaidFromCDN() {\n        return _loadMermaidFromCDN.apply(this, arguments);\n      }\n      return loadMermaidFromCDN;\n    }()\n    /**\n     * 渲染数学公式（事件处理器）\n     */\n    )\n  }, {\n    key: \"renderMath\",\n    value: function renderMath(_event, data) {\n      this.renderMathElement(data.element);\n    }\n\n    /**\n     * 渲染图表（事件处理器）\n     */\n  }, {\n    key: \"renderMermaid\",\n    value: function renderMermaid(_event, data) {\n      this.renderMermaidElement(data.element);\n    }\n\n    /**\n     * 渲染单个数学公式元素\n     */\n  }, {\n    key: \"renderMathElement\",\n    value: function renderMathElement(element) {\n      if (!this.katexLoaded || !window.katex) {\n        console.warn('KaTeX未加载，无法渲染数学公式');\n        return;\n      }\n\n      // 获取数学表达式\n      var expression = element.textContent || element.getAttribute('data-expression') || element.getAttribute('data-math') || element.innerHTML;\n      if (!expression || expression.trim() === '') {\n        console.warn('数学表达式为空，跳过渲染');\n        return;\n      }\n      try {\n        // 判断是否为行内公式\n        var isInline = element.classList.contains('inline') || element.classList.contains('katex-inline') || element.hasAttribute('data-inline');\n\n        // 使用完整的KaTeX配置\n        var options = _objectSpread(_objectSpread({}, KATEX_OPTIONS), {}, {\n          displayMode: !isInline,\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: 'warn'\n        });\n\n        // 渲染数学公式\n        window.katex.render(expression, element, options);\n\n        // 添加成功渲染的标记\n        element.classList.add('katex-rendered');\n        element.setAttribute('data-rendered', 'true');\n        console.log('✅ 数学公式渲染成功:', expression.substring(0, 50) + '...');\n      } catch (error) {\n        console.error('❌ KaTeX渲染错误:', error);\n\n        // 显示错误信息\n        element.innerHTML = \"\\n        <span style=\\\"color: #cc0000; background: #ffe6e6; padding: 2px 4px; border-radius: 3px; font-family: monospace;\\\">\\n          \\u6570\\u5B66\\u516C\\u5F0F\\u9519\\u8BEF: \".concat(expression.substring(0, 100)).concat(expression.length > 100 ? '...' : '', \"\\n        </span>\\n      \");\n        element.classList.add('katex-error');\n      }\n    }\n\n    /**\n     * 渲染化学公式元素\n     */\n  }, {\n    key: \"renderChemistryElement\",\n    value: function renderChemistryElement(element) {\n      if (!this.katexLoaded || !window.katex) {\n        console.warn('KaTeX未加载，无法渲染化学公式');\n        return;\n      }\n\n      // 获取化学表达式\n      var expression = element.textContent || element.getAttribute('data-chemistry') || element.getAttribute('data-chem');\n      if (!expression || expression.trim() === '') {\n        console.warn('化学表达式为空，跳过渲染');\n        return;\n      }\n      try {\n        // 化学公式通常使用mhchem语法，需要包装在\\ce{}中\n        var chemExpression = expression.startsWith('\\\\ce{') ? expression : \"\\\\ce{\".concat(expression, \"}\");\n        var options = _objectSpread(_objectSpread({}, KATEX_OPTIONS), {}, {\n          displayMode: false,\n          throwOnError: false\n        });\n        window.katex.render(chemExpression, element, options);\n        element.classList.add('chemistry-rendered');\n        element.setAttribute('data-rendered', 'true');\n        console.log('✅ 化学公式渲染成功:', expression);\n      } catch (error) {\n        console.error('❌ 化学公式渲染错误:', error);\n        element.innerHTML = \"\\n        <span style=\\\"color: #cc0000; background: #ffe6e6; padding: 2px 4px; border-radius: 3px; font-family: monospace;\\\">\\n          \\u5316\\u5B66\\u516C\\u5F0F\\u9519\\u8BEF: \".concat(expression, \"\\n        </span>\\n      \");\n        element.classList.add('chemistry-error');\n      }\n    }\n\n    /**\n     * 渲染单个Mermaid图表元素\n     */\n  }, {\n    key: \"renderMermaidElement\",\n    value: function renderMermaidElement(element) {\n      if (!this.mermaidLoaded || !window.mermaid) {\n        console.warn('Mermaid未加载，无法渲染图表');\n        return;\n      }\n\n      // 获取图表代码\n      var diagram = element.textContent || element.getAttribute('data-mermaid') || element.getAttribute('data-code') || element.getAttribute('data-diagram') || element.innerHTML;\n      if (!diagram || diagram.trim() === '') {\n        console.warn('图表代码为空，跳过渲染');\n        return;\n      }\n      try {\n        // 生成唯一ID（使用现代方法替代已弃用的substr）\n        var id = \"mermaid-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substring(2, 11));\n\n        // 清空元素内容，显示加载状态\n        element.innerHTML = '<div class=\"mermaid-loading\">正在渲染图表...</div>';\n        element.classList.add('mermaid-rendering');\n\n        // 渲染图表\n        window.mermaid.render(id, diagram).then(function (result) {\n          // 渲染成功\n          element.innerHTML = result.svg;\n          element.classList.remove('mermaid-rendering');\n          element.classList.add('mermaid-rendered');\n          element.setAttribute('data-rendered', 'true');\n\n          // 添加响应式支持\n          var svg = element.querySelector('svg');\n          if (svg) {\n            svg.style.maxWidth = '100%';\n            svg.style.height = 'auto';\n          }\n          console.log('✅ Mermaid图表渲染成功');\n        }).catch(function (error) {\n          // 渲染失败\n          console.error('❌ Mermaid渲染错误:', error);\n          element.innerHTML = \"\\n          <div style=\\\"color: #cc0000; background: #ffe6e6; padding: 10px; border-radius: 5px; border: 1px solid #ffcccc;\\\">\\n            <strong>\\u56FE\\u8868\\u6E32\\u67D3\\u9519\\u8BEF</strong><br>\\n            <small>\".concat(error.message || '未知错误', \"</small><br>\\n            <details style=\\\"margin-top: 5px;\\\">\\n              <summary style=\\\"cursor: pointer;\\\">\\u67E5\\u770B\\u539F\\u59CB\\u4EE3\\u7801</summary>\\n              <pre style=\\\"background: #f5f5f5; padding: 5px; margin-top: 5px; border-radius: 3px; font-size: 12px;\\\">\").concat(diagram, \"</pre>\\n            </details>\\n          </div>\\n        \");\n          element.classList.remove('mermaid-rendering');\n          element.classList.add('mermaid-error');\n        });\n      } catch (error) {\n        console.error('❌ Mermaid渲染异常:', error);\n        element.innerHTML = \"\\n        <div style=\\\"color: #cc0000; background: #ffe6e6; padding: 10px; border-radius: 5px; border: 1px solid #ffcccc;\\\">\\n          <strong>\\u56FE\\u8868\\u6E32\\u67D3\\u5F02\\u5E38</strong><br>\\n          <small>\\u8BF7\\u68C0\\u67E5\\u56FE\\u8868\\u8BED\\u6CD5\\u662F\\u5426\\u6B63\\u786E</small>\\n        </div>\\n      \";\n        element.classList.add('mermaid-error');\n      }\n    }\n\n    /**\n     * 手动渲染指定元素\n     */\n  }, {\n    key: \"renderElement\",\n    value: function renderElement(element) {\n      var _this3 = this;\n      if (element.classList.contains('notion-equation') || element.classList.contains('katex-math') || element.hasAttribute('data-math')) {\n        this.loadKaTeX().then(function () {\n          _this3.renderMathElement(element);\n        }).catch(console.error);\n      } else if (element.classList.contains('notion-mermaid') || element.classList.contains('mermaid-chart') || element.hasAttribute('data-mermaid')) {\n        this.loadMermaid().then(function () {\n          _this3.renderMermaidElement(element);\n        }).catch(console.error);\n      } else if (element.classList.contains('notion-chemistry') || element.classList.contains('chemistry') || element.hasAttribute('data-chemistry')) {\n        this.loadKaTeX().then(function () {\n          _this3.renderChemistryElement(element);\n        }).catch(console.error);\n      }\n    }\n\n    /**\n     * 重新渲染所有元素\n     */\n  }, {\n    key: \"reRenderAll\",\n    value: function reRenderAll() {\n      console.log('🔄 重新渲染所有数学公式和图表...');\n      this.detectAndRender();\n    }\n\n    /**\n     * 渲染所有数学公式 - 兼容原版API\n     */\n  }, {\n    key: \"renderAllMath\",\n    value: function renderAllMath() {\n      var _this4 = this;\n      console.log('🧮 开始渲染所有数学公式...');\n\n      // 检测KaTeX公式 - 支持多种选择器\n      var mathSelectors = ['.notion-equation-inline:not(.katex-rendered)', '.notion-equation-block:not(.katex-rendered)', '.notion-equation:not(.katex-rendered)', '.katex-math:not(.katex-rendered)', '.math-expression:not(.katex-rendered)', '[data-math]:not(.katex-rendered)', '.wp-block-notion-math:not(.katex-rendered)'];\n      var mathElements = document.querySelectorAll(mathSelectors.join(', '));\n      console.log(\"\\uD83D\\uDCCA \\u627E\\u5230 \".concat(mathElements.length, \" \\u4E2A\\u672A\\u6E32\\u67D3\\u7684\\u6570\\u5B66\\u516C\\u5F0F\"));\n      if (mathElements.length > 0) {\n        this.loadKaTeX().then(function () {\n          mathElements.forEach(function (element) {\n            _this4.renderMathElement(element);\n          });\n        }).catch(function (error) {\n          console.error('KaTeX加载失败:', error);\n        });\n      }\n    }\n\n    /**\n     * 渲染所有Mermaid图表 - 兼容原版API\n     */\n  }, {\n    key: \"renderAllMermaid\",\n    value: function renderAllMermaid() {\n      var _this5 = this;\n      console.log('🎨 开始渲染所有Mermaid图表...');\n\n      // 检测Mermaid图表 - 支持多种选择器\n      var mermaidSelectors = ['.notion-mermaid:not(.mermaid-rendered)', '.mermaid-chart:not(.mermaid-rendered)', '.diagram:not(.mermaid-rendered)', '[data-mermaid]:not(.mermaid-rendered)', '.wp-block-notion-mermaid:not(.mermaid-rendered)', '.mermaid:not(.mermaid-rendered)'];\n      var mermaidElements = document.querySelectorAll(mermaidSelectors.join(', '));\n      console.log(\"\\uD83D\\uDCCA \\u627E\\u5230 \".concat(mermaidElements.length, \" \\u4E2A\\u672A\\u6E32\\u67D3\\u7684Mermaid\\u56FE\\u8868\"));\n      if (mermaidElements.length > 0) {\n        this.loadMermaid().then(function () {\n          mermaidElements.forEach(function (element) {\n            _this5.renderMermaidElement(element);\n          });\n        }).catch(function (error) {\n          console.error('Mermaid加载失败:', error);\n        });\n      }\n    }\n\n    /**\n     * 获取渲染状态\n     */\n  }, {\n    key: \"getStatus\",\n    value: function getStatus() {\n      return {\n        katexLoaded: this.katexLoaded,\n        mermaidLoaded: this.mermaidLoaded,\n        mathElements: document.querySelectorAll('.notion-equation, .katex-math, [data-math]').length,\n        mermaidElements: document.querySelectorAll('.notion-mermaid, .mermaid-chart, [data-mermaid]').length,\n        chemElements: document.querySelectorAll('.notion-chemistry, .chemistry, [data-chemistry]').length\n      };\n    }\n\n    /**\n     * 销毁实例\n     */\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.eventBus.off('frontend:math:render', this.renderMath.bind(this));\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.eventBus.off('frontend:mermaid:render', this.renderMermaid.bind(this));\n      MathRenderer.instance = null;\n      console.log('🧮 [数学渲染器] 已销毁');\n    }\n\n    /**\n     * 获取单例实例\n     */\n  }], [{\n    key: \"getInstance\",\n    value: function getInstance() {\n      if (!MathRenderer.instance) {\n        MathRenderer.instance = new MathRenderer();\n      }\n      return MathRenderer.instance;\n    }\n  }]);\n}();\n\n// 导出单例实例\n_MathRenderer = MathRenderer;\n_defineProperty(MathRenderer, \"instance\", null);\nvar mathRenderer = new MathRenderer();\n\n// 全局API暴露 - 保持与原版katex-mermaid.js的兼容性\nif (typeof window !== 'undefined') {\n  // 暴露KaTeX相关函数到全局作用域，供调试和测试使用\n  window.NotionToWordPressKaTeX = {\n    renderAllKatex: function renderAllKatex() {\n      return mathRenderer.renderAllMath();\n    },\n    renderKatexElement: function renderKatexElement(element) {\n      return mathRenderer.renderMathElement(element);\n    },\n    getInstance: function getInstance() {\n      return mathRenderer;\n    }\n  };\n\n  // 暴露Mermaid函数到全局作用域\n  window.NotionToWordPressMermaid = {\n    initMermaid: function initMermaid() {\n      return mathRenderer.renderAllMermaid();\n    },\n    getInstance: function getInstance() {\n      return mathRenderer;\n    }\n  };\n\n  // 添加资源状态检查函数\n  window.NotionResourceChecker = {\n    checkAllResources: function checkAllResources() {\n      console.log('🔍 [Notion资源检查] 开始检查关键资源状态...');\n      var results = {\n        katex: !!window.katex,\n        mermaid: !!window.mermaid,\n        database_css: !!document.querySelector('link[href*=\"notion-database.css\"]'),\n        latex_css: !!document.querySelector('link[href*=\"latex-styles.css\"]') || !!document.querySelector('link[href*=\"math-renderer\"]')\n      };\n      console.log('📊 [资源状态]', results);\n      if (!results.katex) {\n        console.warn('⚠️ KaTeX未加载，数学公式可能无法显示');\n      }\n      if (!results.mermaid) {\n        console.warn('⚠️ Mermaid未加载，图表可能无法显示');\n      }\n      if (!results.database_css) {\n        console.warn('⚠️ 数据库CSS未加载，数据库视图可能显示异常');\n      }\n      return results;\n    }\n  };\n}\n\n// 自动初始化（兼容原有行为）\nif (document.readyState === 'loading') {\n  document.addEventListener('DOMContentLoaded', function () {\n    MathRenderer.getInstance();\n  });\n} else {\n  MathRenderer.getInstance();\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MathRenderer);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/frontend/components/MathRenderer.ts\n\n}");

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors","common"], () => (__webpack_exec__("./src/frontend/components/MathRenderer.ts")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);