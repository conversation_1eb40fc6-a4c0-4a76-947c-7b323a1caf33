/**
 * 看板视图React组件
 * 
 * 支持按状态分组显示、动态列管理和卡片交互
 */

import { useState, useMemo, useCallback } from 'react';
import type {
  BoardViewProps,
  DatabaseRecord,
  DatabaseInfo
} from '../types';

// 看板列组件Props
interface BoardColumnProps {
  title: string;
  records: DatabaseRecord[];
  databaseInfo: DatabaseInfo | null;
  onRecordClick?: (record: DatabaseRecord) => void;
  showProperties?: string[];
  hideProperties?: string[];
}

// 看板卡片组件Props
interface BoardCardProps {
  record: DatabaseRecord;
  databaseInfo: DatabaseInfo | null;
  onClick?: (record: DatabaseRecord) => void;
  showProperties?: string[];
  hideProperties?: string[];
}

// 记录属性组件Props
interface RecordPropertiesProps {
  record: DatabaseRecord;
  databaseInfo: DatabaseInfo | null;
  showProperties?: string[];
  hideProperties?: string[];
  maxProperties?: number;
}

/**
 * 按状态分组记录
 */
function groupRecordsByStatus(
  records: DatabaseRecord[],
  _databaseInfo: DatabaseInfo | null
): Record<string, DatabaseRecord[]> {
  const grouped: Record<string, DatabaseRecord[]> = {};

  records.forEach(record => {
    // 查找状态属性
    let status = '未分类';

    Object.entries(record.properties).forEach(([name, value]) => {
      if (
        name.toLowerCase().includes('status') ||
        name.toLowerCase().includes('状态') ||
        name.toLowerCase().includes('stage') ||
        name.toLowerCase().includes('phase')
      ) {
        if (value && typeof value === 'object' && 'name' in value) {
          status = value.name;
        } else if (typeof value === 'string') {
          status = value;
        }
      }
    });

    if (!grouped[status]) {
      grouped[status] = [];
    }
    grouped[status].push(record);
  });

  return grouped;
}

/**
 * 记录标题组件
 */
function RecordTitle({ record }: { record: DatabaseRecord }) {
  // 查找标题属性
  const getTitle = useCallback((rec: DatabaseRecord): string => {
    for (const [name, value] of Object.entries(rec.properties)) {
      if (name.toLowerCase().includes('title') || 
          name.toLowerCase().includes('名称') ||
          name.toLowerCase().includes('name')) {
        if (value && typeof value === 'object' && 'title' in value && Array.isArray(value.title)) {
          return value.title.map((item: { plain_text?: string }) => item.plain_text || '').join('') || '无标题';
        } else if (value && typeof value === 'object' && 'plain_text' in value) {
          return value.plain_text || '无标题';
        } else if (typeof value === 'string') {
          return value || '无标题';
        }
      }
    }
    return '无标题';
  }, []);

  const title = getTitle(record);

  return (
    <div className="notion-record-title" title={title}>
      {title}
    </div>
  );
}

/**
 * 记录属性组件
 */
function RecordProperties({ 
  record, 
  databaseInfo, 
  showProperties = [], 
  hideProperties = [],
  maxProperties = 3 
}: RecordPropertiesProps) {
  // 获取可见属性
  const visibleProperties = useMemo(() => {
    if (!databaseInfo?.properties) return [];

    const properties: Array<{ name: string; value: unknown; type: string }> = [];

    Object.entries(record.properties).forEach(([name, value]) => {
      // 跳过标题属性和状态属性（已在其他地方显示）
      if (name.toLowerCase().includes('title') || 
          name.toLowerCase().includes('名称') ||
          name.toLowerCase().includes('name') ||
          name.toLowerCase().includes('status') ||
          name.toLowerCase().includes('状态')) {
        return;
      }

      // 检查是否应该显示此属性
      const shouldShow = showProperties.length === 0 || showProperties.includes(name);
      const shouldHide = hideProperties.includes(name);

      if (shouldShow && !shouldHide && value !== null && value !== undefined) {
        const propertyConfig = databaseInfo.properties[name];
        properties.push({
          name,
          value,
          type: propertyConfig?.type || 'text',
        });
      }
    });

    return properties.slice(0, maxProperties);
  }, [record.properties, databaseInfo, showProperties, hideProperties, maxProperties]);

  // 格式化属性值
  const formatPropertyValue = useCallback((value: unknown, type: string): string => {
    if (value === null || value === undefined) return '';

    switch (type) {
      case 'rich_text':
        if (Array.isArray(value)) {
          return value.map(item => item.plain_text || '').join('');
        }
        return value?.plain_text || String(value);
      
      case 'number':
        return typeof value === 'number' ? value.toLocaleString() : String(value);
      
      case 'select':
        return value?.name || String(value);
      
      case 'multi_select':
        if (Array.isArray(value)) {
          return value.map(item => item.name || item).join(', ');
        }
        return String(value);
      
      case 'date':
        if (value?.start) {
          return new Date(value.start).toLocaleDateString();
        }
        return String(value);
      
      case 'checkbox':
        return value ? '✓' : '✗';
      
      case 'url':
        return value ? String(value) : '';
      
      case 'email':
        return value ? String(value) : '';
      
      case 'phone_number':
        return value ? String(value) : '';
      
      default:
        return String(value);
    }
  }, []);

  if (visibleProperties.length === 0) {
    return null;
  }

  return (
    <div className="notion-record-properties">
      {visibleProperties.map((property, index) => {
        const formattedValue = formatPropertyValue(property.value, property.type);

        if (!formattedValue) return null;

        return (
          <div key={index} className="notion-record-property">
            <span className="notion-property-label">{property.name}:</span>
            <span className={`notion-property-value property-type-${property.type}`}>
              {formattedValue}
            </span>
          </div>
        );
      })}
    </div>
  );
}

/**
 * 看板卡片组件
 */
function BoardCard({
  record,
  databaseInfo,
  onClick,
  showProperties,
  hideProperties
}: BoardCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = useCallback(() => {
    onClick?.(record);
  }, [record, onClick]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  }, [handleClick]);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  return (
    <div
      className={`notion-board-card ${onClick ? 'notion-board-card-interactive' : ''} ${isHovered ? 'hovered' : ''}`}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? handleKeyDown : undefined}
      data-record-id={record.id}
    >
      <RecordTitle record={record} />
      <RecordProperties
        record={record}
        databaseInfo={databaseInfo}
        showProperties={showProperties}
        hideProperties={hideProperties}
        maxProperties={3}
      />
    </div>
  );
}

/**
 * 看板列组件
 */
function BoardColumn({
  title,
  records,
  databaseInfo,
  onRecordClick,
  showProperties,
  hideProperties
}: BoardColumnProps) {
  return (
    <div className="notion-board-column">
      <div className="notion-board-header">
        <span className="column-title">{title}</span>
        <span className="column-count">({records.length})</span>
      </div>

      <div className="notion-board-list">
        {records.length === 0 ? (
          <div className="empty-column">
            <div className="empty-icon">📋</div>
            <div className="empty-message">暂无卡片</div>
          </div>
        ) : (
          records.map((record) => (
            <BoardCard
              key={record.id}
              record={record}
              databaseInfo={databaseInfo}
              onClick={onRecordClick}
              showProperties={showProperties}
              hideProperties={hideProperties}
            />
          ))
        )}
      </div>
    </div>
  );
}

/**
 * 看板视图主组件
 */
export function BoardView({
  records,
  databaseInfo,
  options,
  onRecordClick,
  className = '',
  children,
  ...props
}: BoardViewProps) {
  // 按状态分组记录
  const groupedRecords = useMemo(() => {
    return groupRecordsByStatus(records, databaseInfo);
  }, [records, databaseInfo]);

  // 获取状态列表（按记录数量排序）
  const statusColumns = useMemo(() => {
    return Object.entries(groupedRecords)
      .sort(([, a], [, b]) => b.length - a.length) // 按记录数量降序排列
      .map(([status, statusRecords]) => ({
        status,
        records: statusRecords,
      }));
  }, [groupedRecords]);

  // 如果没有记录，显示空状态
  if (records.length === 0) {
    return (
      <div className={`notion-database-view-board ${className}`} {...props}>
        <div className="empty-board">
          <div className="empty-icon">📋</div>
          <div className="empty-message">暂无记录</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`notion-database-view-board ${className}`} {...props}>
      <div className="notion-database-board">
        {statusColumns.map(({ status, records: statusRecords }) => (
          <BoardColumn
            key={status}
            title={status}
            records={statusRecords}
            databaseInfo={databaseInfo}
            onRecordClick={onRecordClick}
            showProperties={options?.showProperties}
            hideProperties={options?.hideProperties}
          />
        ))}
      </div>
      {children}
    </div>
  );
}

export default BoardView;
