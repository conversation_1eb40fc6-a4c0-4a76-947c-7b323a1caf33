/**
 * 数据库视图帮助文档组件
 */

// React is used in JSX

export function DatabaseViewHelp() {

  return (
    <div className="notion-wp-help-section">
      <div className="help-header">
        <h2>🗄️ 数据库视图使用指南</h2>
        <p className="description">
          了解如何使用数据库视图功能查看和管理Notion数据库中的记录。
        </p>
      </div>

      <div className="help-content">
        {/* 快速开始 */}
        <div className="help-section">
          <h3>🚀 快速开始</h3>
          <ol>
            <li>
              <strong>配置连接</strong>
              <p>在 <strong>🔄 同步设置</strong> 标签页中配置Notion API密钥和数据库ID。</p>
            </li>
            <li>
              <strong>测试连接</strong>
              <p>点击"测试连接"按钮确保配置正确。</p>
            </li>
            <li>
              <strong>访问数据库视图</strong>
              <p>切换到 <strong>🗄️ 数据库视图</strong> 标签页开始使用。</p>
            </li>
          </ol>
        </div>

        {/* 视图类型 */}
        <div className="help-section">
          <h3>👁️ 视图类型</h3>
          
          <div className="view-type-item">
            <h4>📊 表格视图</h4>
            <p>以表格形式显示所有记录，适合查看详细数据。</p>
            <ul>
              <li>支持列排序</li>
              <li>支持搜索过滤</li>
              <li>支持分页加载</li>
              <li>适合数据分析</li>
            </ul>
          </div>

          <div className="view-type-item">
            <h4>🖼️ 画廊视图</h4>
            <p>以卡片形式展示记录，适合包含图片的内容。</p>
            <ul>
              <li>支持图片懒加载</li>
              <li>响应式网格布局</li>
              <li>卡片悬停效果</li>
              <li>适合内容展示</li>
            </ul>
          </div>

          <div className="view-type-item">
            <h4>📋 看板视图</h4>
            <p>按状态分组显示，适合项目管理和任务跟踪。</p>
            <ul>
              <li>自动状态分组</li>
              <li>拖拽式交互（即将推出）</li>
              <li>状态统计</li>
              <li>适合项目管理</li>
            </ul>
          </div>
        </div>

        {/* 功能特性 */}
        <div className="help-section">
          <h3>⚡ 功能特性</h3>
          
          <div className="feature-grid">
            <div className="feature-item">
              <h4>🔍 搜索功能</h4>
              <p>在工具栏中输入关键词快速搜索记录。支持标题、内容等字段的模糊搜索。</p>
            </div>

            <div className="feature-item">
              <h4>🔄 实时刷新</h4>
              <p>点击刷新按钮获取最新数据。数据会自动缓存以提升性能。</p>
            </div>

            <div className="feature-item">
              <h4>📱 响应式设计</h4>
              <p>完美适配桌面、平板和手机设备，随时随地查看数据。</p>
            </div>

            <div className="feature-item">
              <h4>⚡ 性能优化</h4>
              <p>支持分页加载、虚拟滚动等技术，确保大数据量下的流畅体验。</p>
            </div>
          </div>
        </div>

        {/* 常见问题 */}
        <div className="help-section">
          <h3>❓ 常见问题</h3>
          
          <div className="faq-item">
            <h4>Q: 为什么看不到数据库视图标签页？</h4>
            <p>A: 请确保您具有管理员权限。数据库视图功能需要 <code>manage_options</code> 权限。</p>
          </div>

          <div className="faq-item">
            <h4>Q: 为什么显示"需要配置数据库连接"？</h4>
            <p>A: 请先在同步设置中配置Notion API密钥和数据库ID，并测试连接成功。</p>
          </div>

          <div className="faq-item">
            <h4>Q: 数据加载很慢怎么办？</h4>
            <p>A: 可以在性能配置中调整页面大小和并发请求数。大数据库建议使用较小的页面大小。</p>
          </div>

          <div className="faq-item">
            <h4>Q: 如何查看特定类型的记录？</h4>
            <p>A: 使用搜索功能输入关键词，或者在看板视图中查看按状态分组的记录。</p>
          </div>

          <div className="faq-item">
            <h4>Q: 支持编辑记录吗？</h4>
            <p>A: 当前版本主要用于查看记录。编辑功能将在后续版本中推出。</p>
          </div>
        </div>

        {/* 技术说明 */}
        <div className="help-section">
          <h3>🔧 技术说明</h3>
          
          <div className="tech-note">
            <h4>数据同步</h4>
            <p>数据库视图显示的是Notion数据库的实时数据，通过Notion API获取。数据会在本地缓存以提升性能。</p>
          </div>

          <div className="tech-note">
            <h4>权限要求</h4>
            <p>使用数据库视图功能需要WordPress的 <code>manage_options</code> 权限，通常只有管理员具有此权限。</p>
          </div>

          <div className="tech-note">
            <h4>性能优化</h4>
            <p>系统采用了多种性能优化技术：</p>
            <ul>
              <li>数据分页加载</li>
              <li>图片懒加载</li>
              <li>虚拟滚动（大数据量）</li>
              <li>智能缓存机制</li>
            </ul>
          </div>
        </div>

        {/* 支持信息 */}
        <div className="help-section">
          <h3>💬 获取支持</h3>
          <p>如果您在使用过程中遇到问题，可以：</p>
          <ul>
            <li>查看 <strong>🐞 调试工具</strong> 标签页中的错误日志</li>
            <li>检查 <strong>📊 性能监控</strong> 中的系统状态</li>
            <li>联系插件开发者获取技术支持</li>
            <li>查看插件官方文档</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
