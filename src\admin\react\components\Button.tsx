/**
 * 按钮组件
 */

// React is automatically imported with new JSX transform
import type { ButtonProps } from '../types';

export function Button({
  children,
  className = '',
  variant = 'secondary',
  size = 'medium',
  disabled = false,
  loading = false,
  onClick,
  type = 'button',
  ...props
}: ButtonProps) {
  const baseClasses = 'button notion-wp-button';
  
  const variantClasses = {
    primary: 'button-primary',
    secondary: 'button-secondary',
    danger: 'button-danger',
  };

  const sizeClasses = {
    small: 'button-small',
    medium: '',
    large: 'button-large',
  };

  const classes = [
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    disabled && 'disabled',
    loading && 'loading',
    className,
  ].filter(Boolean).join(' ');

  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  };

  return (
    <button
      type={type}
      className={classes}
      disabled={disabled || loading}
      onClick={handleClick}
      {...props}
    >
      {loading && (
        <span className="spinner is-active" style={{ float: 'none', marginRight: '8px' }} />
      )}
      {children}
    </button>
  );
}

// 同步按钮组件
export function SyncButton({
  syncType,
  isRunning = false,
  onSync,
  children,
  ...props
}: {
  syncType: 'manual' | 'full';
  isRunning?: boolean;
  onSync?: (type: 'manual' | 'full') => void;
  children: React.ReactNode;
} & Omit<ButtonProps, 'onClick' | 'loading'>) {
  const handleClick = () => {
    if (onSync && !isRunning) {
      onSync(syncType);
    }
  };

  return (
    <Button
      {...props}
      variant={syncType === 'manual' ? 'primary' : 'secondary'}
      loading={isRunning}
      onClick={handleClick}
      className={`notion-wp-sync-btn ${props.className || ''}`}
      data-sync-type={syncType}
    >
      {syncType === 'manual' && (
        <span className="dashicons dashicons-lightbulb" style={{ marginRight: '4px' }} />
      )}
      {syncType === 'full' && (
        <span className="dashicons dashicons-update" style={{ marginRight: '4px' }} />
      )}
      <span className="button-text">{children}</span>
    </Button>
  );
}

// 图标按钮组件
export function IconButton({
  icon,
  title,
  children,
  ...props
}: {
  icon: string;
  title?: string;
} & ButtonProps) {
  return (
    <Button {...props} title={title} className={`icon-button ${props.className || ''}`}>
      <span className={`dashicons dashicons-${icon}`} />
      {children}
    </Button>
  );
}

// 复制按钮组件
export function CopyButton({
  text,
  onCopy,
  ...props
}: {
  text: string;
  onCopy?: (success: boolean) => void;
} & Omit<ButtonProps, 'onClick'>) {
  const handleCopy = async () => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        textArea.remove();
      }
      
      onCopy?.(true);
    } catch (error) {
      console.error('Failed to copy text:', error);
      onCopy?.(false);
    }
  };

  return (
    <IconButton
      {...props}
      icon="clipboard"
      title="复制到剪贴板"
      onClick={handleCopy}
      className={`copy-button ${props.className || ''}`}
    />
  );
}
