/**
 * 前端功能综合Hook
 * 
 * 整合所有前端功能的Hook，提供统一的接口
 */

import { useCallback, useEffect, useState } from 'react';
import { useLazyLoader, type UseLazyLoaderOptions } from './useLazyLoader';
import { useAnchorNavigation, type UseAnchorNavigationOptions } from './useAnchorNavigation';
import { useProgressiveLoader, type UseProgressiveLoaderOptions } from './useProgressiveLoader';

// 综合配置选项
export interface UseFrontendFeaturesOptions {
  lazyLoader?: UseLazyLoaderOptions;
  anchorNavigation?: UseAnchorNavigationOptions;
  progressiveLoader?: UseProgressiveLoaderOptions;
  enableAll?: boolean;
}

// 功能状态接口
export interface FrontendFeaturesState {
  lazyLoader: {
    enabled: boolean;
    supported: boolean;
    stats: any;
  };
  anchorNavigation: {
    enabled: boolean;
    supported: boolean;
    currentAnchor: string | null;
  };
  progressiveLoader: {
    enabled: boolean;
    supported: boolean;
    loading: boolean;
    error: string | null;
  };
}

// Hook返回值接口
export interface UseFrontendFeaturesReturn {
  state: FrontendFeaturesState;
  lazyLoader: ReturnType<typeof useLazyLoader>;
  anchorNavigation: ReturnType<typeof useAnchorNavigation>;
  progressiveLoader: ReturnType<typeof useProgressiveLoader>;
  initializeAll: () => void;
  destroyAll: () => void;
}

/**
 * 前端功能综合Hook
 */
export function useFrontendFeatures(options: UseFrontendFeaturesOptions = {}): UseFrontendFeaturesReturn {
  const {
    lazyLoader: lazyLoaderOptions = {},
    anchorNavigation: anchorNavigationOptions = {},
    progressiveLoader: progressiveLoaderOptions = {},
    enableAll = true,
  } = options;

  // 初始化各个功能Hook
  const lazyLoader = useLazyLoader({
    enabled: enableAll,
    autoInit: false, // 手动控制初始化
    ...lazyLoaderOptions,
  });

  const anchorNavigation = useAnchorNavigation({
    enabled: enableAll,
    autoInit: false, // 手动控制初始化
    ...anchorNavigationOptions,
  });

  const progressiveLoader = useProgressiveLoader({
    enabled: enableAll,
    autoInit: false, // 手动控制初始化
    ...progressiveLoaderOptions,
  });

  // 综合状态
  const [state, setState] = useState<FrontendFeaturesState>({
    lazyLoader: {
      enabled: enableAll && (lazyLoaderOptions.enabled !== false),
      supported: false,
      stats: null,
    },
    anchorNavigation: {
      enabled: enableAll && (anchorNavigationOptions.enabled !== false),
      supported: false,
      currentAnchor: null,
    },
    progressiveLoader: {
      enabled: enableAll && (progressiveLoaderOptions.enabled !== false),
      supported: true, // 渐进式加载总是支持的
      loading: false,
      error: null,
    },
  });

  // 更新状态
  useEffect(() => {
    setState(prev => ({
      ...prev,
      lazyLoader: {
        ...prev.lazyLoader,
        supported: lazyLoader.isSupported,
        stats: lazyLoader.stats,
      },
    }));
  }, [lazyLoader.isSupported, lazyLoader.stats]);

  useEffect(() => {
    setState(prev => ({
      ...prev,
      anchorNavigation: {
        ...prev.anchorNavigation,
        supported: anchorNavigation.isSupported,
        currentAnchor: anchorNavigation.getCurrentAnchor(),
      },
    }));
  }, [anchorNavigation.isSupported]);

  useEffect(() => {
    setState(prev => ({
      ...prev,
      progressiveLoader: {
        ...prev.progressiveLoader,
        loading: progressiveLoader.state.isLoading,
        error: progressiveLoader.state.error,
      },
    }));
  }, [progressiveLoader.state.isLoading, progressiveLoader.state.error]);

  // 初始化所有功能
  const initializeAll = useCallback(() => {
    console.log('🚀 [useFrontendFeatures] 初始化所有前端功能');

    // 这里可以添加初始化逻辑
    // 由于各个Hook已经有自己的初始化逻辑，这里主要是协调
    
    setState(prev => ({
      lazyLoader: {
        ...prev.lazyLoader,
        enabled: true,
      },
      anchorNavigation: {
        ...prev.anchorNavigation,
        enabled: true,
      },
      progressiveLoader: {
        ...prev.progressiveLoader,
        enabled: true,
      },
    }));
  }, []);

  // 销毁所有功能
  const destroyAll = useCallback(() => {
    console.log('🗑️ [useFrontendFeatures] 销毁所有前端功能');

    // 销毁懒加载器
    if (state.lazyLoader.enabled) {
      lazyLoader.destroy();
    }

    // 重置状态
    setState(prev => ({
      lazyLoader: {
        ...prev.lazyLoader,
        enabled: false,
        stats: null,
      },
      anchorNavigation: {
        ...prev.anchorNavigation,
        enabled: false,
        currentAnchor: null,
      },
      progressiveLoader: {
        ...prev.progressiveLoader,
        enabled: false,
        loading: false,
        error: null,
      },
    }));
  }, [state.lazyLoader.enabled, lazyLoader]);

  // 自动初始化
  useEffect(() => {
    if (enableAll) {
      initializeAll();
    }
  }, [enableAll, initializeAll]);

  return {
    state,
    lazyLoader,
    anchorNavigation,
    progressiveLoader,
    initializeAll,
    destroyAll,
  };
}

/**
 * 数据库视图前端功能Hook
 * 
 * 专门为数据库视图组件优化的前端功能Hook
 */
export function useDatabaseViewFeatures(options: UseFrontendFeaturesOptions = {}) {
  const frontendFeatures = useFrontendFeatures({
    lazyLoader: {
      threshold: 0.1,
      rootMargin: '50px 0px',
      ...options.lazyLoader,
    },
    anchorNavigation: {
      smoothScroll: true,
      offset: 80, // 考虑工具栏高度
      ...options.anchorNavigation,
    },
    progressiveLoader: {
      batchSize: 20,
      loadingDelay: 300,
      ...options.progressiveLoader,
    },
    ...options,
  });

  // 数据库视图特定的功能
  const enhanceTableView = useCallback((tableElement: HTMLElement) => {
    // 为表格行添加懒加载
    const rows = tableElement.querySelectorAll('tr[data-record-id]');
    rows.forEach(row => {
      // 表格行懒加载处理
      console.log('处理表格行:', row);
    });

    // 为表格添加锚点导航
    const headers = tableElement.querySelectorAll('th[id]');
    headers.forEach(header => {
      if (header.id) {
        // 可以添加锚点导航功能
      }
    });
  }, [frontendFeatures.lazyLoader]);

  const enhanceGalleryView = useCallback((galleryElement: HTMLElement) => {
    // 为画廊图片添加懒加载
    const images = galleryElement.querySelectorAll('img[data-src], img[loading="lazy"]');
    images.forEach(img => {
      frontendFeatures.lazyLoader.loadImage(img as HTMLImageElement);
    });

    // 为画廊卡片添加渐进式加载
    const cards = galleryElement.querySelectorAll('.notion-gallery-card');
    cards.forEach(card => {
      // 画廊卡片处理
      console.log('处理画廊卡片:', card);
    });
  }, [frontendFeatures.lazyLoader]);

  const enhanceBoardView = useCallback((boardElement: HTMLElement) => {
    // 为看板列添加锚点导航
    const columns = boardElement.querySelectorAll('.notion-board-column');
    columns.forEach((column, index) => {
      const columnId = `board-column-${index}`;
      column.id = columnId;
    });

    // 为看板卡片添加懒加载
    const cards = boardElement.querySelectorAll('.notion-board-card');
    cards.forEach(card => {
      // 看板卡片处理
      console.log('处理看板卡片:', card);
    });
  }, [frontendFeatures.lazyLoader]);

  return {
    ...frontendFeatures,
    enhanceTableView,
    enhanceGalleryView,
    enhanceBoardView,
  };
}

/**
 * 管理界面前端功能Hook
 * 
 * 专门为WordPress管理界面优化的前端功能Hook
 */
export function useAdminFeatures(options: UseFrontendFeaturesOptions = {}) {
  return useFrontendFeatures({
    lazyLoader: {
      threshold: 0.2,
      rootMargin: '100px 0px',
      ...options.lazyLoader,
    },
    anchorNavigation: {
      smoothScroll: true,
      offset: 32, // WordPress管理栏高度
      ...options.anchorNavigation,
    },
    progressiveLoader: {
      batchSize: 10,
      loadingDelay: 500,
      ...options.progressiveLoader,
    },
    ...options,
  });
}
