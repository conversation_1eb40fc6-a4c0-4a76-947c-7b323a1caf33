/**
 * 其他设置标签页组件
 */

import { useSettings, useAppActions } from '../context/AppContext';
import { useWordPressAjax, useI18n } from '../hooks/useWordPress';
import { Input, Select, Checkbox, FormRow } from './Input';
import { Button } from './Button';

export function OtherSettingsTab() {
  const settings = useSettings();
  const { updateSettings, showToast } = useAppActions();
  const { request } = useWordPressAjax();
  const { __ } = useI18n();

  // 处理设置更新
  const handleSettingChange = (key: string, value: string | boolean | number) => {
    updateSettings({ [key]: value });
  };

  // 清理缓存
  const handleClearCache = async () => {
    try {
      const response = await request('notion_to_wordpress_clear_cache');
      
      if (response.success) {
        showToast({
          type: 'success',
          message: '缓存已清理',
        });
      } else {
        showToast({
          type: 'error',
          message: response.message || '清理缓存失败',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: '清理缓存失败：' + (error instanceof Error ? error.message : '未知错误'),
      });
    }
  };

  // 重置所有设置
  const handleResetAllSettings = async () => {
    if (!confirm(__('确定要重置所有设置吗？此操作不可撤销。', '确定要重置所有设置吗？此操作不可撤销。'))) {
      return;
    }

    try {
      const response = await request('notion_to_wordpress_reset_settings');
      
      if (response.success) {
        showToast({
          type: 'success',
          message: '所有设置已重置',
        });
        // 重新加载页面以应用默认设置
        window.location.reload();
      } else {
        showToast({
          type: 'error',
          message: response.message || '重置设置失败',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: '重置设置失败：' + (error instanceof Error ? error.message : '未知错误'),
      });
    }
  };

  // 日志级别选项
  const logLevelOptions = [
    { value: 'none', label: __('关闭日志', '关闭日志') },
    { value: 'error', label: __('仅错误', '仅错误') },
    { value: 'warning', label: __('警告及以上', '警告及以上') },
    { value: 'info', label: __('信息及以上', '信息及以上') },
    { value: 'debug', label: __('调试模式', '调试模式') },
  ];

  // 时区选项
  const timezoneOptions = [
    { value: 'UTC', label: 'UTC' },
    { value: 'Asia/Shanghai', label: '北京时间 (UTC+8)' },
    { value: 'America/New_York', label: '纽约时间 (UTC-5/-4)' },
    { value: 'Europe/London', label: '伦敦时间 (UTC+0/+1)' },
    { value: 'Asia/Tokyo', label: '东京时间 (UTC+9)' },
  ];

  return (
    <div className="notion-wp-settings-section">
      <h2>{__('⚙️ 其他设置', '⚙️ 其他设置')}</h2>
      <p className="description">
        {__('配置插件的其他功能选项和系统设置。', '配置插件的其他功能选项和系统设置。')}
      </p>

      {/* 日志和调试 */}
      <div className="notion-wp-settings-section">
        <h3>{__('📝 日志和调试', '📝 日志和调试')}</h3>
        
        <table className="form-table">
          <tbody>
            <FormRow
              label={__('日志级别', '日志级别')}
              description={__('选择要记录的日志级别。调试模式会记录详细信息，但可能影响性能。', '选择要记录的日志级别。调试模式会记录详细信息，但可能影响性能。')}
            >
              <Select
                value={settings.log_level || 'info'}
                options={logLevelOptions}
                onChange={(value) => handleSettingChange('log_level', value)}
              />
            </FormRow>

            <FormRow
              label={__('启用调试模式', '启用调试模式')}
              description={__('启用详细的调试信息输出，用于问题诊断。', '启用详细的调试信息输出，用于问题诊断。')}
            >
              <Checkbox
                checked={settings.debug_mode || false}
                onChange={(checked) => handleSettingChange('debug_mode', checked)}
              />
            </FormRow>

            <FormRow
              label={__('保留日志天数', '保留日志天数')}
              description={__('自动清理多少天前的日志文件。设为0表示不自动清理。', '自动清理多少天前的日志文件。设为0表示不自动清理。')}
            >
              <Input
                type="number"
                value={String(settings.log_retention_days || 30)}
                onChange={(value) => handleSettingChange('log_retention_days', parseInt(value) || 30)}
              />
            </FormRow>
          </tbody>
        </table>
      </div>

      {/* 内容处理 */}
      <div className="notion-wp-settings-section">
        <h3>{__('📄 内容处理', '📄 内容处理')}</h3>
        
        <table className="form-table">
          <tbody>
            <FormRow
              label={__('默认文章状态', '默认文章状态')}
              description={__('当Notion页面没有指定状态时使用的默认WordPress文章状态。', '当Notion页面没有指定状态时使用的默认WordPress文章状态。')}
            >
              <Select
                value={settings.default_post_status || 'draft'}
                options={[
                  { value: 'draft', label: __('草稿', '草稿') },
                  { value: 'publish', label: __('发布', '发布') },
                  { value: 'private', label: __('私密', '私密') },
                  { value: 'pending', label: __('待审核', '待审核') },
                ]}
                onChange={(value) => handleSettingChange('default_post_status', value)}
              />
            </FormRow>

            <FormRow
              label={__('默认文章类型', '默认文章类型')}
              description={__('当Notion页面没有指定类型时使用的默认WordPress文章类型。', '当Notion页面没有指定类型时使用的默认WordPress文章类型。')}
            >
              <Select
                value={settings.default_post_type || 'post'}
                options={[
                  { value: 'post', label: __('文章', '文章') },
                  { value: 'page', label: __('页面', '页面') },
                ]}
                onChange={(value) => handleSettingChange('default_post_type', value)}
              />
            </FormRow>

            <FormRow
              label={__('时区设置', '时区设置')}
              description={__('用于处理日期和时间的时区。建议与WordPress设置保持一致。', '用于处理日期和时间的时区。建议与WordPress设置保持一致。')}
            >
              <Select
                value={settings.timezone || 'UTC'}
                options={timezoneOptions}
                onChange={(value) => handleSettingChange('timezone', value)}
              />
            </FormRow>
          </tbody>
        </table>
      </div>

      {/* 安全和隐私 */}
      <div className="notion-wp-settings-section">
        <h3>{__('🔒 安全和隐私', '🔒 安全和隐私')}</h3>
        
        <table className="form-table">
          <tbody>
            <FormRow
              label={__('API密钥加密存储', 'API密钥加密存储')}
              description={__('使用WordPress内置加密功能保护API密钥。', '使用WordPress内置加密功能保护API密钥。')}
            >
              <Checkbox
                checked={settings.encrypt_api_key !== false}
                onChange={(checked) => handleSettingChange('encrypt_api_key', checked)}
              />
            </FormRow>

            <FormRow
              label={__('限制管理员访问', '限制管理员访问')}
              description={__('只允许超级管理员访问插件设置。', '只允许超级管理员访问插件设置。')}
            >
              <Checkbox
                checked={settings.admin_only_access || false}
                onChange={(checked) => handleSettingChange('admin_only_access', checked)}
              />
            </FormRow>

            <FormRow
              label={__('匿名统计', '匿名统计')}
              description={__('允许发送匿名使用统计以帮助改进插件。不会收集敏感信息。', '允许发送匿名使用统计以帮助改进插件。不会收集敏感信息。')}
            >
              <Checkbox
                checked={settings.anonymous_stats !== false}
                onChange={(checked) => handleSettingChange('anonymous_stats', checked)}
              />
            </FormRow>
          </tbody>
        </table>
      </div>

      {/* 卸载选项 */}
      <div className="notion-wp-settings-section">
        <h3>{__('🗑️ 卸载选项', '🗑️ 卸载选项')}</h3>
        
        <table className="form-table">
          <tbody>
            <FormRow
              label={__('卸载时删除数据', '卸载时删除数据')}
              description={__('卸载插件时删除所有设置和数据。注意：此操作不可撤销！', '卸载插件时删除所有设置和数据。注意：此操作不可撤销！')}
            >
              <Checkbox
                checked={settings.delete_on_uninstall || false}
                onChange={(checked) => handleSettingChange('delete_on_uninstall', checked)}
              />
            </FormRow>

            <FormRow
              label={__('保留同步的文章', '保留同步的文章')}
              description={__('卸载时保留已同步的WordPress文章，只删除插件数据。', '卸载时保留已同步的WordPress文章，只删除插件数据。')}
            >
              <Checkbox
                checked={settings.keep_synced_posts !== false}
                onChange={(checked) => handleSettingChange('keep_synced_posts', checked)}
              />
            </FormRow>
          </tbody>
        </table>
      </div>

      {/* 系统操作 */}
      <div className="notion-wp-settings-section">
        <h3>{__('🔧 系统操作', '🔧 系统操作')}</h3>
        
        <div className="system-actions">
          <div className="action-group">
            <h4>{__('缓存管理', '缓存管理')}</h4>
            <p>{__('清理所有缓存数据，强制重新获取最新内容。', '清理所有缓存数据，强制重新获取最新内容。')}</p>
            <Button
              variant="secondary"
              onClick={handleClearCache}
            >
              <span className="dashicons dashicons-trash" style={{ marginRight: '4px' }} />
              {__('清理缓存', '清理缓存')}
            </Button>
          </div>

          <div className="action-group danger-zone">
            <h4>{__('⚠️ 危险操作', '⚠️ 危险操作')}</h4>
            <p>{__('以下操作将重置所有设置，请谨慎操作！', '以下操作将重置所有设置，请谨慎操作！')}</p>
            <Button
              variant="danger"
              onClick={handleResetAllSettings}
            >
              <span className="dashicons dashicons-warning" style={{ marginRight: '4px' }} />
              {__('重置所有设置', '重置所有设置')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
