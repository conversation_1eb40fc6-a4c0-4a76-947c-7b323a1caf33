/**
 * 字段映射标签页组件
 */

import { useState } from 'react';
import { useSettings, useAppActions } from '../context/AppContext';
import { useI18n } from '../hooks/useWordPress';
import { Input, FormRow, Checkbox } from './Input';
import { Button } from './Button';

export function FieldMappingTab() {
  const settings = useSettings();
  const { updateSettings, showToast } = useAppActions();
  const { __ } = useI18n();

  const [customMappings, setCustomMappings] = useState(settings.field_mapping);

  // 处理字段映射更新
  const handleMappingChange = (field: string, value: string) => {
    const newMappings = { ...customMappings, [field]: value };
    setCustomMappings(newMappings);
    updateSettings({ field_mapping: newMappings });
  };

  // 重置为默认映射
  const handleResetToDefaults = () => {
    const defaultMappings = {
      title: 'Title,标题',
      status: 'Status,状态',
      post_type: 'Type,类型',
      date: 'Date,日期',
      excerpt: 'Summary,摘要,Excerpt',
      featured_image: 'Featured Image,特色图片',
      categories: 'Categories,分类,Category',
      tags: 'Tags,标签,Tag',
      password: 'Password,密码',
    };
    
    setCustomMappings(defaultMappings);
    updateSettings({ field_mapping: defaultMappings });
    
    showToast({
      type: 'success',
      message: '已重置为默认字段映射',
    });
  };

  // 字段映射配置
  const fieldMappingConfig = [
    {
      key: 'title',
      label: __('文章标题', '文章标题'),
      description: __('Notion页面标题对应的WordPress文章标题', 'Notion页面标题对应的WordPress文章标题'),
      required: true,
    },
    {
      key: 'status',
      label: __('发布状态', '发布状态'),
      description: __('控制文章发布状态的字段（如：Published, Draft）', '控制文章发布状态的字段（如：Published, Draft）'),
      required: false,
    },
    {
      key: 'post_type',
      label: __('文章类型', '文章类型'),
      description: __('指定WordPress文章类型的字段（如：post, page）', '指定WordPress文章类型的字段（如：post, page）'),
      required: false,
    },
    {
      key: 'date',
      label: __('发布日期', '发布日期'),
      description: __('文章发布日期字段', '文章发布日期字段'),
      required: false,
    },
    {
      key: 'excerpt',
      label: __('文章摘要', '文章摘要'),
      description: __('文章摘要或简介字段', '文章摘要或简介字段'),
      required: false,
    },
    {
      key: 'featured_image',
      label: __('特色图片', '特色图片'),
      description: __('文章特色图片URL字段', '文章特色图片URL字段'),
      required: false,
    },
    {
      key: 'categories',
      label: __('文章分类', '文章分类'),
      description: __('文章分类字段，支持多个分类', '文章分类字段，支持多个分类'),
      required: false,
    },
    {
      key: 'tags',
      label: __('文章标签', '文章标签'),
      description: __('文章标签字段，支持多个标签', '文章标签字段，支持多个标签'),
      required: false,
    },
    {
      key: 'password',
      label: __('文章密码', '文章密码'),
      description: __('设置文章访问密码的字段', '设置文章访问密码的字段'),
      required: false,
    },
  ];

  return (
    <div className="notion-wp-settings-section">
      <h2>{__('字段映射', '字段映射')}</h2>
      <p className="description">
        {__('设置您的Notion数据库属性名称与WordPress字段的对应关系。多个备选名称请用英文逗号隔开。', '设置您的Notion数据库属性名称与WordPress字段的对应关系。多个备选名称请用英文逗号隔开。')}
      </p>

      <table className="form-table">
        <tbody>
          {fieldMappingConfig.map(field => (
            <FormRow
              key={field.key}
              label={field.label}
              required={field.required}
              description={field.description}
            >
              <Input
                value={customMappings[field.key] || ''}
                placeholder={__(`输入${field.label}字段名`, `输入${field.label}字段名`)}
                onChange={(value) => handleMappingChange(field.key, value)}
                required={field.required}
              />
            </FormRow>
          ))}
        </tbody>
      </table>

      {/* 高级映射选项 */}
      <div className="notion-wp-settings-section">
        <h3>{__('🔧 高级映射选项', '🔧 高级映射选项')}</h3>
        
        <table className="form-table">
          <tbody>
            <FormRow
              label={__('自动创建分类', '自动创建分类')}
              description={__('当Notion中的分类在WordPress中不存在时，自动创建新分类', '当Notion中的分类在WordPress中不存在时，自动创建新分类')}
            >
              <Checkbox
                checked={settings.auto_create_categories || false}
                onChange={(checked) => updateSettings({ auto_create_categories: checked })}
              />
            </FormRow>

            <FormRow
              label={__('自动创建标签', '自动创建标签')}
              description={__('当Notion中的标签在WordPress中不存在时，自动创建新标签', '当Notion中的标签在WordPress中不存在时，自动创建新标签')}
            >
              <Checkbox
                checked={settings.auto_create_tags || false}
                onChange={(checked) => updateSettings({ auto_create_tags: checked })}
              />
            </FormRow>

            <FormRow
              label={__('保留HTML格式', '保留HTML格式')}
              description={__('在同步内容时保留Notion中的HTML格式', '在同步内容时保留Notion中的HTML格式')}
            >
              <Checkbox
                checked={settings.preserve_html || true}
                onChange={(checked) => updateSettings({ preserve_html: checked })}
              />
            </FormRow>

            <FormRow
              label={__('同步元数据', '同步元数据')}
              description={__('同步Notion页面的自定义属性作为WordPress自定义字段', '同步Notion页面的自定义属性作为WordPress自定义字段')}
            >
              <Checkbox
                checked={settings.sync_metadata || true}
                onChange={(checked) => updateSettings({ sync_metadata: checked })}
              />
            </FormRow>
          </tbody>
        </table>
      </div>

      {/* 操作按钮 */}
      <div className="notion-wp-button-row">
        <Button
          variant="secondary"
          onClick={handleResetToDefaults}
        >
          {__('重置为默认映射', '重置为默认映射')}
        </Button>
      </div>

      {/* 映射预览 */}
      <div className="notion-wp-settings-section">
        <h3>{__('📋 当前映射预览', '📋 当前映射预览')}</h3>
        <div className="field-mapping-preview">
          <table className="wp-list-table widefat fixed striped">
            <thead>
              <tr>
                <th>{__('WordPress字段', 'WordPress字段')}</th>
                <th>{__('Notion属性名', 'Notion属性名')}</th>
                <th>{__('状态', '状态')}</th>
              </tr>
            </thead>
            <tbody>
              {fieldMappingConfig.map(field => (
                <tr key={field.key}>
                  <td>
                    <strong>{field.label}</strong>
                    {field.required && <span className="required">*</span>}
                  </td>
                  <td>
                    <code>{customMappings[field.key] || __('未设置', '未设置')}</code>
                  </td>
                  <td>
                    {customMappings[field.key] ? (
                      <span className="status-enabled">{__('已配置', '已配置')}</span>
                    ) : field.required ? (
                      <span className="status-required">{__('必需配置', '必需配置')}</span>
                    ) : (
                      <span className="status-optional">{__('可选', '可选')}</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 帮助信息 */}
      <div className="notion-wp-settings-section">
        <h3>{__('💡 字段映射说明', '💡 字段映射说明')}</h3>
        <div className="help-content">
          <ul>
            <li>{__('字段名称不区分大小写', '字段名称不区分大小写')}</li>
            <li>{__('可以使用英文逗号分隔多个备选名称，系统会按顺序查找', '可以使用英文逗号分隔多个备选名称，系统会按顺序查找')}</li>
            <li>{__('标题字段是必需的，其他字段为可选', '标题字段是必需的，其他字段为可选')}</li>
            <li>{__('如果某个字段留空，该功能将被禁用', '如果某个字段留空，该功能将被禁用')}</li>
            <li>{__('建议在Notion数据库中使用英文属性名以获得最佳兼容性', '建议在Notion数据库中使用英文属性名以获得最佳兼容性')}</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
