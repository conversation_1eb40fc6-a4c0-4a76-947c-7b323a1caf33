/**
 * 前端功能Hook测试 - 简化版
 * 
 * 验证所有前端功能Hook的基本功能
 */

import { useRef } from 'react';
import { useLazyLoader, useLazyImage, useLazyContainer } from './useLazyLoader';
import { useAnchorNavigation, useAnchorLink, useScrollSpy } from './useAnchorNavigation';
import { useProgressiveLoader, useLoadMoreButton, useInfiniteScroll } from './useProgressiveLoader';
import { useFrontendFeatures, useDatabaseViewFeatures } from './useFrontendFeatures';

/**
 * 懒加载Hook测试
 */
export function TestUseLazyLoader() {
  const lazyLoader = useLazyLoader({
    threshold: 0.1,
    rootMargin: '50px 0px',
  });

  const { imgRef, loaded, loading, error } = useLazyImage('https://picsum.photos/400/300?random=1');

  const { containerRef, loadImages, preloadContainerImages } = useLazyContainer();

  return (
    <div style={{ padding: '20px', border: '1px solid #ddd', margin: '10px', borderRadius: '4px' }}>
      <h4>懒加载Hook测试</h4>
      
      <div style={{ marginBottom: '15px' }}>
        <h5>懒加载器状态</h5>
        <p><strong>支持:</strong> {lazyLoader.isSupported ? '是' : '否'}</p>
        <p><strong>统计:</strong> {lazyLoader.stats ? JSON.stringify(lazyLoader.stats) : '无'}</p>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <h5>单个图片懒加载</h5>
        <p><strong>状态:</strong> {loading ? '加载中' : loaded ? '已加载' : '未加载'}</p>
        {error && <p style={{ color: 'red' }}><strong>错误:</strong> {error}</p>}
        <img
          ref={imgRef}
          alt="懒加载测试图片"
          style={{ width: '200px', height: '150px', objectFit: 'cover', border: '1px solid #ddd' }}
        />
      </div>

      <div style={{ marginBottom: '15px' }}>
        <h5>容器懒加载</h5>
        <button onClick={loadImages} style={{ marginRight: '10px' }}>加载图片</button>
        <button onClick={preloadContainerImages}>预加载图片</button>
        <div ref={containerRef as any} style={{ marginTop: '10px', border: '1px solid #ddd', padding: '10px' }}>
          <img data-src="https://picsum.photos/150/100?random=2" alt="容器图片1" style={{ width: '150px', height: '100px', margin: '5px' }} />
          <img data-src="https://picsum.photos/150/100?random=3" alt="容器图片2" style={{ width: '150px', height: '100px', margin: '5px' }} />
        </div>
      </div>
    </div>
  );
}

/**
 * 锚点导航Hook测试
 */
export function TestUseAnchorNavigation() {
  const anchorNavigation = useAnchorNavigation({
    smoothScroll: true,
    offset: 80,
  });

  const { handleClick: handleLink1Click, isScrolling: isScrolling1 } = useAnchorLink('#section1');
  const { handleClick: handleLink2Click, isScrolling: isScrolling2 } = useAnchorLink('#section2');

  const { activeAnchor } = useScrollSpy(['section1', 'section2']);

  const handleScrollToSection = (sectionId: string) => {
    anchorNavigation.scrollToAnchor(sectionId);
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ddd', margin: '10px', borderRadius: '4px' }}>
      <h4>锚点导航Hook测试</h4>
      
      <div style={{ marginBottom: '15px' }}>
        <h5>导航状态</h5>
        <p><strong>当前活跃锚点:</strong> {activeAnchor || '无'}</p>
        <p><strong>链接1滚动中:</strong> {isScrolling1 ? '是' : '否'}</p>
        <p><strong>链接2滚动中:</strong> {isScrolling2 ? '是' : '否'}</p>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <h5>导航操作</h5>
        <button onClick={handleLink1Click} style={{ marginRight: '10px' }}>跳转到Section1</button>
        <button onClick={handleLink2Click} style={{ marginRight: '10px' }}>跳转到Section2</button>
        <button onClick={() => handleScrollToSection('section1')}>滚动到Section1</button>
      </div>

      <div style={{ height: '200px', overflow: 'auto', border: '1px solid #ddd', padding: '10px' }}>
        <div id="section1" style={{ height: '100px', backgroundColor: '#f0f0f0', marginBottom: '10px', padding: '10px' }}>
          <h6>Section 1</h6>
          <p>这是第一个区域的内容</p>
        </div>
        <div id="section2" style={{ height: '100px', backgroundColor: '#e0e0e0', padding: '10px' }}>
          <h6>Section 2</h6>
          <p>这是第二个区域的内容</p>
        </div>
      </div>
    </div>
  );
}

/**
 * 渐进式加载Hook测试
 */
export function TestUseProgressiveLoader() {
  const progressiveLoader = useProgressiveLoader({
    batchSize: 5,
    loadingDelay: 300,
  });

  const { buttonRef, handleClick, isLoading, disabled } = useLoadMoreButton();

  const { containerRef: infiniteContainerRef } = useInfiniteScroll();
  
  // 模拟数据
  const mockRecords = [
    { id: '1' }, { id: '2' }, { id: '3' }, { id: '4' }, { id: '5' }
  ];

  return (
    <div style={{ padding: '20px', border: '1px solid #ddd', margin: '10px', borderRadius: '4px' }}>
      <h4>渐进式加载Hook测试</h4>
      
      <div style={{ marginBottom: '15px' }}>
        <h5>加载器状态</h5>
        <p><strong>加载中:</strong> {progressiveLoader.state.isLoading ? '是' : '否'}</p>
        <p><strong>有更多:</strong> {progressiveLoader.state.hasMore ? '是' : '否'}</p>
        <p><strong>已加载:</strong> {progressiveLoader.state.totalLoaded}</p>
        {progressiveLoader.state.error && (
          <p style={{ color: 'red' }}><strong>错误:</strong> {progressiveLoader.state.error}</p>
        )}
      </div>

      <div style={{ marginBottom: '15px' }}>
        <h5>加载更多按钮测试</h5>
        <button 
          ref={buttonRef} 
          onClick={handleClick} 
          disabled={disabled}
          style={{ padding: '8px 16px', backgroundColor: disabled ? '#ccc' : '#007cba', color: 'white', border: 'none', borderRadius: '4px' }}
        >
          {isLoading ? '加载中...' : '加载更多'}
        </button>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <h5>无限滚动测试</h5>
        <div 
          ref={infiniteContainerRef as any}
          style={{ height: '150px', overflow: 'auto', border: '1px solid #ddd', padding: '10px' }}
        >
          {mockRecords.map((record, index) => (
            <div key={record.id || index} style={{ padding: '5px', borderBottom: '1px solid #eee' }}>
              记录 #{index + 1}: {record.id || `模拟记录-${index}`}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

/**
 * 综合功能Hook测试
 */
export function TestUseFrontendFeatures() {
  const frontendFeatures = useFrontendFeatures();
  const databaseFeatures = useDatabaseViewFeatures();

  const tableRef = useRef<HTMLDivElement>(null);
  const galleryRef = useRef<HTMLDivElement>(null);
  const boardRef = useRef<HTMLDivElement>(null);

  const handleEnhanceViews = () => {
    if (tableRef.current) {
      databaseFeatures.enhanceTableView(tableRef.current);
    }
    if (galleryRef.current) {
      databaseFeatures.enhanceGalleryView(galleryRef.current);
    }
    if (boardRef.current) {
      databaseFeatures.enhanceBoardView(boardRef.current);
    }
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ddd', margin: '10px', borderRadius: '4px' }}>
      <h4>综合功能Hook测试</h4>
      
      <div style={{ marginBottom: '15px' }}>
        <h5>功能状态</h5>
        <p><strong>前端功能状态:</strong> 已加载</p>
        <p><strong>数据库视图状态:</strong> 已加载</p>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <h5>操作按钮</h5>
        <button onClick={frontendFeatures.initializeAll} style={{ marginRight: '10px' }}>初始化所有功能</button>
        <button onClick={handleEnhanceViews} style={{ marginRight: '10px' }}>增强视图</button>
        <button onClick={frontendFeatures.destroyAll}>清理功能</button>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <h5>模拟数据库视图</h5>
        <div ref={tableRef} style={{ border: '1px solid #ddd', padding: '10px', marginBottom: '10px' }}>
          <strong>表格视图</strong> - 等待增强
        </div>
        <div ref={galleryRef} style={{ border: '1px solid #ddd', padding: '10px', marginBottom: '10px' }}>
          <strong>画廊视图</strong> - 等待增强
        </div>
        <div ref={boardRef} style={{ border: '1px solid #ddd', padding: '10px' }}>
          <strong>看板视图</strong> - 等待增强
        </div>
      </div>
    </div>
  );
}

/**
 * 主测试组件
 */
export function FrontendFeaturesTestSuite() {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>前端功能Hook测试套件</h2>
      <TestUseLazyLoader />
      <TestUseAnchorNavigation />
      <TestUseProgressiveLoader />
      <TestUseFrontendFeatures />
    </div>
  );
}
