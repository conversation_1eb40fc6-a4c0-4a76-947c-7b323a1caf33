module.exports = {
  root: true,

  // 解析器配置
  parser: '@typescript-eslint/parser',

  // 环境配置
  env: {
    browser: true,
    es6: true,
    node: true,
    jquery: true,
    jest: true
  },

  // 全局变量
  globals: {
    wp: 'readonly',
    jQuery: 'readonly',
    $: 'readonly',
    ajaxurl: 'readonly',
    notionToWp: 'readonly'
  },

  // 插件配置
  plugins: [
    '@typescript-eslint',
    'react',
    'react-hooks'
  ],

  // 扩展配置
  extends: [
    'eslint:recommended'
  ],

  // 解析器选项
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    },
    project: './tsconfig.json'
  },

  // React设置
  settings: {
    react: {
      version: 'detect'
    }
  },

  // 规则配置
  rules: {
    // 基本规则
    'no-unused-vars': 'off', // 使用TypeScript版本
    '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'prefer-const': 'warn',
    'no-var': 'error',
    'object-shorthand': 'warn',
    'prefer-arrow-callback': 'warn',
    'no-case-declarations': 'warn',
    'no-prototype-builtins': 'warn',

    // TypeScript规则
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-non-null-assertion': 'warn',

    // React规则
    'react/react-in-jsx-scope': 'off', // React 17+ 不需要导入React
    'react/prop-types': 'off', // 使用TypeScript类型检查
    'react/display-name': 'warn',
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',

    // WordPress特定规则
    'no-undef': 'off', // WordPress全局变量较多
    'camelcase': 'off'  // WordPress使用下划线命名
  },
  
  // 忽略模式
  ignorePatterns: [
    'node_modules/',
    'assets/dist/',
    'build/',
    'vendor/',
    '*.min.js'
  ],
  
  // 覆盖配置
  overrides: [
    {
      files: ['**/*.test.js', '**/*.spec.js', '**/*.test.ts', '**/*.spec.ts', '**/*.test.tsx', '**/*.spec.tsx'],
      env: {
        jest: true
      },
      rules: {
        'no-unused-vars': 'off',
        '@typescript-eslint/no-unused-vars': 'off',
        '@typescript-eslint/no-explicit-any': 'off'
      }
    },
    {
      files: ['**/*.tsx', '**/*.jsx'],
      rules: {
        'react/jsx-uses-react': 'off',
        'react/react-in-jsx-scope': 'off'
      }
    }
  ]
};
