/**
 * Webhook设置组件
 */

import { useState } from 'react';
import { useSettings, useAppActions } from '../context/AppContext';
import { useWordPressAjax, useI18n, useClipboard } from '../hooks/useWordPress';
import { Input, Checkbox, FormRow } from './Input';
import { IconButton, CopyButton } from './Button';

export function WebhookSettings() {
  const settings = useSettings();
  const { updateSettings, showToast } = useAppActions();
  const { request } = useWordPressAjax();
  const { __ } = useI18n();
  const { copy } = useClipboard();

  const [isRefreshingToken, setIsRefreshingToken] = useState(false);

  // 处理Webhook启用/禁用
  const handleWebhookToggle = (enabled: boolean) => {
    updateSettings({ webhook_enabled: enabled });
  };

  // 刷新验证令牌
  const handleRefreshToken = async () => {
    setIsRefreshingToken(true);
    try {
      const response = await request('notion_to_wordpress_refresh_webhook_token');
      
      if (response.success && response.data?.verification_token) {
        updateSettings({ webhook_verify_token: response.data.verification_token });
        showToast({
          type: 'success',
          message: '验证令牌已刷新',
        });
      } else {
        showToast({
          type: 'error',
          message: response.message || '刷新令牌失败',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: '刷新令牌失败：' + (error instanceof Error ? error.message : '未知错误'),
      });
    } finally {
      setIsRefreshingToken(false);
    }
  };

  // 复制到剪贴板
  const handleCopy = async (text: string, label: string) => {
    const success = await copy(text);
    showToast({
      type: success ? 'success' : 'error',
      message: success ? `${label}已复制到剪贴板` : `复制${label}失败`,
    });
  };

  // 生成Webhook URL
  const webhookUrl = settings.webhook_token 
    ? `${window.location.origin}/wp-json/notion-to-wordpress/v1/webhook/${settings.webhook_token}`
    : '';

  return (
    <div className="notion-wp-settings-section">
      <FormRow
        label={__('Webhook 支持', 'Webhook 支持')}
        description={__('启用后，您可以设置 Notion 集成的 Webhook 以在内容变更时自动触发同步。', '启用后，您可以设置 Notion 集成的 Webhook 以在内容变更时自动触发同步。')}
      >
        <Checkbox
          checked={settings.webhook_enabled}
          label={__('启用 Webhook 支持', '启用 Webhook 支持')}
          onChange={handleWebhookToggle}
        />
      </FormRow>

      {settings.webhook_enabled && (
        <div className="notion-wp-subsetting">
          <div className="notion-wp-field">
            <FormRow
              label={__('验证令牌', '验证令牌')}
              description={__('首次发送 Webhook 时，Notion 将返回 verification_token，此处会自动展示。点击刷新按钮可获取最新的令牌。', '首次发送 Webhook 时，Notion 将返回 verification_token，此处会自动展示。点击刷新按钮可获取最新的令牌。')}
            >
              <div className="input-with-button">
                <Input
                  value={settings.webhook_verify_token}
                  placeholder={__('等待 Notion 返回…', '等待 Notion 返回…')}
                  disabled
                />
                <IconButton
                  variant="secondary"
                  icon="update"
                  title={__('刷新验证令牌', '刷新验证令牌')}
                  loading={isRefreshingToken}
                  onClick={handleRefreshToken}
                />
                {settings.webhook_verify_token && (
                  <CopyButton
                    text={settings.webhook_verify_token}
                    onCopy={() => handleCopy(settings.webhook_verify_token, '验证令牌')}
                  />
                )}
              </div>
            </FormRow>
          </div>

          <div className="notion-wp-field">
            <FormRow
              label={__('Webhook 地址', 'Webhook 地址')}
              description={__('在 Notion 开发者平台设置此 URL 作为您集成的 Webhook 终端点。', '在 Notion 开发者平台设置此 URL 作为您集成的 Webhook 终端点。')}
            >
              <div className="input-with-button">
                <Input
                  value={webhookUrl}
                  disabled
                />
                {webhookUrl && (
                  <CopyButton
                    text={webhookUrl}
                    onCopy={() => handleCopy(webhookUrl, 'Webhook URL')}
                  />
                )}
              </div>
            </FormRow>
          </div>

          <div className="notion-wp-field">
            <FormRow label={__('Webhook 同步选项', 'Webhook 同步选项')}>
              <div className="checkbox-group">
                <Checkbox
                  checked={settings.webhook_incremental_sync}
                  label={__('启用增量同步', '启用增量同步')}
                  description={__('Webhook触发时仅同步有变化的页面，提高响应速度', 'Webhook触发时仅同步有变化的页面，提高响应速度')}
                  onChange={(checked) => updateSettings({ webhook_incremental_sync: checked })}
                />
                
                <Checkbox
                  checked={settings.webhook_check_deletions}
                  label={__('数据库事件检查删除', '数据库事件检查删除')}
                  description={__('数据库结构变化时检查删除的页面（单页面事件不受影响）', '数据库结构变化时检查删除的页面（单页面事件不受影响）')}
                  onChange={(checked) => updateSettings({ webhook_check_deletions: checked })}
                />
              </div>
            </FormRow>
          </div>
        </div>
      )}
    </div>
  );
}
