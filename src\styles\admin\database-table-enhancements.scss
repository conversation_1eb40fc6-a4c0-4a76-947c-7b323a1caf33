/**
 * 数据库表格视图增强样式
 * 
 * 为React表格组件提供额外的交互和视觉增强
 */

// 表格头部增强
.notion-database-view-table {
  .notion-database-table {
    .notion-table-header {
      .notion-table-header-cell {
        position: relative;
        cursor: pointer;
        user-select: none;
        transition: var(--database-transition);

        &:hover {
          background: rgba(0, 115, 170, 0.1);
        }

        &.sorted {
          background: rgba(0, 115, 170, 0.15);
          color: var(--database-primary-color);
        }

        .header-text {
          display: inline-block;
          margin-right: 8px;
        }

        .sort-indicator {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 12px;
          color: var(--database-primary-color);
          font-weight: bold;
        }

        // 键盘导航支持
        &:focus {
          outline: 2px solid var(--database-primary-color);
          outline-offset: -2px;
        }
      }
    }

    .notion-table-body {
      .notion-table-row {
        &.selected {
          background: rgba(0, 115, 170, 0.1);
          border-color: var(--database-primary-color);
        }

        &.notion-table-row-interactive {
          // 键盘导航支持
          &:focus {
            outline: 2px solid var(--database-primary-color);
            outline-offset: -2px;
          }

          // 选中状态
          &.selected {
            background: rgba(0, 115, 170, 0.15);
            
            &:hover {
              background: rgba(0, 115, 170, 0.2);
            }
          }
        }

        .notion-table-cell {
          .cell-content {
            &.cell-type-checkbox {
              text-align: center;
              font-size: 16px;
              color: var(--database-primary-color);
            }

            &.cell-type-number {
              text-align: right;
              font-family: 'Courier New', monospace;
            }

            &.cell-type-date {
              color: #666;
            }

            &.cell-type-select {
              display: inline-block;
              padding: 2px 8px;
              background: var(--database-secondary-color);
              border-radius: 12px;
              font-size: 12px;
              font-weight: 500;
            }

            &.cell-type-multi_select {
              .select-tag {
                display: inline-block;
                padding: 2px 6px;
                margin: 1px 2px;
                background: var(--database-secondary-color);
                border-radius: 8px;
                font-size: 11px;
                font-weight: 500;
              }
            }
          }

          // 链接样式
          a {
            color: var(--database-primary-color);
            text-decoration: none;
            
            &:hover {
              text-decoration: underline;
            }

            &:focus {
              outline: 1px solid var(--database-primary-color);
              outline-offset: 1px;
            }
          }
        }
      }
    }
  }

  // 空状态样式
  .empty-table {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #666;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .empty-message {
      font-size: 16px;
      font-weight: 500;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .notion-database-view-table {
    .notion-database-table {
      overflow-x: auto;
      
      .notion-table-header,
      .notion-table-row {
        min-width: 600px; // 确保在小屏幕上可以横向滚动
      }

      .notion-table-header-cell,
      .notion-table-cell {
        min-width: 100px;
        padding: 8px 12px;
        font-size: 14px;
      }

      .notion-table-header-cell {
        .sort-indicator {
          right: 4px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .notion-database-view-table {
    .notion-database-table {
      .notion-table-header-cell,
      .notion-table-cell {
        min-width: 80px;
        padding: 6px 8px;
        font-size: 13px;
      }

      .notion-table-header-cell {
        .header-text {
          margin-right: 4px;
        }

        .sort-indicator {
          font-size: 10px;
        }
      }
    }

    .empty-table {
      padding: 40px 20px;

      .empty-icon {
        font-size: 36px;
        margin-bottom: 12px;
      }

      .empty-message {
        font-size: 14px;
      }
    }
  }
}

// 加载状态
.notion-database-view-table {
  &.loading {
    .notion-database-table {
      opacity: 0.6;
      pointer-events: none;
    }
  }
}

// 表格性能优化：虚拟滚动支持
.notion-database-view-table {
  &.virtual-scroll {
    .notion-table-body {
      height: 400px;
      overflow-y: auto;
    }
  }
}

// 表格选择模式
.notion-database-view-table {
  &.selection-mode {
    .notion-table-header-cell:first-child::before,
    .notion-table-cell:first-child::before {
      content: '';
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid var(--database-border-color);
      border-radius: 3px;
      margin-right: 8px;
      vertical-align: middle;
    }

    .notion-table-row.selected {
      .notion-table-cell:first-child::before {
        background: var(--database-primary-color);
        border-color: var(--database-primary-color);
        background-image: url('data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>');
        background-size: 12px 12px;
        background-position: center;
        background-repeat: no-repeat;
      }
    }
  }
}

// 表格拖拽排序支持
.notion-database-view-table {
  &.sortable {
    .notion-table-row {
      &.dragging {
        opacity: 0.5;
        transform: rotate(2deg);
      }

      &.drag-over {
        border-top: 2px solid var(--database-primary-color);
      }
    }
  }
}
