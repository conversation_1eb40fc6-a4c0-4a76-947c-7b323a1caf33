/**
 * 数据库视图占位符样式
 * 
 * 为尚未实现的视图类型提供占位符样式
 */

.notion-database-view {
  .view-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    text-align: center;
    color: #666;
    background: var(--database-bg-color);
    border: 2px dashed var(--database-border-color);
    border-radius: var(--database-border-radius);
    margin: 20px 0;

    .placeholder-icon {
      font-size: 64px;
      margin-bottom: 20px;
      opacity: 0.6;
    }

    .placeholder-message {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 12px;
      color: var(--database-text-color);
    }

    .records-count {
      font-size: 14px;
      color: #999;
      background: var(--database-secondary-color);
      padding: 4px 12px;
      border-radius: 12px;
      display: inline-block;
    }

    // 悬停效果
    &:hover {
      border-color: var(--database-primary-color);
      background: rgba(0, 115, 170, 0.02);

      .placeholder-icon {
        opacity: 0.8;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .notion-database-view {
    .view-placeholder {
      padding: 60px 20px;

      .placeholder-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      .placeholder-message {
        font-size: 16px;
        margin-bottom: 10px;
      }

      .records-count {
        font-size: 13px;
      }
    }
  }
}

@media (max-width: 480px) {
  .notion-database-view {
    .view-placeholder {
      padding: 40px 15px;

      .placeholder-icon {
        font-size: 36px;
        margin-bottom: 12px;
      }

      .placeholder-message {
        font-size: 14px;
        margin-bottom: 8px;
      }

      .records-count {
        font-size: 12px;
        padding: 3px 10px;
      }
    }
  }
}
