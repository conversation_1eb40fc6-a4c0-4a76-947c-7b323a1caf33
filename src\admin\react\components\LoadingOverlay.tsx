/**
 * 加载覆盖层组件
 */

// React imports handled by JSX transform
import { useAppState } from '../context/AppContext';

export function LoadingOverlay() {
  const { isLoading } = useAppState();

  if (!isLoading) {
    return null;
  }

  return (
    <div className="notion-wp-loading-overlay">
      <div className="loading-content">
        <div className="spinner is-active" />
        <p>加载中...</p>
      </div>
    </div>
  );
}
