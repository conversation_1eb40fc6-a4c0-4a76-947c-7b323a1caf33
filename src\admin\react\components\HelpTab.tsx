/**
 * 使用帮助标签页组件
 */

import { useState } from 'react';
import { useI18n } from '../hooks/useWordPress';
import { Button } from './Button';

export function HelpTab() {
  const { __ } = useI18n();
  const [activeSection, setActiveSection] = useState('getting-started');

  const helpSections = [
    { id: 'getting-started', title: '🚀 快速开始', icon: 'admin-home' },
    { id: 'api-setup', title: '🔑 API设置', icon: 'admin-network' },
    { id: 'field-mapping', title: '🔗 字段映射', icon: 'admin-links' },
    { id: 'sync-guide', title: '🔄 同步指南', icon: 'update' },
    { id: 'troubleshooting', title: '🔧 故障排除', icon: 'admin-tools' },
    { id: 'faq', title: '❓ 常见问题', icon: 'editor-help' },
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'getting-started':
        return (
          <div className="help-content">
            <h3>{__('🚀 快速开始', '🚀 快速开始')}</h3>
            <div className="help-steps">
              <div className="help-step">
                <div className="step-number">1</div>
                <div className="step-content">
                  <h4>{__('创建Notion集成', '创建Notion集成')}</h4>
                  <p>{__('访问 Notion 开发者页面，创建新的集成并获取API密钥。', '访问 Notion 开发者页面，创建新的集成并获取API密钥。')}</p>
                  <Button variant="secondary" onClick={() => window.open('https://developers.notion.com/docs/getting-started', '_blank')}>
                    {__('访问Notion开发者页面', '访问Notion开发者页面')}
                  </Button>
                </div>
              </div>
              
              <div className="help-step">
                <div className="step-number">2</div>
                <div className="step-content">
                  <h4>{__('配置数据库权限', '配置数据库权限')}</h4>
                  <p>{__('在Notion中，将您的集成添加到要同步的数据库中，确保集成有读取权限。', '在Notion中，将您的集成添加到要同步的数据库中，确保集成有读取权限。')}</p>
                </div>
              </div>
              
              <div className="help-step">
                <div className="step-number">3</div>
                <div className="step-content">
                  <h4>{__('填写插件设置', '填写插件设置')}</h4>
                  <p>{__('在"同步设置"标签页中填写API密钥和数据库ID，然后测试连接。', '在"同步设置"标签页中填写API密钥和数据库ID，然后测试连接。')}</p>
                </div>
              </div>
              
              <div className="help-step">
                <div className="step-number">4</div>
                <div className="step-content">
                  <h4>{__('配置字段映射', '配置字段映射')}</h4>
                  <p>{__('在"字段映射"标签页中设置Notion属性与WordPress字段的对应关系。', '在"字段映射"标签页中设置Notion属性与WordPress字段的对应关系。')}</p>
                </div>
              </div>
              
              <div className="help-step">
                <div className="step-number">5</div>
                <div className="step-content">
                  <h4>{__('开始同步', '开始同步')}</h4>
                  <p>{__('返回"同步设置"标签页，点击"智能同步"开始您的第一次同步。', '返回"同步设置"标签页，点击"智能同步"开始您的第一次同步。')}</p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'api-setup':
        return (
          <div className="help-content">
            <h3>{__('🔑 API设置详解', '🔑 API设置详解')}</h3>
            
            <div className="help-section">
              <h4>{__('获取API密钥', '获取API密钥')}</h4>
              <ol>
                <li>{__('访问 https://www.notion.so/my-integrations', '访问 https://www.notion.so/my-integrations')}</li>
                <li>{__('点击"新建集成"按钮', '点击"新建集成"按钮')}</li>
                <li>{__('填写集成名称和描述', '填写集成名称和描述')}</li>
                <li>{__('选择关联的工作区', '选择关联的工作区')}</li>
                <li>{__('创建后复制"内部集成令牌"', '创建后复制"内部集成令牌"')}</li>
              </ol>
            </div>

            <div className="help-section">
              <h4>{__('获取数据库ID', '获取数据库ID')}</h4>
              <p>{__('数据库ID可以从Notion数据库URL中获取：', '数据库ID可以从Notion数据库URL中获取：')}</p>
              <code>https://www.notion.so/workspace/数据库ID?v=视图ID</code>
              <p>{__('复制URL中的32位字符串作为数据库ID。', '复制URL中的32位字符串作为数据库ID。')}</p>
            </div>

            <div className="help-section">
              <h4>{__('配置数据库权限', '配置数据库权限')}</h4>
              <ol>
                <li>{__('在Notion中打开要同步的数据库', '在Notion中打开要同步的数据库')}</li>
                <li>{__('点击右上角的"..."菜单', '点击右上角的"..."菜单')}</li>
                <li>{__('选择"添加连接"', '选择"添加连接"')}</li>
                <li>{__('搜索并选择您创建的集成', '搜索并选择您创建的集成')}</li>
                <li>{__('确认授权', '确认授权')}</li>
              </ol>
            </div>
          </div>
        );

      case 'field-mapping':
        return (
          <div className="help-content">
            <h3>{__('🔗 字段映射指南', '🔗 字段映射指南')}</h3>
            
            <div className="help-section">
              <h4>{__('字段映射原理', '字段映射原理')}</h4>
              <p>{__('字段映射用于建立Notion数据库属性与WordPress文章字段之间的对应关系。插件会根据这些映射规则将Notion页面转换为WordPress文章。', '字段映射用于建立Notion数据库属性与WordPress文章字段之间的对应关系。插件会根据这些映射规则将Notion页面转换为WordPress文章。')}</p>
            </div>

            <div className="help-section">
              <h4>{__('支持的字段类型', '支持的字段类型')}</h4>
              <table className="help-table">
                <thead>
                  <tr>
                    <th>{__('WordPress字段', 'WordPress字段')}</th>
                    <th>{__('Notion属性类型', 'Notion属性类型')}</th>
                    <th>{__('说明', '说明')}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>{__('标题', '标题')}</td>
                    <td>{__('标题', '标题')}</td>
                    <td>{__('必需字段，页面标题', '必需字段，页面标题')}</td>
                  </tr>
                  <tr>
                    <td>{__('状态', '状态')}</td>
                    <td>{__('选择', '选择')}</td>
                    <td>{__('发布状态：Published, Draft等', '发布状态：Published, Draft等')}</td>
                  </tr>
                  <tr>
                    <td>{__('分类', '分类')}</td>
                    <td>{__('多选', '多选')}</td>
                    <td>{__('文章分类', '文章分类')}</td>
                  </tr>
                  <tr>
                    <td>{__('标签', '标签')}</td>
                    <td>{__('多选', '多选')}</td>
                    <td>{__('文章标签', '文章标签')}</td>
                  </tr>
                  <tr>
                    <td>{__('日期', '日期')}</td>
                    <td>{__('日期', '日期')}</td>
                    <td>{__('发布日期', '发布日期')}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div className="help-section">
              <h4>{__('映射规则', '映射规则')}</h4>
              <ul>
                <li>{__('字段名称不区分大小写', '字段名称不区分大小写')}</li>
                <li>{__('可以使用逗号分隔多个备选名称', '可以使用逗号分隔多个备选名称')}</li>
                <li>{__('系统会按顺序查找匹配的属性', '系统会按顺序查找匹配的属性')}</li>
                <li>{__('留空的字段将被忽略', '留空的字段将被忽略')}</li>
              </ul>
            </div>
          </div>
        );

      case 'sync-guide':
        return (
          <div className="help-content">
            <h3>{__('🔄 同步指南', '🔄 同步指南')}</h3>
            
            <div className="help-section">
              <h4>{__('同步类型', '同步类型')}</h4>
              <div className="sync-types">
                <div className="sync-type-card">
                  <h5>{__('智能同步', '智能同步')}</h5>
                  <p>{__('只同步有变化的页面，速度快，推荐日常使用。', '只同步有变化的页面，速度快，推荐日常使用。')}</p>
                </div>
                <div className="sync-type-card">
                  <h5>{__('完全同步', '完全同步')}</h5>
                  <p>{__('同步所有页面，确保数据一致性，适合初次设置或故障恢复。', '同步所有页面，确保数据一致性，适合初次设置或故障恢复。')}</p>
                </div>
              </div>
            </div>

            <div className="help-section">
              <h4>{__('自动同步', '自动同步')}</h4>
              <p>{__('插件支持多种自动同步方式：', '插件支持多种自动同步方式：')}</p>
              <ul>
                <li><strong>{__('定时同步', '定时同步')}</strong>: {__('基于WordPress Cron的定时任务', '基于WordPress Cron的定时任务')}</li>
                <li><strong>{__('Webhook同步', 'Webhook同步')}</strong>: {__('Notion内容变化时实时触发', 'Notion内容变化时实时触发')}</li>
              </ul>
            </div>

            <div className="help-section">
              <h4>{__('同步流程', '同步流程')}</h4>
              <ol>
                <li>{__('连接Notion API获取数据库信息', '连接Notion API获取数据库信息')}</li>
                <li>{__('检索页面列表和变更记录', '检索页面列表和变更记录')}</li>
                <li>{__('根据字段映射转换页面内容', '根据字段映射转换页面内容')}</li>
                <li>{__('创建或更新WordPress文章', '创建或更新WordPress文章')}</li>
                <li>{__('处理图片和附件', '处理图片和附件')}</li>
                <li>{__('更新同步记录', '更新同步记录')}</li>
              </ol>
            </div>
          </div>
        );

      case 'troubleshooting':
        return (
          <div className="help-content">
            <h3>{__('🔧 故障排除', '🔧 故障排除')}</h3>
            
            <div className="troubleshooting-item">
              <h4>{__('连接测试失败', '连接测试失败')}</h4>
              <div className="problem-solution">
                <div className="problem">
                  <strong>{__('可能原因：', '可能原因：')}</strong>
                  <ul>
                    <li>{__('API密钥错误或已过期', 'API密钥错误或已过期')}</li>
                    <li>{__('数据库ID格式不正确', '数据库ID格式不正确')}</li>
                    <li>{__('集成未授权访问数据库', '集成未授权访问数据库')}</li>
                    <li>{__('网络连接问题', '网络连接问题')}</li>
                  </ul>
                </div>
                <div className="solution">
                  <strong>{__('解决方案：', '解决方案：')}</strong>
                  <ul>
                    <li>{__('检查API密钥是否正确复制', '检查API密钥是否正确复制')}</li>
                    <li>{__('确认数据库ID为32位字符串', '确认数据库ID为32位字符串')}</li>
                    <li>{__('重新授权集成访问数据库', '重新授权集成访问数据库')}</li>
                    <li>{__('检查服务器网络设置', '检查服务器网络设置')}</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="troubleshooting-item">
              <h4>{__('同步失败或不完整', '同步失败或不完整')}</h4>
              <div className="problem-solution">
                <div className="problem">
                  <strong>{__('可能原因：', '可能原因：')}</strong>
                  <ul>
                    <li>{__('字段映射配置错误', '字段映射配置错误')}</li>
                    <li>{__('Notion页面结构不符合预期', 'Notion页面结构不符合预期')}</li>
                    <li>{__('服务器资源不足', '服务器资源不足')}</li>
                    <li>{__('API请求限制', 'API请求限制')}</li>
                  </ul>
                </div>
                <div className="solution">
                  <strong>{__('解决方案：', '解决方案：')}</strong>
                  <ul>
                    <li>{__('检查字段映射设置', '检查字段映射设置')}</li>
                    <li>{__('确保Notion页面有必需的属性', '确保Notion页面有必需的属性')}</li>
                    <li>{__('调整性能配置参数', '调整性能配置参数')}</li>
                    <li>{__('启用调试模式查看详细日志', '启用调试模式查看详细日志')}</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="troubleshooting-item">
              <h4>{__('Webhook不工作', 'Webhook不工作')}</h4>
              <div className="problem-solution">
                <div className="problem">
                  <strong>{__('可能原因：', '可能原因：')}</strong>
                  <ul>
                    <li>{__('Webhook URL配置错误', 'Webhook URL配置错误')}</li>
                    <li>{__('服务器防火墙阻止', '服务器防火墙阻止')}</li>
                    <li>{__('SSL证书问题', 'SSL证书问题')}</li>
                  </ul>
                </div>
                <div className="solution">
                  <strong>{__('解决方案：', '解决方案：')}</strong>
                  <ul>
                    <li>{__('检查Webhook URL是否可访问', '检查Webhook URL是否可访问')}</li>
                    <li>{__('配置服务器允许外部访问', '配置服务器允许外部访问')}</li>
                    <li>{__('确保网站有有效的SSL证书', '确保网站有有效的SSL证书')}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        );

      case 'faq':
        return (
          <div className="help-content">
            <h3>{__('❓ 常见问题', '❓ 常见问题')}</h3>
            
            <div className="faq-item">
              <h4>{__('Q: 插件是否支持多个Notion数据库？', 'Q: 插件是否支持多个Notion数据库？')}</h4>
              <p>{__('A: 当前版本每个WordPress站点只支持连接一个Notion数据库。如需同步多个数据库，建议使用多个WordPress站点。', 'A: 当前版本每个WordPress站点只支持连接一个Notion数据库。如需同步多个数据库，建议使用多个WordPress站点。')}</p>
            </div>

            <div className="faq-item">
              <h4>{__('Q: 同步会覆盖WordPress中的手动修改吗？', 'Q: 同步会覆盖WordPress中的手动修改吗？')}</h4>
              <p>{__('A: 是的，同步会覆盖WordPress文章内容。建议将Notion作为内容的主要编辑平台，避免在WordPress中直接修改同步的文章。', 'A: 是的，同步会覆盖WordPress文章内容。建议将Notion作为内容的主要编辑平台，避免在WordPress中直接修改同步的文章。')}</p>
            </div>

            <div className="faq-item">
              <h4>{__('Q: 如何处理Notion中的图片？', 'Q: 如何处理Notion中的图片？')}</h4>
              <p>{__('A: 插件会自动下载Notion中的图片到WordPress媒体库，并更新文章中的图片链接。支持常见的图片格式。', 'A: 插件会自动下载Notion中的图片到WordPress媒体库，并更新文章中的图片链接。支持常见的图片格式。')}</p>
            </div>

            <div className="faq-item">
              <h4>{__('Q: 同步频率如何设置？', 'Q: 同步频率如何设置？')}</h4>
              <p>{__('A: 可以设置定时同步（从每天两次到每月一次），也可以配置Webhook实现实时同步。推荐使用Webhook获得最佳体验。', 'A: 可以设置定时同步（从每天两次到每月一次），也可以配置Webhook实现实时同步。推荐使用Webhook获得最佳体验。')}</p>
            </div>

            <div className="faq-item">
              <h4>{__('Q: 插件对服务器性能有什么要求？', 'Q: 插件对服务器性能有什么要求？')}</h4>
              <p>{__('A: 建议PHP内存限制至少256MB，支持cURL扩展。对于大型数据库，建议使用性能较好的服务器并调整性能配置。', 'A: 建议PHP内存限制至少256MB，支持cURL扩展。对于大型数据库，建议使用性能较好的服务器并调整性能配置。')}</p>
            </div>
          </div>
        );

      default:
        return <div>{__('内容加载中...', '内容加载中...')}</div>;
    }
  };

  return (
    <div className="notion-wp-settings-section">
      <h2>{__('📖 使用帮助', '📖 使用帮助')}</h2>
      
      <div className="help-layout">
        <div className="help-sidebar">
          {helpSections.map(section => (
            <button
              key={section.id}
              className={`help-nav-item ${activeSection === section.id ? 'active' : ''}`}
              onClick={() => setActiveSection(section.id)}
            >
              <span className={`dashicons dashicons-${section.icon}`} />
              {section.title}
            </button>
          ))}
        </div>
        
        <div className="help-main">
          {renderContent()}
        </div>
      </div>
    </div>
  );
}
