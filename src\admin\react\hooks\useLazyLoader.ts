/**
 * 懒加载React Hook
 * 
 * 将前端LazyLoader功能包装为React Hook，提供图片懒加载功能
 */

import { useEffect, useRef, useCallback, useState } from 'react';
import { LazyLoader, type LazyLoadConfig, type LazyLoadStats } from '../../../frontend/components/LazyLoader';
import { on, off } from '../../../shared/core/EventBus';

// Hook配置选项
export interface UseLazyLoaderOptions extends Partial<LazyLoadConfig> {
  enabled?: boolean;
  autoInit?: boolean;
}

// Hook返回值接口
export interface UseLazyLoaderReturn {
  stats: LazyLoadStats | null;
  isSupported: boolean;
  loadImage: (img: HTMLImageElement) => void;
  preloadImages: (urls: string[]) => Promise<void>;
  getStats: () => any;
  destroy: () => void;
}

/**
 * 懒加载Hook
 */
export function useLazyLoader(options: UseLazyLoaderOptions = {}): UseLazyLoaderReturn {
  const {
    enabled = true,
    autoInit = true,
    ...lazyConfig
  } = options;

  const [stats, setStats] = useState<LazyLoadStats | null>(null);
  const [isSupported, setIsSupported] = useState(false);
  const loaderRef = useRef<LazyLoader | null>(null);
  const mountedRef = useRef(true);

  // 安全更新状态
  const safeSetState = useCallback((updater: () => void) => {
    if (mountedRef.current) {
      updater();
    }
  }, []);

  // 初始化懒加载器
  const initLoader = useCallback(() => {
    if (!enabled || loaderRef.current) return;

    try {
      loaderRef.current = LazyLoader.getInstance(lazyConfig);
      
      safeSetState(() => {
        setIsSupported('IntersectionObserver' in window);
      });

      console.log('🖼️ [useLazyLoader] 懒加载器已初始化');
    } catch (error) {
      console.error('🖼️ [useLazyLoader] 初始化失败:', error);
    }
  }, [enabled, lazyConfig, safeSetState]);

  // 手动加载图片
  const loadImage = useCallback((img: HTMLImageElement) => {
    if (!loaderRef.current) return;

    try {
      loaderRef.current.loadImageManually(img);
    } catch (error) {
      console.error('🖼️ [useLazyLoader] 加载图片失败:', error);
    }
  }, []);

  // 预加载图片
  const preloadImages = useCallback(async (urls: string[]) => {
    if (!loaderRef.current) return;

    try {
      await loaderRef.current.preloadImages(urls);
    } catch (error) {
      console.error('🖼️ [useLazyLoader] 预加载图片失败:', error);
    }
  }, []);

  // 获取统计信息
  const getStats = useCallback(() => {
    if (!loaderRef.current) return null;

    try {
      return loaderRef.current.getStats();
    } catch (error) {
      console.error('🖼️ [useLazyLoader] 获取统计失败:', error);
      return null;
    }
  }, []);

  // 销毁懒加载器
  const destroy = useCallback(() => {
    if (!loaderRef.current) return;

    try {
      loaderRef.current.destroy();
      loaderRef.current = null;
      console.log('🖼️ [useLazyLoader] 懒加载器已销毁');
    } catch (error) {
      console.error('🖼️ [useLazyLoader] 销毁失败:', error);
    }
  }, []);

  // 事件监听器
  useEffect(() => {
    if (!enabled) return;

    // 监听懒加载事件
    const handleLoaderInitialized = (event: any) => {
      const { observerSupported } = event.detail || event;
      safeSetState(() => {
        setIsSupported(observerSupported);
      });
    };

    const handleImageLoaded = (event: any) => {
      const { stats: newStats } = event.detail || event;
      if (newStats) {
        safeSetState(() => {
          setStats(newStats);
        });
      }
    };

    const handleImageError = (event: any) => {
      const { stats: newStats } = event.detail || event;
      if (newStats) {
        safeSetState(() => {
          setStats(newStats);
        });
      }
    };

    on('lazy:loader:initialized', handleLoaderInitialized);
    on('lazy:image:loaded', handleImageLoaded);
    on('lazy:image:error', handleImageError);

    return () => {
      off('lazy:loader:initialized', handleLoaderInitialized);
      off('lazy:image:loaded', handleImageLoaded);
      off('lazy:image:error', handleImageError);
    };
  }, [enabled, safeSetState]);

  // 自动初始化
  useEffect(() => {
    if (autoInit && enabled) {
      initLoader();
    }
  }, [autoInit, enabled, initLoader]);

  // 组件卸载清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      // 注意：不自动销毁懒加载器，因为它是单例，可能被其他组件使用
    };
  }, []);

  return {
    stats,
    isSupported,
    loadImage,
    preloadImages,
    getStats,
    destroy,
  };
}

/**
 * 图片懒加载Hook
 * 
 * 专门用于单个图片元素的懒加载
 */
export function useLazyImage(src: string, options: UseLazyLoaderOptions = {}) {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const { loadImage } = useLazyLoader(options);

  // 处理图片加载
  const handleLoad = useCallback(() => {
    setLoaded(true);
    setLoading(false);
    setError(null);
  }, []);

  const handleError = useCallback(() => {
    setError('图片加载失败');
    setLoading(false);
    setLoaded(false);
  }, []);

  // 设置图片元素
  useEffect(() => {
    const img = imgRef.current;
    if (!img || !src) return;

    img.src = src;
    img.addEventListener('load', handleLoad);
    img.addEventListener('error', handleError);

    // 开始加载
    setLoading(true);
    loadImage(img);

    return () => {
      img.removeEventListener('load', handleLoad);
      img.removeEventListener('error', handleError);
    };
  }, [src, loadImage, handleLoad, handleError]);

  return {
    imgRef,
    loaded,
    loading,
    error,
    loadImage: () => imgRef.current ? loadImage(imgRef.current) : undefined,
  };
}

/**
 * 容器懒加载Hook
 * 
 * 用于观察容器内的所有图片
 */
export function useLazyContainer(options: UseLazyLoaderOptions = {}) {
  const containerRef = useRef<HTMLElement>(null);
  const { loadImage, preloadImages } = useLazyLoader(options);

  // 加载容器内的图片
  const loadImages = useCallback(() => {
    const container = containerRef.current;
    if (!container) return;

    const images = container.querySelectorAll('img[data-src]');
    images.forEach(img => {
      loadImage(img as HTMLImageElement);
    });
  }, [loadImage]);

  // 预加载容器内的图片URL
  const preloadContainerImages = useCallback(async () => {
    const container = containerRef.current;
    if (!container) return;

    const images = container.querySelectorAll('img[data-src]');
    const urls = Array.from(images).map(img => (img as HTMLImageElement).dataset.src).filter(Boolean) as string[];

    if (urls.length > 0) {
      await preloadImages(urls);
    }
  }, [preloadImages]);

  return {
    containerRef,
    loadImages,
    preloadContainerImages,
  };
}
