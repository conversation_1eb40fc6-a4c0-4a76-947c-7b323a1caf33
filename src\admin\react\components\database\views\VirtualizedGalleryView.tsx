/**
 * 虚拟化画廊视图React组件
 * 
 * 为大数据量场景提供性能优化的画廊视图
 */

import React, { useState, useMemo, useCallback, useRef } from 'react';
import type { GalleryViewProps, DatabaseRecord } from '../types';

// 虚拟滚动配置
interface VirtualScrollConfig {
  itemHeight: number;
  itemWidth: number;
  containerHeight: number;
  columnsCount: number;
  gap: number;
  overscan: number; // 预渲染的额外行数
}

// 虚拟化画廊Props
interface VirtualizedGalleryViewProps extends GalleryViewProps {
  virtualScrollConfig?: Partial<VirtualScrollConfig>;
}

// 默认虚拟滚动配置
const DEFAULT_VIRTUAL_CONFIG: VirtualScrollConfig = {
  itemHeight: 320, // 每个卡片高度
  itemWidth: 280, // 每个卡片宽度
  containerHeight: 600, // 容器高度
  columnsCount: 3, // 列数
  gap: 20, // 间距
  overscan: 2, // 预渲染2行
};

/**
 * 虚拟化画廊视图组件
 */
export function VirtualizedGalleryView({
  records,
  databaseInfo,
  options,
  onRecordClick,
  virtualScrollConfig = {},
  className = '',
  children,
  ...props
}: VirtualizedGalleryViewProps) {
  const config = useMemo(() => ({ ...DEFAULT_VIRTUAL_CONFIG, ...virtualScrollConfig }), [virtualScrollConfig]);
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // 计算网格布局
  const gridLayout = useMemo(() => {
    const rowsCount = Math.ceil(records.length / config.columnsCount);
    const totalHeight = rowsCount * (config.itemHeight + config.gap) - config.gap;
    
    return {
      rowsCount,
      totalHeight,
      itemsPerRow: config.columnsCount,
    };
  }, [records.length, config]);

  // 计算可见范围
  const visibleRange = useMemo(() => {
    const startRow = Math.floor(scrollTop / (config.itemHeight + config.gap));
    const endRow = Math.min(
      startRow + Math.ceil(config.containerHeight / (config.itemHeight + config.gap)) + config.overscan,
      gridLayout.rowsCount
    );
    
    return {
      start: Math.max(0, startRow - config.overscan),
      end: endRow,
    };
  }, [scrollTop, config, gridLayout.rowsCount]);

  // 可见记录
  const visibleRecords = useMemo(() => {
    const startIndex = visibleRange.start * config.columnsCount;
    const endIndex = visibleRange.end * config.columnsCount;
    return records.slice(startIndex, endIndex);
  }, [records, visibleRange, config.columnsCount]);

  // 处理滚动
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  // 偏移量
  const offsetY = visibleRange.start * (config.itemHeight + config.gap);

  if (records.length === 0) {
    return (
      <div className={`notion-database-view-gallery virtualized ${className}`} {...props}>
        <div className="empty-gallery">
          <div className="empty-icon">🖼️</div>
          <div className="empty-message">暂无数据</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`notion-database-view-gallery virtualized ${className}`} {...props}>
      {/* 虚拟滚动容器 */}
      <div
        ref={containerRef}
        className="virtualized-gallery-container"
        style={{ 
          height: config.containerHeight, 
          overflow: 'auto',
          position: 'relative'
        }}
        onScroll={handleScroll}
      >
        {/* 总高度占位符 */}
        <div style={{ height: gridLayout.totalHeight, position: 'relative' }}>
          {/* 可见项容器 */}
          <div
            className="notion-database-gallery"
            style={{
              transform: `translateY(${offsetY}px)`,
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              display: 'grid',
              gridTemplateColumns: `repeat(${config.columnsCount}, 1fr)`,
              gap: config.gap,
              padding: config.gap,
            }}
          >
            {visibleRecords.map((record, index) => {
              const actualIndex = visibleRange.start * config.columnsCount + index;
              return (
                <VirtualizedGalleryCard
                  key={record.id}
                  record={record}
                  databaseInfo={databaseInfo}
                  height={config.itemHeight}
                  onClick={onRecordClick}
                  index={actualIndex}
                  options={options}
                />
              );
            })}
          </div>
        </div>
      </div>
      {children}
    </div>
  );
}

/**
 * 虚拟化画廊卡片组件
 */
interface VirtualizedGalleryCardProps {
  record: DatabaseRecord;
  databaseInfo: { properties?: Record<string, unknown> };
  height: number;
  onClick?: (record: DatabaseRecord) => void;
  index: number;
  options?: { showProperties?: string[]; hideProperties?: string[] };
}

function VirtualizedGalleryCard({
  record,
  databaseInfo: _databaseInfo,
  height,
  onClick,
  index,
  options: _options
}: VirtualizedGalleryCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleClick = useCallback(() => {
    onClick?.(record);
  }, [record, onClick]);

  // 获取封面URL
  const getCoverUrl = useCallback((cover: { type?: string; file?: { url?: string }; external?: { url?: string } } | null): string | null => {
    if (!cover) return null;
    
    if (cover.type === 'file' && cover.file?.url) {
      return cover.file.url;
    } else if (cover.type === 'external' && cover.external?.url) {
      return cover.external.url;
    }
    
    return null;
  }, []);

  // 获取标题
  const getTitle = useCallback((rec: DatabaseRecord): string => {
    for (const [name, value] of Object.entries(rec.properties)) {
      if (name.toLowerCase().includes('title') || 
          name.toLowerCase().includes('名称') ||
          name.toLowerCase().includes('name')) {
        if (value && typeof value === 'object' && 'title' in value && Array.isArray(value.title)) {
          return value.title.map((item: { plain_text?: string }) => item.plain_text || '').join('') || '无标题';
        } else if (typeof value === 'string') {
          return value || '无标题';
        }
      }
    }
    return '无标题';
  }, []);

  const coverUrl = getCoverUrl(record.cover as { type?: string; file?: { url?: string }; external?: { url?: string } } | null);
  const title = getTitle(record);

  // 处理图片加载
  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
    setImageError(false);
  }, []);

  const handleImageError = useCallback(() => {
    setImageError(true);
    setImageLoaded(false);
  }, []);

  return (
    <div
      className={`notion-gallery-card ${onClick ? 'notion-gallery-card-interactive' : ''}`}
      style={{ height }}
      onClick={handleClick}
      data-record-id={record.id}
      data-index={index}
    >
      {/* 封面 */}
      <div className="notion-record-cover" style={{ height: height * 0.6 }}>
        {coverUrl ? (
          <>
            <img
              src={coverUrl}
              alt="Record cover"
              className={`notion-lazy-image ${imageLoaded ? 'loaded' : ''} ${imageError ? 'error' : ''}`}
              onLoad={handleImageLoad}
              onError={handleImageError}
              loading="lazy"
              style={{ 
                width: '100%', 
                height: '100%', 
                objectFit: 'cover',
                opacity: imageLoaded ? 1 : 0,
                transition: 'opacity 0.3s ease'
              }}
            />
            {!imageLoaded && !imageError && (
              <div className="cover-loading" style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: 'rgba(255, 255, 255, 0.9)'
              }}>
                <div className="loading-spinner" style={{
                  width: 24,
                  height: 24,
                  border: '2px solid #f3f3f3',
                  borderTop: '2px solid #0073aa',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
              </div>
            )}
          </>
        ) : (
          <div className="cover-placeholder" style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
            height: '100%'
          }}>
            <span style={{ fontSize: 32, opacity: 0.5 }}>🖼️</span>
          </div>
        )}
      </div>

      {/* 内容 */}
      <div className="notion-gallery-content" style={{ 
        padding: 12, 
        height: height * 0.4,
        overflow: 'hidden'
      }}>
        <div className="notion-record-title" style={{
          fontWeight: 600,
          fontSize: 14,
          marginBottom: 8,
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          lineHeight: 1.4
        }}>
          {title}
        </div>
        
        {/* 简化的属性显示 */}
        <div className="notion-record-properties" style={{ fontSize: 12, color: '#666' }}>
          {Object.entries(record.properties).slice(0, 2).map(([name, value], idx) => {
            if (name.toLowerCase().includes('title') || 
                name.toLowerCase().includes('名称') ||
                name.toLowerCase().includes('name')) {
              return null;
            }
            
            let displayValue = '';
            if (value && typeof value === 'object' && 'name' in value) {
              displayValue = value.name;
            } else if (typeof value === 'string') {
              displayValue = value;
            } else if (typeof value === 'number') {
              displayValue = value.toString();
            }
            
            if (!displayValue) return null;
            
            return (
              <div key={idx} style={{ marginBottom: 2, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                <span style={{ fontWeight: 500 }}>{name}:</span> {displayValue}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

export default VirtualizedGalleryView;
