/**
 * 数据库视图组合Hook
 * 
 * 结合数据库记录管理和信息管理，提供完整的数据库视图功能
 */

import { useCallback, useMemo } from 'react';
import { useDatabaseRecords, type UseDatabaseRecordsOptions } from './useDatabaseRecords';
import { useDatabaseInfo, type UseDatabaseInfoOptions } from './useDatabaseInfo';
import type {
  DatabaseRecord,
  DatabaseInfo,
  DatabaseFilter,
  DatabaseSort
} from '../types';

// 组合hook配置选项
export interface UseDatabaseViewOptions {
  records?: UseDatabaseRecordsOptions;
  info?: UseDatabaseInfoOptions;
}

// 组合状态接口
export interface DatabaseViewState {
  records: DatabaseRecord[];
  databaseInfo: DatabaseInfo | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  hasMore: boolean;
  isEmpty: boolean;
  canLoadMore: boolean;
}

// 组合hook返回值接口
export interface UseDatabaseViewReturn {
  // 组合状态
  state: DatabaseViewState;
  
  // 记录操作
  loadRecords: (query?: Record<string, any>) => Promise<DatabaseRecord[]>;
  loadMoreRecords: () => Promise<DatabaseRecord[]>;
  searchRecords: (searchTerm: string, searchProperty?: string) => Promise<DatabaseRecord[]>;
  filterRecords: (filters: DatabaseFilter[]) => Promise<DatabaseRecord[]>;
  sortRecords: (sorts: DatabaseSort[]) => Promise<DatabaseRecord[]>;
  refreshRecords: () => Promise<DatabaseRecord[]>;
  clearRecords: () => void;
  
  // 信息操作
  loadInfo: () => Promise<DatabaseInfo | null>;
  refreshInfo: () => Promise<DatabaseInfo | null>;
  clearInfo: () => void;
  
  // 组合操作
  refreshAll: () => Promise<void>;
  clearAll: () => void;
  
  // 状态查询
  isLoading: boolean;
  hasError: boolean;
  hasRecords: boolean;
  hasInfo: boolean;
}

/**
 * 数据库视图组合Hook
 */
export function useDatabaseView(
  databaseId: string,
  options: UseDatabaseViewOptions = {}
): UseDatabaseViewReturn {
  const { records: recordsOptions = {}, info: infoOptions = {} } = options;

  // 使用子hooks
  const recordsHook = useDatabaseRecords(databaseId, recordsOptions);
  const infoHook = useDatabaseInfo(databaseId, infoOptions);

  // 组合状态
  const state = useMemo((): DatabaseViewState => {
    const recordsState = recordsHook.state;
    const infoState = infoHook.state;

    return {
      records: recordsState.records,
      databaseInfo: infoState.info,
      loading: recordsState.loading || infoState.loading,
      error: recordsState.error || infoState.error,
      totalCount: recordsState.totalCount,
      hasMore: recordsState.hasMore,
      isEmpty: recordsHook.isEmpty,
      canLoadMore: recordsHook.canLoadMore,
    };
  }, [recordsHook.state, recordsHook.isEmpty, recordsHook.canLoadMore, infoHook.state]);

  // 组合操作
  const refreshAll = useCallback(async (): Promise<void> => {
    try {
      await Promise.all([
        recordsHook.refreshRecords(),
        infoHook.refreshInfo(),
      ]);
    } catch (error) {
      console.error('刷新数据库视图失败:', error);
      throw error;
    }
  }, [recordsHook.refreshRecords, infoHook.refreshInfo]);

  const clearAll = useCallback(() => {
    recordsHook.clearRecords();
    infoHook.clearInfo();
  }, [recordsHook.clearRecords, infoHook.clearInfo]);

  // 计算派生状态
  const isLoading = recordsHook.isLoading || infoHook.isLoading;
  const hasError = recordsHook.hasError || infoHook.hasError;
  const hasRecords = state.records.length > 0;
  const hasInfo = infoHook.hasInfo;

  return {
    // 组合状态
    state,
    
    // 记录操作（直接代理）
    loadRecords: recordsHook.loadRecords,
    loadMoreRecords: recordsHook.loadMoreRecords,
    searchRecords: recordsHook.searchRecords,
    filterRecords: recordsHook.filterRecords,
    sortRecords: recordsHook.sortRecords,
    refreshRecords: recordsHook.refreshRecords,
    clearRecords: recordsHook.clearRecords,
    
    // 信息操作（直接代理）
    loadInfo: infoHook.loadInfo,
    refreshInfo: infoHook.refreshInfo,
    clearInfo: infoHook.clearInfo,
    
    // 组合操作
    refreshAll,
    clearAll,
    
    // 状态查询
    isLoading,
    hasError,
    hasRecords,
    hasInfo,
  };
}

/**
 * 数据库视图Hook的便捷版本
 * 
 * 使用默认配置，适合大多数场景
 */
export function useSimpleDatabaseView(databaseId: string) {
  return useDatabaseView(databaseId, {
    records: {
      pageSize: 20,
      enableSearch: true,
      enableFilter: true,
      enableSort: true,
      initialLoad: true,
    },
    info: {
      autoLoad: true,
      cacheTime: 5 * 60 * 1000, // 5分钟缓存
    },
  });
}

/**
 * 只读数据库视图Hook
 * 
 * 禁用搜索、过滤、排序功能，适合展示场景
 */
export function useReadOnlyDatabaseView(databaseId: string) {
  return useDatabaseView(databaseId, {
    records: {
      pageSize: 50,
      enableSearch: false,
      enableFilter: false,
      enableSort: false,
      initialLoad: true,
    },
    info: {
      autoLoad: true,
      cacheTime: 10 * 60 * 1000, // 10分钟缓存
    },
  });
}

/**
 * 高性能数据库视图Hook
 * 
 * 优化配置，适合大数据量场景
 */
export function useHighPerformanceDatabaseView(databaseId: string) {
  return useDatabaseView(databaseId, {
    records: {
      pageSize: 10,
      enableSearch: true,
      enableFilter: true,
      enableSort: true,
      initialLoad: false, // 手动加载
      autoRefresh: false,
    },
    info: {
      autoLoad: true,
      cacheTime: 15 * 60 * 1000, // 15分钟缓存
    },
  });
}
