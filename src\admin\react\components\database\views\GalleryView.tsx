/**
 * 画廊视图React组件
 * 
 * 支持卡片式布局、图片懒加载、响应式网格和交互动画
 */

import { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import type {
  GalleryViewProps,
  DatabaseRecord,
  DatabaseInfo
} from '../types';

// 画廊卡片组件Props
interface GalleryCardProps {
  record: DatabaseRecord;
  databaseInfo: DatabaseInfo | null;
  onClick?: (record: DatabaseRecord) => void;
  showProperties?: string[];
  hideProperties?: string[];
}

// 记录封面组件Props
interface RecordCoverProps {
  record: DatabaseRecord;
  className?: string;
}

// 记录属性组件Props
interface RecordPropertiesProps {
  record: DatabaseRecord;
  databaseInfo: DatabaseInfo | null;
  showProperties?: string[];
  hideProperties?: string[];
  maxProperties?: number;
}

/**
 * 记录封面组件
 */
function RecordCover({ record, className = '' }: RecordCoverProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  // 获取封面URL
  const getCoverUrl = useCallback((cover: { type?: string; file?: { url?: string }; external?: { url?: string } } | null): string | null => {
    if (!cover) return null;
    
    if (cover.type === 'file' && cover.file?.url) {
      return cover.file.url;
    } else if (cover.type === 'external' && cover.external?.url) {
      return cover.external.url;
    }
    
    return null;
  }, []);

  const coverUrl = getCoverUrl(record.cover);

  // 处理图片加载
  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
    setImageError(false);
  }, []);

  const handleImageError = useCallback(() => {
    setImageError(true);
    setImageLoaded(false);
  }, []);

  // 如果没有封面，显示默认占位符
  if (!coverUrl) {
    return (
      <div className={`notion-record-cover no-cover ${className}`}>
        <div className="cover-placeholder">
          <span className="placeholder-icon">🖼️</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`notion-record-cover ${className}`}>
      <img
        ref={imgRef}
        src={coverUrl}
        alt="Record cover"
        className={`notion-lazy-image ${imageLoaded ? 'loaded' : ''} ${imageError ? 'error' : ''}`}
        onLoad={handleImageLoad}
        onError={handleImageError}
        loading="lazy"
      />
      {!imageLoaded && !imageError && (
        <div className="cover-loading">
          <div className="loading-spinner"></div>
        </div>
      )}
      {imageError && (
        <div className="cover-error">
          <span className="error-icon">❌</span>
          <span className="error-text">加载失败</span>
        </div>
      )}
    </div>
  );
}

/**
 * 记录标题组件
 */
function RecordTitle({ record }: { record: DatabaseRecord }) {
  // 查找标题属性
  const getTitle = useCallback((rec: DatabaseRecord): string => {
    // 查找标题属性
    for (const [name, value] of Object.entries(rec.properties)) {
      if (name.toLowerCase().includes('title') || 
          name.toLowerCase().includes('名称') ||
          name.toLowerCase().includes('name')) {
        if (value && typeof value === 'object' && 'title' in value && Array.isArray(value.title)) {
          return value.title.map((item: { plain_text?: string }) => item.plain_text || '').join('') || '无标题';
        } else if (value && typeof value === 'object' && 'plain_text' in value) {
          return value.plain_text || '无标题';
        } else if (typeof value === 'string') {
          return value || '无标题';
        }
      }
    }
    return '无标题';
  }, []);

  const title = getTitle(record);

  return (
    <div className="notion-record-title" title={title}>
      {title}
    </div>
  );
}

/**
 * 记录属性组件
 */
function RecordProperties({ 
  record, 
  databaseInfo, 
  showProperties = [], 
  hideProperties = [],
  maxProperties = 3 
}: RecordPropertiesProps) {
  // 获取可见属性
  const visibleProperties = useMemo(() => {
    if (!databaseInfo?.properties) return [];

    const properties: Array<{ name: string; value: unknown; type: string }> = [];

    Object.entries(record.properties).forEach(([name, value]) => {
      // 跳过标题属性（已在标题中显示）
      if (name.toLowerCase().includes('title') || 
          name.toLowerCase().includes('名称') ||
          name.toLowerCase().includes('name')) {
        return;
      }

      // 检查是否应该显示此属性
      const shouldShow = showProperties.length === 0 || showProperties.includes(name);
      const shouldHide = hideProperties.includes(name);

      if (shouldShow && !shouldHide && value !== null && value !== undefined) {
        const propertyConfig = databaseInfo.properties[name];
        properties.push({
          name,
          value,
          type: propertyConfig?.type || 'text',
        });
      }
    });

    return properties.slice(0, maxProperties);
  }, [record.properties, databaseInfo, showProperties, hideProperties, maxProperties]);

  // 格式化属性值
  const formatPropertyValue = useCallback((value: unknown, type: string): string => {
    if (value === null || value === undefined) return '';

    switch (type) {
      case 'rich_text':
        if (Array.isArray(value)) {
          return value.map((item: { plain_text?: string }) => item.plain_text || '').join('');
        }
        return (value as { plain_text?: string })?.plain_text || String(value);
      
      case 'number':
        return typeof value === 'number' ? value.toLocaleString() : String(value);
      
      case 'select':
        return (value as { name?: string })?.name || String(value);

      case 'multi_select':
        if (Array.isArray(value)) {
          return value.map((item: { name?: string }) => item.name || item).join(', ');
        }
        return String(value);

      case 'date':
        if ((value as { start?: string })?.start) {
          return new Date((value as { start: string }).start).toLocaleDateString();
        }
        return String(value);
      
      case 'checkbox':
        return value ? '✓' : '✗';
      
      case 'url':
        return value ? String(value) : '';
      
      case 'email':
        return value ? String(value) : '';
      
      case 'phone_number':
        return value ? String(value) : '';
      
      default:
        return String(value);
    }
  }, []);

  if (visibleProperties.length === 0) {
    return null;
  }

  return (
    <div className="notion-record-properties">
      {visibleProperties.map((property, index) => {
        const formattedValue = formatPropertyValue(property.value, property.type);

        if (!formattedValue) return null;

        return (
          <div key={index} className="notion-record-property">
            <span className="notion-property-label">{property.name}:</span>
            <span className={`notion-property-value property-type-${property.type}`}>
              {formattedValue}
            </span>
          </div>
        );
      })}
    </div>
  );
}

/**
 * 画廊卡片组件
 */
function GalleryCard({
  record,
  databaseInfo,
  onClick,
  showProperties,
  hideProperties
}: GalleryCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = useCallback(() => {
    onClick?.(record);
  }, [record, onClick]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  }, [handleClick]);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  return (
    <div
      className={`notion-gallery-card ${onClick ? 'notion-gallery-card-interactive' : ''} ${isHovered ? 'hovered' : ''}`}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? handleKeyDown : undefined}
      data-record-id={record.id}
    >
      <RecordCover record={record} />

      <div className="notion-gallery-content">
        <RecordTitle record={record} />
        <RecordProperties
          record={record}
          databaseInfo={databaseInfo}
          showProperties={showProperties}
          hideProperties={hideProperties}
          maxProperties={3}
        />
      </div>
    </div>
  );
}

/**
 * 画廊视图主组件
 */
export function GalleryView({
  records,
  databaseInfo,
  options,
  onRecordClick,
  className = '',
  children,
  ...props
}: GalleryViewProps) {
  const galleryRef = useRef<HTMLDivElement>(null);

  // 懒加载效果
  useEffect(() => {
    if (galleryRef.current) {
      // 触发懒加载刷新
      const images = galleryRef.current.querySelectorAll('img[loading="lazy"]');
      images.forEach(img => {
        // 确保图片在视口中时加载
        if (img instanceof HTMLImageElement && !img.complete) {
          const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                const image = entry.target as HTMLImageElement;
                if (image.dataset.src) {
                  image.src = image.dataset.src;
                }
                observer.unobserve(image);
              }
            });
          }, {
            rootMargin: '50px 0px',
            threshold: 0.1,
          });
          observer.observe(img);
        }
      });
    }
  }, [records]);

  // 如果没有记录，显示空状态
  if (records.length === 0) {
    return (
      <div className={`notion-database-view-gallery ${className}`} {...props}>
        <div className="empty-gallery">
          <div className="empty-icon">🖼️</div>
          <div className="empty-message">暂无记录</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`notion-database-view-gallery ${className}`} {...props}>
      <div ref={galleryRef} className="notion-database-gallery">
        {records.map((record) => (
          <GalleryCard
            key={record.id}
            record={record}
            databaseInfo={databaseInfo}
            onClick={onRecordClick}
            showProperties={options?.showProperties}
            hideProperties={options?.hideProperties}
          />
        ))}
      </div>
      {children}
    </div>
  );
}

export default GalleryView;
