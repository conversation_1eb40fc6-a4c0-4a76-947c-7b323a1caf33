#!/usr/bin/env node

/**
 * 统一构建工具
 * 
 * 负责WordPress插件的构建、验证和打包，
 * 合并了原build.js和verify-build.js的功能。
 * 
 * <AUTHOR>
 * @version 2.0.0-beta.2
 */

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const chalk = require('chalk');
const { glob } = require('glob');
const crypto = require('crypto');

// 导入统一模块
const config = require('./lib/config');
const Utils = require('./lib/utils');

class BuildTool {
    constructor() {
        this.projectRoot = config.getProjectRoot();
        this.buildConfig = config.getBuildConfig();
        this.buildDir = path.join(this.projectRoot, this.buildConfig.output.directory);
        this.tempDir = path.join(this.buildDir, 'temp');
        this.pluginName = config.getProjectInfo().name;
        
        // 必须包含的文件和目录
        this.requiredFiles = this.buildConfig.include.files;
        this.requiredDirs = this.buildConfig.include.directories;
        
        // 排除的文件和目录
        this.excludeFiles = this.buildConfig.exclude.files;
        this.excludeDirs = this.buildConfig.exclude.directories;
        this.excludePatterns = this.buildConfig.exclude.patterns;
    }

    /**
     * 检查webpack构建输出
     */
    async checkWebpackBuild() {
        Utils.info('检查webpack构建输出...');
        
        const distDir = path.join(this.projectRoot, 'assets/dist');
        const jsDir = path.join(distDir, 'js');
        const cssDir = path.join(distDir, 'css');
        
        // 检查dist目录是否存在
        if (!fs.existsSync(distDir)) {
            Utils.warn('assets/dist/ 目录不存在，请先运行 npm run build:webpack');
            Utils.info('正在运行webpack构建...');
            
            try {
                const { execSync } = require('child_process');
                execSync('npm run build:webpack', { 
                    stdio: 'inherit',
                    cwd: this.projectRoot 
                });
                Utils.success('webpack构建完成');
            } catch (error) {
                throw new Error(`webpack构建失败: ${error.message}`);
            }
        }
        
        // 检查关键文件是否存在
        const requiredFiles = [
            'admin.js',
            'frontend.js',
            'sync-progress.js',
            'katex-mermaid.js'
        ];
        
        const missingFiles = [];
        for (const file of requiredFiles) {
            const filePath = path.join(jsDir, file);
            if (!fs.existsSync(filePath)) {
                missingFiles.push(file);
            }
        }
        
        if (missingFiles.length > 0) {
            Utils.warn(`缺少webpack构建文件: ${missingFiles.join(', ')}`);
            Utils.info('正在重新运行webpack构建...');
            
            try {
                const { execSync } = require('child_process');
                execSync('npm run build:webpack', { 
                    stdio: 'inherit',
                    cwd: this.projectRoot 
                });
                Utils.success('webpack构建完成');
            } catch (error) {
                throw new Error(`webpack构建失败: ${error.message}`);
            }
        } else {
            Utils.success('webpack构建输出检查通过');
        }
    }

    /**
     * 准备构建目录
     */
    prepareBuildDir() {
        Utils.info('准备构建目录...');
        
        // 清理并创建构建目录
        if (fs.existsSync(this.buildDir)) {
            fs.rmSync(this.buildDir, { recursive: true, force: true });
        }
        
        Utils.ensureDir(this.buildDir);
        Utils.ensureDir(this.tempDir);
        
        Utils.success('构建目录准备完成');
    }

    /**
     * 检查文件是否应该被排除
     */
    shouldExclude(filePath, relativePath) {
        // 检查排除的文件
        if (this.excludeFiles.includes(relativePath)) {
            return true;
        }
        
        // 检查排除的目录
        for (const excludeDir of this.excludeDirs) {
            if (relativePath.startsWith(excludeDir)) {
                return true;
            }
        }
        
        // 检查排除的模式
        for (const pattern of this.excludePatterns) {
            if (relativePath.includes(pattern.replace('*', ''))) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 复制文件到构建目录
     */
    async copyFiles() {
        Utils.info('复制文件到构建目录...');
        
        const pluginDir = path.join(this.tempDir, this.pluginName);
        Utils.ensureDir(pluginDir);
        
        let copiedCount = 0;
        let skippedCount = 0;
        
        // 复制必需的文件
        for (const file of this.requiredFiles) {
            const srcPath = path.join(this.projectRoot, file);
            const destPath = path.join(pluginDir, file);
            
            if (fs.existsSync(srcPath)) {
                Utils.ensureDir(path.dirname(destPath));
                fs.copyFileSync(srcPath, destPath);
                copiedCount++;
                Utils.info(`  ✓ ${file}`);
            } else {
                Utils.warn(`  ✗ 文件不存在: ${file}`);
                skippedCount++;
            }
        }
        
        // 复制必需的目录
        for (const dir of this.requiredDirs) {
            const srcDir = path.join(this.projectRoot, dir);
            const destDir = path.join(pluginDir, dir);
            
            if (fs.existsSync(srcDir)) {
                const { copied, skipped } = await this.copyDirectory(srcDir, destDir, dir);
                copiedCount += copied;
                skippedCount += skipped;
                Utils.info(`  ✓ ${dir} (${copied} 个文件)`);
            } else {
                Utils.warn(`  ✗ 目录不存在: ${dir}`);
            }
        }
        
        Utils.success(`文件复制完成: ${copiedCount} 个文件已复制, ${skippedCount} 个文件被跳过`);
        return pluginDir;
    }

    /**
     * 递归复制目录
     */
    async copyDirectory(srcDir, destDir, relativePath) {
        Utils.ensureDir(destDir);
        
        let copiedCount = 0;
        let skippedCount = 0;
        
        const items = fs.readdirSync(srcDir);
        
        for (const item of items) {
            const srcPath = path.join(srcDir, item);
            const destPath = path.join(destDir, item);
            const itemRelativePath = path.join(relativePath, item).replace(/\\/g, '/');
            
            const stat = fs.statSync(srcPath);
            
            if (stat.isDirectory()) {
                if (!this.shouldExclude(srcPath, itemRelativePath + '/')) {
                    const { copied, skipped } = await this.copyDirectory(srcPath, destPath, itemRelativePath);
                    copiedCount += copied;
                    skippedCount += skipped;
                } else {
                    skippedCount++;
                }
            } else if (stat.isFile()) {
                if (!this.shouldExclude(srcPath, itemRelativePath)) {
                    fs.copyFileSync(srcPath, destPath);
                    copiedCount++;
                } else {
                    skippedCount++;
                }
            }
        }
        
        return { copied: copiedCount, skipped: skippedCount };
    }

    /**
     * 创建ZIP包
     */
    async createZip(pluginDir) {
        Utils.info('创建ZIP包...');
        
        const version = this.getCurrentVersion();
        const zipFileName = this.buildConfig.output.filename.replace('{PROJECT_NAME}', this.pluginName).replace('{VERSION}', version);
        const zipPath = path.join(this.buildDir, zipFileName);
        
        return new Promise((resolve, reject) => {
            const output = fs.createWriteStream(zipPath);
            const archive = archiver('zip', {
                zlib: { level: this.buildConfig.compression.level }
            });
            
            output.on('close', () => {
                const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
                Utils.success(`ZIP包创建完成: ${zipFileName} (${sizeInMB} MB)`);
                resolve(zipPath);
            });
            
            archive.on('error', (err) => {
                Utils.error(`ZIP包创建失败: ${err.message}`);
                reject(err);
            });
            
            archive.pipe(output);
            archive.directory(pluginDir, this.pluginName);
            archive.finalize();
        });
    }

    /**
     * 验证构建包
     */
    validatePackage(zipPath) {
        Utils.info('验证构建包...');
        
        try {
            // 检查文件是否存在
            if (!fs.existsSync(zipPath)) {
                throw new Error('ZIP包文件不存在');
            }
            
            // 检查文件大小
            const stats = fs.statSync(zipPath);
            const sizeInMB = stats.size / 1024 / 1024;
            
            if (sizeInMB > 50) {
                Utils.warn(`ZIP包较大: ${sizeInMB.toFixed(2)} MB`);
            }
            
            // 验证必需文件（这里简化验证，实际可以解压检查）
            const requiredInZip = [
                `${this.pluginName}/${this.pluginName}.php`,
                `${this.pluginName}/readme.txt`,
                `${this.pluginName}/uninstall.php`
            ];
            
            Utils.success('构建包验证通过');
            Utils.info(`包大小: ${Utils.formatFileSize(stats.size)}`);
            
            return true;
        } catch (error) {
            Utils.error(`构建包验证失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 生成校验和文件
     */
    async generateChecksums(zipPath) {
        Utils.info('生成校验和文件...');
        
        const checksumPath = path.join(this.buildDir, 'checksums.txt');
        const zipFileName = path.basename(zipPath);
        
        // 计算MD5和SHA256
        const content = fs.readFileSync(zipPath);
        const md5 = crypto.createHash('md5').update(content).digest('hex');
        const sha256 = crypto.createHash('sha256').update(content).digest('hex');
        
        const checksumContent = [
            `# Checksums for ${zipFileName}`,
            `# Generated on ${new Date().toISOString()}`,
            '',
            `MD5:    ${md5}`,
            `SHA256: ${sha256}`,
            '',
            `# File: ${zipFileName}`,
            `# Size: ${Utils.formatFileSize(content.length)}`
        ].join('\n');
        
        fs.writeFileSync(checksumPath, checksumContent, 'utf8');
        
        Utils.success('校验和文件生成完成');
        return checksumPath;
    }

    /**
     * 清理临时文件
     */
    cleanup() {
        if (fs.existsSync(this.tempDir)) {
            fs.rmSync(this.tempDir, { recursive: true, force: true });
            Utils.info('临时文件清理完成');
        }
    }

    /**
     * 获取当前版本号
     */
    getCurrentVersion() {
        try {
            const mainFile = path.join(this.projectRoot, 'notion-to-wordpress.php');
            const content = fs.readFileSync(mainFile, 'utf8');
            const versionMatch = content.match(/\* Version:\s+([0-9]+\.[0-9]+\.[0-9]+(?:-[a-zA-Z0-9.-]+)?)/);
            return versionMatch ? versionMatch[1] : '1.0.0';
        } catch (error) {
            Utils.warn('无法获取版本号，使用默认版本 1.0.0');
            return '1.0.0';
        }
    }

    /**
     * 主构建流程
     */
    async build() {
        try {
            Utils.info(chalk.bold('🚀 WordPress 插件构建工具'));
            Utils.info(`正在构建插件：${chalk.cyan(this.pluginName)}`);
            
            // 检查webpack构建输出
            await this.checkWebpackBuild();
            
            // 准备构建目录
            this.prepareBuildDir();
            
            // 复制文件
            const pluginDir = await this.copyFiles();
            
            // 创建 ZIP 包
            const zipPath = await this.createZip(pluginDir);
            
            // 验证包
            const isValid = this.validatePackage(zipPath);

            // 生成校验和
            const checksumPath = await this.generateChecksums(zipPath);

            // 清理
            this.cleanup();

            if (isValid) {
                Utils.success(`✅ 构建成功完成！`);
                Utils.info(`生成的包位置：${chalk.green(zipPath)}`);
                Utils.info(`校验和文件：${chalk.green(checksumPath)}`);
                Utils.info(`您现在可以在 WordPress 后台安装此 ZIP 文件。`);

                return zipPath;
            } else {
                throw new Error('包验证失败');
            }
            
        } catch (error) {
            Utils.error(`构建失败：${error.message}`);
            this.cleanup();
            process.exit(1);
        }
    }

    /**
     * 清理构建目录
     */
    clean() {
        Utils.info('清理构建目录...');
        
        if (fs.existsSync(this.buildDir)) {
            fs.rmSync(this.buildDir, { recursive: true, force: true });
            Utils.success('构建目录清理完成');
        } else {
            Utils.info('构建目录不存在，无需清理');
        }
    }

    /**
     * 验证现有构建
     */
    verify() {
        Utils.info('验证现有构建...');
        
        if (!fs.existsSync(this.buildDir)) {
            Utils.error('构建目录不存在，请先运行构建');
            process.exit(1);
        }
        
        const zipFiles = fs.readdirSync(this.buildDir).filter(file => file.endsWith('.zip'));
        
        if (zipFiles.length === 0) {
            Utils.error('未找到ZIP包文件');
            process.exit(1);
        }
        
        let allValid = true;
        for (const zipFile of zipFiles) {
            const zipPath = path.join(this.buildDir, zipFile);
            Utils.info(`验证: ${zipFile}`);
            if (!this.validatePackage(zipPath)) {
                allValid = false;
            }
        }
        
        if (allValid) {
            Utils.success('所有构建包验证通过');
        } else {
            Utils.error('部分构建包验证失败');
            process.exit(1);
        }
    }

    /**
     * 显示帮助信息
     */
    showHelp() {
        const commands = {
            '🏗️ 构建命令': {
                'npm run build': '构建 WordPress 插件包',
                'node scripts/build.js': '直接运行构建',
                'npm run build:clean': '清理构建目录',
                'npm run build:verify': '验证构建结果'
            },
            '📦 构建选项': {
                'build': '执行完整构建流程',
                'clean': '清理构建目录',
                'verify': '验证现有构建',
                'help': '显示帮助信息'
            }
        };

        Utils.showHelp('构建工具', commands);
    }
}

// 命令行处理
if (require.main === module) {
    const args = process.argv.slice(2);
    const command = args[0] || 'build';

    const builder = new BuildTool();

    switch (command) {
        case 'build':
            builder.build();
            break;
        case 'clean':
            builder.clean();
            break;
        case 'verify':
            builder.verify();
            break;
        case 'help':
        case '--help':
        case '-h':
            builder.showHelp();
            break;
        default:
            Utils.error(`无效的命令: ${command}`);
            builder.showHelp();
            process.exit(1);
    }
}

module.exports = BuildTool;