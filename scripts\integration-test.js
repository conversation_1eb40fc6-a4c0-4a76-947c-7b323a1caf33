#!/usr/bin/env node

/**
 * 集成测试脚本
 * 
 * 运行WordPress插件的集成测试
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

class IntegrationTester {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.testResults = [];
    }

    /**
     * 运行集成测试
     */
    async runTests() {
        console.log('🧪 开始运行集成测试...\n');

        try {
            // 检查基本文件结构
            await this.testFileStructure();
            
            // 检查PHP语法
            await this.testPHPSyntax();
            
            // 检查JavaScript构建
            await this.testJavaScriptBuild();
            
            // 检查CSS构建
            await this.testCSSBuild();
            
            // 显示测试结果
            this.showResults();
            
        } catch (error) {
            console.error('❌ 集成测试失败:', error.message);
            process.exit(1);
        }
    }

    /**
     * 测试文件结构
     */
    async testFileStructure() {
        console.log('📁 检查文件结构...');
        
        const requiredFiles = [
            'notion-to-wordpress.php',
            'readme.txt',
            'package.json',
            'webpack.config.js',
            'tsconfig.json'
        ];

        const requiredDirs = [
            'includes',
            'admin',
            'assets',
            'src'
        ];

        for (const file of requiredFiles) {
            const filePath = path.join(this.projectRoot, file);
            if (!fs.existsSync(filePath)) {
                throw new Error(`缺少必需文件: ${file}`);
            }
        }

        for (const dir of requiredDirs) {
            const dirPath = path.join(this.projectRoot, dir);
            if (!fs.existsSync(dirPath)) {
                throw new Error(`缺少必需目录: ${dir}`);
            }
        }

        this.testResults.push({ name: '文件结构检查', status: 'passed' });
        console.log('✅ 文件结构检查通过\n');
    }

    /**
     * 测试PHP语法
     */
    async testPHPSyntax() {
        console.log('🐘 检查PHP语法...');
        
        try {
            // 检查主插件文件
            execSync('php -l notion-to-wordpress.php', { 
                cwd: this.projectRoot,
                stdio: 'pipe'
            });

            // 检查includes目录下的PHP文件
            const includesDir = path.join(this.projectRoot, 'includes');
            if (fs.existsSync(includesDir)) {
                const phpFiles = this.findPHPFiles(includesDir);
                for (const phpFile of phpFiles) {
                    execSync(`php -l "${phpFile}"`, { 
                        cwd: this.projectRoot,
                        stdio: 'pipe'
                    });
                }
            }

            this.testResults.push({ name: 'PHP语法检查', status: 'passed' });
            console.log('✅ PHP语法检查通过\n');
            
        } catch (error) {
            this.testResults.push({ name: 'PHP语法检查', status: 'failed', error: error.message });
            console.log('⚠️  PHP语法检查跳过（PHP未安装或不可用）\n');
        }
    }

    /**
     * 测试JavaScript构建
     */
    async testJavaScriptBuild() {
        console.log('📦 检查JavaScript构建...');
        
        try {
            // 检查是否有构建输出
            const distDir = path.join(this.projectRoot, 'assets', 'dist', 'js');
            if (!fs.existsSync(distDir)) {
                throw new Error('缺少JavaScript构建输出目录');
            }

            const jsFiles = fs.readdirSync(distDir).filter(file => file.endsWith('.js'));
            if (jsFiles.length === 0) {
                throw new Error('没有找到JavaScript构建文件');
            }

            this.testResults.push({ name: 'JavaScript构建检查', status: 'passed' });
            console.log('✅ JavaScript构建检查通过\n');
            
        } catch (error) {
            this.testResults.push({ name: 'JavaScript构建检查', status: 'failed', error: error.message });
            console.log('❌ JavaScript构建检查失败:', error.message, '\n');
        }
    }

    /**
     * 测试CSS构建
     */
    async testCSSBuild() {
        console.log('🎨 检查CSS构建...');
        
        try {
            // 检查是否有CSS构建输出
            const distDir = path.join(this.projectRoot, 'assets', 'dist', 'css');
            if (!fs.existsSync(distDir)) {
                throw new Error('缺少CSS构建输出目录');
            }

            const cssFiles = fs.readdirSync(distDir).filter(file => file.endsWith('.css'));
            if (cssFiles.length === 0) {
                throw new Error('没有找到CSS构建文件');
            }

            this.testResults.push({ name: 'CSS构建检查', status: 'passed' });
            console.log('✅ CSS构建检查通过\n');
            
        } catch (error) {
            this.testResults.push({ name: 'CSS构建检查', status: 'failed', error: error.message });
            console.log('❌ CSS构建检查失败:', error.message, '\n');
        }
    }

    /**
     * 查找PHP文件
     */
    findPHPFiles(dir) {
        const phpFiles = [];
        
        function scanDir(currentDir) {
            const items = fs.readdirSync(currentDir);
            
            for (const item of items) {
                const itemPath = path.join(currentDir, item);
                const stat = fs.statSync(itemPath);
                
                if (stat.isDirectory()) {
                    scanDir(itemPath);
                } else if (item.endsWith('.php')) {
                    phpFiles.push(itemPath);
                }
            }
        }
        
        scanDir(dir);
        return phpFiles;
    }

    /**
     * 显示测试结果
     */
    showResults() {
        console.log('📊 集成测试结果:');
        console.log('='.repeat(50));
        
        let passed = 0;
        let failed = 0;
        
        for (const result of this.testResults) {
            const status = result.status === 'passed' ? '✅' : '❌';
            console.log(`${status} ${result.name}`);
            
            if (result.status === 'passed') {
                passed++;
            } else {
                failed++;
                if (result.error) {
                    console.log(`   错误: ${result.error}`);
                }
            }
        }
        
        console.log('='.repeat(50));
        console.log(`总计: ${this.testResults.length} 项测试`);
        console.log(`通过: ${passed} 项`);
        console.log(`失败: ${failed} 项`);
        
        if (failed > 0) {
            console.log('\n⚠️  有测试失败，请检查上述错误信息');
            process.exit(1);
        } else {
            console.log('\n🎉 所有集成测试通过！');
        }
    }
}

// 运行测试
if (require.main === module) {
    const tester = new IntegrationTester();
    tester.runTests().catch(error => {
        console.error('集成测试运行失败:', error);
        process.exit(1);
    });
}

module.exports = IntegrationTester;
