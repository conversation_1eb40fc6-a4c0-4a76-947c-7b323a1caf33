/**
 * 数据库视图React组件类型定义
 * 
 * 扩展现有类型系统，为数据库视图组件提供完整的TypeScript支持
 */

import type { ReactNode } from 'react';

// 基础组件Props
export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
}

// 重新导出现有类型，保持兼容性
export type ViewType =
  | 'table'
  | 'list'
  | 'gallery'
  | 'board'
  | 'calendar'
  | 'timeline';

// 数据库记录类型
export interface DatabaseRecord {
  id: string;
  properties: Record<string, any>;
  created_time: string;
  last_edited_time: string;
  url?: string;
  icon?: {
    type: string;
    emoji?: string;
    file?: { url: string };
    external?: { url: string };
  };
  cover?: {
    type: string;
    file?: { url: string };
    external?: { url: string };
  };
}

// 数据库信息类型
export interface DatabaseInfo {
  id: string;
  title: Array<{
    type: string;
    text: { content: string };
  }>;
  description: Array<{
    type: string;
    text: { content: string };
  }>;
  properties: Record<string, {
    id: string;
    name: string;
    type: string;
    [key: string]: any;
  }>;
  created_time: string;
  last_edited_time: string;
  url: string;
  archived: boolean;
  icon?: {
    type: string;
    emoji?: string;
    file?: { url: string };
    external?: { url: string };
  };
  cover?: {
    type: string;
    file?: { url: string };
    external?: { url: string };
  };
}

// 数据库过滤器类型
export interface DatabaseFilter {
  property?: string;
  condition?: string;
  value?: any;
}

// 数据库排序类型
export interface DatabaseSort {
  property: string;
  direction: 'asc' | 'desc';
}

// 渲染选项类型
export interface RenderOptions {
  viewType?: ViewType;
  showProperties?: string[];
  hideProperties?: string[];
  maxRecords?: number;
  enableInteraction?: boolean;
  responsive?: boolean;
}

// 属性配置类型
export interface PropertyConfig {
  name: string;
  type: string;
  visible: boolean;
  width?: string;
  format?: string;
}

// 数据库视图组件Props
export interface DatabaseViewProps extends BaseComponentProps {
  databaseId: string;
  defaultViewType?: ViewType;
  enableSearch?: boolean;
  enableFilter?: boolean;
  enableSort?: boolean;
  enablePagination?: boolean;
  pageSize?: number;
  autoRefresh?: boolean;
  onRecordClick?: (record: DatabaseRecord) => void;
  onViewTypeChange?: (viewType: ViewType) => void;
}

// 数据库工具栏组件Props
export interface DatabaseToolbarProps extends BaseComponentProps {
  viewType: ViewType;
  searchTerm: string;
  enableSearch?: boolean;
  enableFilter?: boolean;
  enableSort?: boolean;
  enableRefresh?: boolean;
  isLoading?: boolean;
  recordCount?: number;
  onViewTypeChange: (viewType: ViewType) => void;
  onSearchChange: (searchTerm: string) => void;
  onFilterClick?: () => void;
  onSortClick?: () => void;
  onRefreshClick?: () => void;
}

// 视图组件通用Props
export interface ViewComponentProps extends BaseComponentProps {
  records: DatabaseRecord[];
  databaseInfo: DatabaseInfo;
  options?: RenderOptions;
  onRecordClick?: (record: DatabaseRecord) => void;
}

// 表格视图Props
export interface TableViewProps extends ViewComponentProps {
  sortConfig?: DatabaseSort | null;
  onSort?: (property: string) => void;
}

// 画廊视图Props
export interface GalleryViewProps extends ViewComponentProps {
  columns?: number;
  cardHeight?: number;
}

// 看板视图Props
export interface BoardViewProps extends ViewComponentProps {
  groupByProperty?: string;
  columns?: string[];
}

// 数据库状态类型
export interface DatabaseState {
  records: DatabaseRecord[];
  databaseInfo: DatabaseInfo | null;
  loading: boolean;
  error: string | null;
  searchTerm: string;
  viewType: ViewType;
  filters: DatabaseFilter[];
  sorts: DatabaseSort[];
  hasMore: boolean;
  page: number;
  totalRecords: number;
}

// 数据库操作类型
export type DatabaseAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_RECORDS'; payload: DatabaseRecord[] }
  | { type: 'SET_DATABASE_INFO'; payload: DatabaseInfo }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_SEARCH_TERM'; payload: string }
  | { type: 'SET_VIEW_TYPE'; payload: ViewType }
  | { type: 'SET_FILTERS'; payload: DatabaseFilter[] }
  | { type: 'SET_SORTS'; payload: DatabaseSort[] }
  | { type: 'SET_HAS_MORE'; payload: boolean }
  | { type: 'SET_PAGE'; payload: number }
  | { type: 'RESET_STATE' };
