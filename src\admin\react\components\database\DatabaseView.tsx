/**
 * 数据库视图主容器组件
 * 
 * 这是数据库视图的主要容器组件，负责：
 * - 整体布局和状态管理
 * - 工具栏和视图内容的协调
 * - 与现有EventBus系统的集成
 */

import { useState, useEffect, useCallback } from 'react';
import { DatabaseToolbar } from './DatabaseToolbar';
import { TableView } from './views/TableView';
import { GalleryView } from './views/GalleryView';
import { BoardView } from './views/BoardView';
import { useDatabaseView } from '../../hooks/useDatabaseView';
import type {
  DatabaseViewProps,
  ViewType,
  DatabaseRecord
} from './types';

// 注意：原有的reducer代码已被hook替代，不再需要

/**
 * 数据库视图主组件
 */
export function DatabaseView({
  databaseId,
  defaultViewType = 'table',
  enableSearch = true,
  enableFilter = true,
  enableSort = true,
  enablePagination = true,
  pageSize = 20,
  autoRefresh = false,
  onRecordClick,
  onViewTypeChange,
  className = '',
  ...props
}: DatabaseViewProps) {
  // 使用数据库视图hook
  const databaseView = useDatabaseView(databaseId, {
    records: {
      pageSize,
      enableSearch,
      enableFilter,
      enableSort,
      autoRefresh,
      initialLoad: !!databaseId,
    },
    info: {
      autoLoad: !!databaseId,
    },
  });

  // 本地状态（视图类型和搜索词）
  const [viewType, setViewType] = useState<ViewType>(defaultViewType);
  const [searchTerm, setSearchTerm] = useState('');

  // 处理视图类型变化
  const handleViewTypeChange = useCallback((newViewType: ViewType) => {
    setViewType(newViewType);
    onViewTypeChange?.(newViewType);
  }, [onViewTypeChange]);

  // 处理搜索
  const handleSearchChange = useCallback((newSearchTerm: string) => {
    setSearchTerm(newSearchTerm);

    // 如果搜索词不为空，执行搜索
    if (newSearchTerm.trim()) {
      databaseView.searchRecords(newSearchTerm.trim()).catch(console.error);
    } else {
      // 搜索词为空时，重新加载所有记录
      databaseView.loadRecords().catch(console.error);
    }
  }, [databaseView]);

  // 处理过滤
  const handleFilterClick = useCallback(() => {
    // TODO: 实现过滤功能
    console.log('🔍 [数据库视图] 过滤按钮点击');
  }, []);

  // 处理排序
  const handleSortClick = useCallback(() => {
    // TODO: 实现排序功能
    console.log('↕️ [数据库视图] 排序按钮点击');
  }, []);

  // 处理刷新
  const handleRefreshClick = useCallback(() => {
    console.log('🔄 [数据库视图] 刷新按钮点击');
    databaseView.refreshAll().catch(console.error);
  }, [databaseView]);

  // 处理记录点击
  const handleRecordClick = useCallback((record: DatabaseRecord) => {
    onRecordClick?.(record);
  }, [onRecordClick]);

  // 处理加载更多
  const handleLoadMore = useCallback(() => {
    if (databaseView.state.canLoadMore) {
      databaseView.loadMoreRecords().catch(console.error);
    }
  }, [databaseView]);

  // 组件挂载时的初始化
  useEffect(() => {
    if (!databaseId) {
      console.warn('🔄 [数据库视图] 数据库ID未配置');
      return;
    }

    console.log('🔄 [数据库视图] 初始化中...', { databaseId, defaultViewType });
  }, [databaseId, defaultViewType]);

  // 渲染加载状态
  if (databaseView.isLoading && databaseView.state.records.length === 0) {
    return (
      <div className={`notion-database-view-component ${className}`} {...props}>
        <div className="database-view-container">
          <div className="loading-placeholder">
            <div className="spinner"></div>
            <span>加载中...</span>
          </div>
        </div>
      </div>
    );
  }

  // 渲染错误状态
  if (databaseView.hasError && databaseView.state.records.length === 0) {
    return (
      <div className={`notion-database-view-component ${className}`} {...props}>
        <div className="database-view-container">
          <div className="error-placeholder">
            <div className="error-icon">❌</div>
            <div className="error-message">{databaseView.state.error}</div>
            <button
              className="retry-button"
              type="button"
              onClick={() => databaseView.refreshAll().catch(console.error)}
            >
              重试
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 主要渲染
  return (
    <div className={`notion-database-view-component ${className}`} {...props}>
      {/* 数据库工具栏 */}
      <DatabaseToolbar
        viewType={viewType}
        searchTerm={searchTerm}
        enableSearch={enableSearch}
        enableFilter={enableFilter}
        enableSort={enableSort}
        enableRefresh={true}
        isLoading={databaseView.isLoading}
        recordCount={databaseView.state.totalCount}
        onViewTypeChange={handleViewTypeChange}
        onSearchChange={handleSearchChange}
        onFilterClick={handleFilterClick}
        onSortClick={handleSortClick}
        onRefreshClick={handleRefreshClick}
      />

      {/* 视图容器 */}
      <div className="database-view-container">
        {databaseView.state.isEmpty && !databaseView.isLoading ? (
          <div className="empty-placeholder">
            <div className="empty-icon">📭</div>
            <div className="empty-message">
              {searchTerm ? `未找到包含"${searchTerm}"的记录` : '暂无记录'}
            </div>
            {searchTerm && (
              <button
                className="clear-search-button"
                type="button"
                onClick={() => handleSearchChange('')}
              >
                清除搜索
              </button>
            )}
          </div>
        ) : (
          <div className={`notion-database-view notion-database-view-${viewType}`}>
            {/* 根据视图类型渲染对应组件 */}
            {viewType === 'table' && databaseView.state.databaseInfo && (
              <TableView
                records={databaseView.state.records}
                databaseInfo={databaseView.state.databaseInfo}
                options={{
                  viewType,
                  enableInteraction: true,
                  responsive: true,
                }}
                onRecordClick={handleRecordClick}
              />
            )}

            {viewType === 'list' && (
              <div className="view-placeholder">
                <div className="placeholder-icon">📋</div>
                <div className="placeholder-message">列表视图即将推出</div>
                <div className="records-count">
                  {databaseView.state.records.length} 条记录
                </div>
              </div>
            )}

            {viewType === 'gallery' && databaseView.state.databaseInfo && (
              <GalleryView
                records={databaseView.state.records}
                databaseInfo={databaseView.state.databaseInfo}
                options={{
                  viewType,
                  enableInteraction: true,
                  responsive: true,
                }}
                onRecordClick={handleRecordClick}
              />
            )}

            {viewType === 'board' && databaseView.state.databaseInfo && (
              <BoardView
                records={databaseView.state.records}
                databaseInfo={databaseView.state.databaseInfo}
                options={{
                  viewType,
                  enableInteraction: true,
                  responsive: true,
                }}
                onRecordClick={handleRecordClick}
              />
            )}

            {(viewType === 'calendar' || viewType === 'timeline') && (
              <div className="view-placeholder">
                <div className="placeholder-icon">📅</div>
                <div className="placeholder-message">{viewType === 'calendar' ? '日历' : '时间线'}视图即将推出</div>
                <div className="records-count">
                  {databaseView.state.records.length} 条记录
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 分页 */}
      {enablePagination && databaseView.state.canLoadMore && (
        <div className="database-pagination">
          <button
            className="load-more-button"
            type="button"
            onClick={handleLoadMore}
            disabled={databaseView.isLoading}
          >
            {databaseView.isLoading ? '加载中...' : '加载更多'}
          </button>
        </div>
      )}
    </div>
  );
}

export default DatabaseView;
