/**
 * 应用状态管理Context
 */

import React, { createContext, useContext, useReducer, useCallback } from 'react';
import type { 
  AppState, 
  AppAction, 
  AppContextType, 
  PluginSettings, 
  SyncStatus, 
  StatsData, 
  TabType, 
  ToastMessage 
} from '../types';

// 初始状态
const initialState: AppState = {
  settings: {
    notion_api_key: '',
    notion_database_id: '',
    sync_schedule: 'manual',
    delete_on_uninstall: false,
    field_mapping: {
      title: 'Title,标题',
      status: 'Status,状态',
      post_type: 'Type,类型',
      date: 'Date,日期',
      excerpt: 'Summary,摘要,Excerpt',
      featured_image: 'Featured Image,特色图片',
      categories: 'Categories,分类,Category',
      tags: 'Tags,标签,Tag',
      password: 'Password,密码',
    },
    performance_level: 'balanced',
    field_template: 'mixed',
    webhook_enabled: false,
    webhook_verify_token: '',
    webhook_token: '',
    webhook_incremental_sync: true,
    webhook_check_deletions: true,
    cron_incremental_sync: true,
    cron_check_deletions: true,
  },
  syncStatus: {
    isRunning: false,
    type: 'manual',
    progress: 0,
    currentStep: '',
  },
  stats: {
    importedCount: 0,
    publishedCount: 0,
    lastUpdate: '从未',
    nextRun: '未计划',
  },
  validation: {
    isValid: true,
    errors: {},
    warnings: {},
  },
  activeTab: 'api-settings',
  toasts: [],
  isLoading: false,
};

// Reducer函数
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_SETTINGS':
      return {
        ...state,
        settings: { ...state.settings, ...action.payload },
      };

    case 'SET_SYNC_STATUS':
      return {
        ...state,
        syncStatus: { ...state.syncStatus, ...action.payload },
      };

    case 'SET_STATS':
      return {
        ...state,
        stats: { ...state.stats, ...action.payload },
      };

    case 'SET_VALIDATION':
      return {
        ...state,
        validation: { ...state.validation, ...action.payload },
      };

    case 'SET_ACTIVE_TAB':
      return {
        ...state,
        activeTab: action.payload,
      };

    case 'ADD_TOAST':
      return {
        ...state,
        toasts: [...state.toasts, action.payload],
      };

    case 'REMOVE_TOAST':
      return {
        ...state,
        toasts: state.toasts.filter(toast => toast.id !== action.payload),
      };

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
      };

    case 'RESET_STATE':
      return initialState;

    default:
      return state;
  }
}

// Context创建
const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider组件
export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Action creators
  const updateSettings = useCallback((settings: Partial<PluginSettings>) => {
    dispatch({ type: 'SET_SETTINGS', payload: settings });
  }, []);

  const updateSyncStatus = useCallback((status: Partial<SyncStatus>) => {
    dispatch({ type: 'SET_SYNC_STATUS', payload: status });
  }, []);

  const updateStats = useCallback((stats: Partial<StatsData>) => {
    dispatch({ type: 'SET_STATS', payload: stats });
  }, []);

  const setActiveTab = useCallback((tab: TabType) => {
    dispatch({ type: 'SET_ACTIVE_TAB', payload: tab });
  }, []);

  const showToast = useCallback((toast: Omit<ToastMessage, 'id'>) => {
    const id = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    dispatch({ 
      type: 'ADD_TOAST', 
      payload: { ...toast, id } 
    });

    // 自动移除toast
    if (toast.duration !== 0) {
      setTimeout(() => {
        dispatch({ type: 'REMOVE_TOAST', payload: id });
      }, toast.duration || 5000);
    }
  }, []);

  const hideToast = useCallback((id: string) => {
    dispatch({ type: 'REMOVE_TOAST', payload: id });
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  }, []);

  const setError = useCallback((error: string | undefined) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  }, []);

  const contextValue: AppContextType = {
    state,
    dispatch,
    actions: {
      updateSettings,
      updateSyncStatus,
      updateStats,
      setActiveTab,
      showToast,
      hideToast,
      setLoading,
      setError,
    },
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
}

// Hook for using context
export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}

// 选择器hooks
export function useAppState() {
  const { state } = useAppContext();
  return state;
}

export function useAppActions() {
  const { actions } = useAppContext();
  return actions;
}

export function useSettings() {
  const { state } = useAppContext();
  return state.settings;
}

export function useSyncStatus() {
  const { state } = useAppContext();
  return state.syncStatus;
}

export function useStats() {
  const { state } = useAppContext();
  return state.stats;
}

export function useValidation() {
  const { state } = useAppContext();
  return state.validation;
}

export function useActiveTab() {
  const { state } = useAppContext();
  return state.activeTab;
}

export function useToasts() {
  const { state } = useAppContext();
  return state.toasts;
}
