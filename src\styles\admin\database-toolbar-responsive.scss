/**
 * 数据库工具栏响应式样式增强
 * 
 * 为数据库工具栏提供更好的移动端体验
 */

// 响应式断点
$mobile-breakpoint: 768px;
$tablet-breakpoint: 1024px;

// 数据库工具栏响应式样式
.database-toolbar {
  // 平板端适配
  @media (max-width: $tablet-breakpoint) {
    flex-wrap: wrap;
    gap: 8px;
    padding: 10px 12px;

    .toolbar-left,
    .toolbar-right {
      gap: 6px;
    }

    .view-type-selector {
      min-width: 100px;
      font-size: 13px;
    }

    .search-input {
      min-width: 150px;
      font-size: 13px;
    }

    .filter-button,
    .sort-button,
    .refresh-button {
      padding: 5px 10px;
      font-size: 13px;
    }
  }

  // 移动端适配
  @media (max-width: $mobile-breakpoint) {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px;

    .toolbar-left,
    .toolbar-right {
      justify-content: space-between;
      width: 100%;
      gap: 8px;
    }

    .toolbar-left {
      flex-wrap: wrap;
      
      .view-type-selector {
        flex: 1;
        min-width: 120px;
      }

      .search-input {
        flex: 2;
        min-width: 0;
        width: 100%;
        margin-top: 8px;
        order: 10; // 搜索框放到下一行
      }

      .filter-button,
      .sort-button {
        flex: 1;
        min-width: 60px;
        
        // 在小屏幕上只显示图标
        .button-text {
          display: none;
        }
      }
    }

    .toolbar-right {
      justify-content: space-between;
      align-items: center;

      .refresh-button {
        // 刷新按钮保持图标样式
      }

      .status-indicator {
        flex: 1;
        text-align: right;

        .record-count {
          font-size: 12px;
        }
      }
    }
  }

  // 超小屏幕适配
  @media (max-width: 480px) {
    .toolbar-left {
      .filter-button,
      .sort-button {
        padding: 6px 8px;
        
        .dashicons {
          margin-right: 0 !important;
        }
      }
    }

    .status-indicator {
      .loading-spinner {
        .spinner {
          width: 16px;
          height: 16px;
        }
      }

      .record-count {
        font-size: 11px;
      }
    }
  }
}

// 工具栏按钮状态增强
.database-toolbar {
  .filter-button,
  .sort-button,
  .refresh-button {
    // 活跃状态
    &.active {
      background: var(--database-primary-color);
      color: white;
      border-color: var(--database-primary-color);
    }

    // 禁用状态
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      
      &:hover {
        background: white;
        border-color: var(--database-border-color);
      }
    }

    // 加载状态
    &.loading {
      pointer-events: none;
      
      .dashicons {
        animation: spin 1s linear infinite;
      }
    }
  }
}

// 旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 搜索输入框增强
.database-toolbar .search-input {
  // 搜索图标
  background-image: url('data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.35-4.35"></path></svg>');
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px 16px;
  padding-right: 32px;

  // 清除按钮样式
  &::-webkit-search-cancel-button {
    -webkit-appearance: none;
    appearance: none;
    height: 16px;
    width: 16px;
    background-image: url('data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>');
    background-size: 16px 16px;
    cursor: pointer;
  }
}

// 状态指示器增强
.database-toolbar .status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .loading-spinner {
    display: flex;
    align-items: center;
  }

  .record-count {
    white-space: nowrap;
    color: var(--database-text-color);
    font-weight: 500;
  }
}
