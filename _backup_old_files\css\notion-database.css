/**
 * Notion数据库渲染器样式 - 简化版
 *
 * 专门为新的DatabaseRenderer类设计的简洁样式，
 * 移除了复杂的Grid布局和JavaScript交互，专注于静态展示。
 *
 * @since      1.1.3
 * @version    2.0.0-beta.2
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-<PERSON><PERSON>/Notion-to-WordPress
 */

/* ================ 通用数据库样式 ================ */
.notion-database {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin: 16px 0;
}

.notion-database-title {
    padding: 16px;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.notion-database-empty {
    padding: 32px;
    text-align: center;
    color: #6b7280;
    font-style: italic;
}

.notion-icon {
    display: inline-block;
    margin-right: 8px;
    font-size: 16px;
    vertical-align: middle;
}

.notion-icon-img {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    vertical-align: middle;
}

/* ================ 表格视图样式 ================ */
.notion-database-table {
    max-height: 600px;
    overflow-y: auto;
}

.notion-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.notion-table th,
.notion-table td {
    padding: 12px 16px;
    text-align: left;
    vertical-align: middle;
    border-right: 1px solid #f1f3f4;
}

.notion-table th:last-child,
.notion-table td:last-child {
    border-right: none;
}

.notion-table thead th {
    background: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 10;
}

.notion-table tbody tr {
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.15s ease;
}

.notion-table tbody tr:hover {
    background-color: #f8f9fa;
}

.notion-table tbody tr:last-child {
    border-bottom: none;
}

.notion-table-header-cell {
    font-weight: 600;
    color: #1f2937;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.notion-table-title-cell {
    font-weight: 500;
    color: #1f2937;
    max-width: 200px;
}

.notion-table-cell {
    color: #374151;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-word;
}

/* ================ 画廊视图样式 ================ */
.notion-database-gallery {
    padding: 16px;
}

.notion-gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.notion-gallery-item {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: box-shadow 0.15s ease;
}

.notion-gallery-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.notion-gallery-cover {
    width: 100%;
    height: 160px;
    overflow: hidden;
}

.notion-gallery-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.notion-gallery-content {
    padding: 16px;
}

.notion-gallery-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.notion-gallery-properties {
    margin-top: 12px;
}

.notion-gallery-property {
    margin-bottom: 6px;
    font-size: 14px;
    color: #6b7280;
}

.notion-property-name {
    font-weight: 500;
    color: #374151;
}

/* ================ 看板视图样式 ================ */
.notion-database-board {
    padding: 16px;
}

.notion-board-columns {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    margin-top: 16px;
    padding-bottom: 8px;
}

.notion-board-column {
    min-width: 280px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
}

.notion-board-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.notion-board-header h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.notion-board-count {
    background: #e9ecef;
    color: #6b7280;
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 12px;
}

.notion-board-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.notion-board-item {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    transition: box-shadow 0.15s ease;
}

.notion-board-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.notion-board-cover {
    width: 100%;
    height: 120px;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 8px;
}

.notion-board-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.notion-board-title {
    font-size: 14px;
    font-weight: 500;
    color: #1f2937;
    display: flex;
    align-items: center;
}

/* ================ 属性样式 ================ */
.notion-select {
    background: #e0f2fe;
    color: #0277bd;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.notion-tag {
    background: #f3e5f5;
    color: #7b1fa2;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 4px;
}

/* ================ 响应式设计 ================ */
@media (max-width: 768px) {
    .notion-table th,
    .notion-table td {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    .notion-table-header-cell {
        font-size: 12px;
    }
    
    .notion-gallery-grid {
        grid-template-columns: 1fr;
    }
    
    .notion-board-columns {
        flex-direction: column;
    }
    
    .notion-board-column {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .notion-database {
        margin: 8px 0;
    }
    
    .notion-database-title {
        padding: 12px;
        font-size: 14px;
    }
    
    .notion-gallery-content,
    .notion-board-column {
        padding: 12px;
    }
}