/**
 * 存储工具函数
 */

export interface StorageOptions {
  ttl?: number; // 过期时间（毫秒）
  prefix?: string; // 键名前缀
}

export interface StorageItem<T = any> {
  value: T;
  timestamp: number;
  ttl?: number;
}

/**
 * 本地存储管理器
 */
export class LocalStorageManager {
  private prefix: string;

  constructor(prefix = 'notion_wp_') {
    this.prefix = prefix;
  }

  /**
   * 设置存储项
   */
  set<T = any>(key: string, value: T, options: StorageOptions = {}): void {
    const item: StorageItem<T> = {
      value,
      timestamp: Date.now(),
      ttl: options.ttl,
    };

    const fullKey = this.getFullKey(key, options.prefix);

    try {
      window.localStorage.setItem(fullKey, JSON.stringify(item));
    } catch (error) {
      console.warn('LocalStorage set failed:', error);
    }
  }

  /**
   * 获取存储项
   */
  get<T = any>(key: string, defaultValue?: T, prefix?: string): T | undefined {
    const fullKey = this.getFullKey(key, prefix);

    try {
      const itemStr = window.localStorage.getItem(fullKey);
      if (!itemStr) return defaultValue;

      const item: StorageItem<T> = JSON.parse(itemStr);

      // 检查是否过期
      if (this.isExpired(item)) {
        this.remove(key, prefix);
        return defaultValue;
      }

      return item.value;
    } catch (error) {
      console.warn('LocalStorage get failed:', error);
      return defaultValue;
    }
  }

  /**
   * 移除存储项
   */
  remove(key: string, prefix?: string): void {
    const fullKey = this.getFullKey(key, prefix);
    window.localStorage.removeItem(fullKey);
  }

  /**
   * 清空所有存储项
   */
  clear(prefix?: string): void {
    const targetPrefix = prefix || this.prefix;
    const keys = Object.keys(window.localStorage);

    keys.forEach(key => {
      if (key.startsWith(targetPrefix)) {
        window.localStorage.removeItem(key);
      }
    });
  }

  /**
   * 检查存储项是否存在
   */
  has(key: string, prefix?: string): boolean {
    const fullKey = this.getFullKey(key, prefix);
    const item = window.localStorage.getItem(fullKey);

    if (!item) return false;

    try {
      const parsedItem: StorageItem = JSON.parse(item);
      return !this.isExpired(parsedItem);
    } catch {
      return false;
    }
  }

  /**
   * 获取所有键名
   */
  keys(prefix?: string): string[] {
    const targetPrefix = prefix || this.prefix;
    const keys = Object.keys(window.localStorage);

    return keys
      .filter(key => key.startsWith(targetPrefix))
      .map(key => key.substring(targetPrefix.length));
  }

  /**
   * 获取存储大小（字节）
   */
  getSize(): number {
    let size = 0;
    const storage = window.localStorage;
    for (const key in storage) {
      if (key.startsWith(this.prefix)) {
        size += (storage as any)[key].length + key.length;
      }
    }
    return size;
  }

  /**
   * 清理过期项
   */
  cleanup(): number {
    let cleanedCount = 0;
    const keys = this.keys();

    keys.forEach(key => {
      const item = this.getRawItem(key);
      if (item && this.isExpired(item)) {
        this.remove(key);
        cleanedCount++;
      }
    });

    return cleanedCount;
  }

  private getFullKey(key: string, prefix?: string): string {
    return (prefix || this.prefix) + key;
  }

  private isExpired(item: StorageItem): boolean {
    if (!item.ttl) return false;
    return Date.now() - item.timestamp > item.ttl;
  }

  private getRawItem(key: string): StorageItem | null {
    try {
      const itemStr = window.localStorage.getItem(this.getFullKey(key));
      return itemStr ? JSON.parse(itemStr) : null;
    } catch {
      return null;
    }
  }
}

/**
 * 会话存储管理器
 */
export class SessionStorageManager {
  private prefix: string;

  constructor(prefix = 'notion_wp_session_') {
    this.prefix = prefix;
  }

  set<T = any>(key: string, value: T, options: StorageOptions = {}): void {
    const item: StorageItem<T> = {
      value,
      timestamp: Date.now(),
      ttl: options.ttl,
    };

    const fullKey = this.getFullKey(key, options.prefix);

    try {
      window.sessionStorage.setItem(fullKey, JSON.stringify(item));
    } catch (error) {
      console.warn('SessionStorage set failed:', error);
    }
  }

  get<T = any>(key: string, defaultValue?: T, prefix?: string): T | undefined {
    const fullKey = this.getFullKey(key, prefix);

    try {
      const itemStr = window.sessionStorage.getItem(fullKey);
      if (!itemStr) return defaultValue;

      const item: StorageItem<T> = JSON.parse(itemStr);

      if (this.isExpired(item)) {
        this.remove(key, prefix);
        return defaultValue;
      }

      return item.value;
    } catch (error) {
      console.warn('SessionStorage get failed:', error);
      return defaultValue;
    }
  }

  remove(key: string, prefix?: string): void {
    const fullKey = this.getFullKey(key, prefix);
    window.sessionStorage.removeItem(fullKey);
  }

  clear(prefix?: string): void {
    const targetPrefix = prefix || this.prefix;
    const keys = Object.keys(window.sessionStorage);

    keys.forEach(key => {
      if (key.startsWith(targetPrefix)) {
        window.sessionStorage.removeItem(key);
      }
    });
  }

  private getFullKey(key: string, prefix?: string): string {
    return (prefix || this.prefix) + key;
  }

  private isExpired(item: StorageItem): boolean {
    if (!item.ttl) return false;
    return Date.now() - item.timestamp > item.ttl;
  }
}

/**
 * 内存缓存管理器 - 增强版
 *
 * 支持资源缓存、智能缓存策略、持久化等功能
 * 整合了ResourceOptimizer的智能缓存功能
 */
export class MemoryCache<T = any> {
  private cache = new Map<string, StorageItem<T>>();
  private maxSize: number;
  private persistenceKey?: string;
  private autoCleanupInterval?: NodeJS.Timeout;

  constructor(maxSize = 100, options: {
    persistenceKey?: string;
    autoCleanup?: boolean;
    cleanupInterval?: number;
  } = {}) {
    this.maxSize = maxSize;
    this.persistenceKey = options.persistenceKey;

    // 如果启用持久化，从localStorage恢复缓存
    if (this.persistenceKey) {
      this.loadFromStorage();
    }

    // 如果启用自动清理，设置定时器
    if (options.autoCleanup !== false) {
      const interval = options.cleanupInterval || 60000; // 默认1分钟
      this.autoCleanupInterval = setInterval(() => {
        this.cleanup();
      }, interval);
    }
  }

  set(key: string, value: T, ttl?: number): void {
    // 如果缓存已满，删除最旧的项（LRU策略）
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
      }
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl,
    });

    // 如果启用持久化，保存到存储
    if (this.persistenceKey) {
      this.saveToStorage();
    }
  }

  get(key: string, defaultValue?: T): T | undefined {
    const item = this.cache.get(key);
    if (!item) return defaultValue;

    if (this.isExpired(item)) {
      this.cache.delete(key);
      return defaultValue;
    }

    return item.value;
  }

  has(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;

    if (this.isExpired(item)) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  cleanup(): number {
    let cleanedCount = 0;
    for (const [key, item] of this.cache.entries()) {
      if (this.isExpired(item)) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    // 如果启用持久化且有清理项，保存到存储
    if (cleanedCount > 0 && this.persistenceKey) {
      this.saveToStorage();
    }

    return cleanedCount;
  }

  /**
   * 从localStorage加载缓存
   */
  private loadFromStorage(): void {
    if (!this.persistenceKey) return;

    try {
      const cached = window.localStorage.getItem(this.persistenceKey);
      if (cached) {
        const data = JSON.parse(cached);
        if (data.entries && Array.isArray(data.entries)) {
          this.cache = new Map(data.entries);
          console.log(`📦 [MemoryCache] 从存储恢复 ${data.entries.length} 项缓存`);
        }
      }
    } catch (error) {
      console.warn('📦 [MemoryCache] 从存储加载失败:', error);
    }
  }

  /**
   * 保存缓存到localStorage
   */
  private saveToStorage(): void {
    if (!this.persistenceKey) return;

    try {
      const data = {
        entries: Array.from(this.cache.entries()),
        size: this.cache.size,
        timestamp: Date.now(),
      };

      window.localStorage.setItem(this.persistenceKey, JSON.stringify(data));
    } catch (error) {
      console.warn('📦 [MemoryCache] 保存到存储失败:', error);
    }
  }

  /**
   * 手动触发持久化
   */
  persist(): void {
    if (this.persistenceKey) {
      this.saveToStorage();
    }
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    if (this.autoCleanupInterval) {
      clearInterval(this.autoCleanupInterval);
      this.autoCleanupInterval = undefined;
    }

    // 最后一次持久化
    if (this.persistenceKey) {
      this.saveToStorage();
    }

    this.cache.clear();
  }

  private isExpired(item: StorageItem<T>): boolean {
    if (!item.ttl) return false;
    return Date.now() - item.timestamp > item.ttl;
  }
}

// 默认实例
export const localStorage = new LocalStorageManager();
export const sessionStorage = new SessionStorageManager();
export const memoryCache = new MemoryCache();

// 专用缓存实例
export const resourceCache = new MemoryCache(100, {
  persistenceKey: 'notion-resource-cache',
  autoCleanup: true,
  cleanupInterval: 60000, // 1分钟清理一次
});

export const smartCache = new MemoryCache(200, {
  persistenceKey: 'notion-smart-cache',
  autoCleanup: true,
  cleanupInterval: 300000, // 5分钟清理一次
});
