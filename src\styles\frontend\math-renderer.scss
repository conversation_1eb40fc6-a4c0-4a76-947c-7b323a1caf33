/**
 * 数学公式和图表渲染样式 - SCSS版本
 * 
 * 从原有latex-styles.css和katex-fonts.css迁移，包括：
 * - KaTeX数学公式样式
 * - Mermaid图表样式
 * - 字体定义和回退
 * - 响应式设计
 */

@use "../shared/variables" as *;

// KaTeX字体定义
// KaTeX字体定义 - 使用本地字体文件，CDN作为备用
@font-face {
  font-family: KaTeX_Main;
  font-style: normal;
  font-weight: 400;
  src: url('../../assets/fonts/KaTeX_Main-Regular.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Main-Regular.woff2') format('woff2'),
       local('Times New Roman'), local('serif');
}

@font-face {
  font-family: KaTeX_Math;
  font-style: italic;
  font-weight: 400;
  src: url('../../assets/fonts/KaTeX_Math-Italic.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Math-Italic.woff2') format('woff2'),
       local('Times New Roman'), local('serif');
}

@font-face {
  font-family: KaTeX_Size2;
  font-style: normal;
  font-weight: 400;
  src: url('../../assets/fonts/KaTeX_Size2-Regular.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Size2-Regular.woff2') format('woff2'),
       local('Times New Roman'), local('serif');
}

@font-face {
  font-family: KaTeX_AMS;
  font-style: normal;
  font-weight: 400;
  src: url('../../assets/fonts/KaTeX_AMS-Regular.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_AMS-Regular.woff2') format('woff2'),
       local('serif');
}

@font-face {
  font-family: KaTeX_Size1;
  font-style: normal;
  font-weight: 400;
  src: url('../../assets/fonts/KaTeX_Size1-Regular.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Size1-Regular.woff2') format('woff2'),
       local('serif');
}

@font-face {
  font-family: KaTeX_Size3;
  font-style: normal;
  font-weight: 400;
  src: url('../../assets/fonts/KaTeX_Size3-Regular.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Size3-Regular.woff2') format('woff2'),
       local('serif');
}

@font-face {
  font-family: KaTeX_Size4;
  font-style: normal;
  font-weight: 400;
  src: url('../../assets/fonts/KaTeX_Size4-Regular.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Size4-Regular.woff2') format('woff2'),
       local('serif');
}

@font-face {
  font-family: KaTeX_Main;
  font-style: normal;
  font-weight: 700;
  src: url('../../assets/fonts/KaTeX_Main-Bold.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Main-Bold.woff2') format('woff2'),
       local('serif');
}

@font-face {
  font-family: KaTeX_Main;
  font-style: italic;
  font-weight: 400;
  src: url('../../assets/fonts/KaTeX_Main-Italic.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Main-Italic.woff2') format('woff2'),
       local('serif');
}

@font-face {
  font-family: KaTeX_Caligraphic;
  font-style: normal;
  font-weight: 400;
  src: url('../../assets/fonts/KaTeX_Caligraphic-Regular.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Caligraphic-Regular.woff2') format('woff2'),
       local('serif');
}

@font-face {
  font-family: KaTeX_Script;
  font-style: normal;
  font-weight: 400;
  src: url('../../assets/fonts/KaTeX_Script-Regular.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Script-Regular.woff2') format('woff2'),
       local('serif');
}

@font-face {
  font-family: KaTeX_Typewriter;
  font-style: normal;
  font-weight: 400;
  src: url('../../assets/fonts/KaTeX_Typewriter-Regular.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Typewriter-Regular.woff2') format('woff2'),
       local('monospace');
}

@font-face {
  font-family: KaTeX_Caligraphic;
  font-style: normal;
  font-weight: 700;
  src: url('../../assets/fonts/KaTeX_Caligraphic-Bold.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Caligraphic-Bold.woff2') format('woff2'),
       local('serif');
}

@font-face {
  font-family: KaTeX_Fraktur;
  font-style: normal;
  font-weight: 400;
  src: url('../../assets/fonts/KaTeX_Fraktur-Regular.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Fraktur-Regular.woff2') format('woff2'),
       local('serif');
}

@font-face {
  font-family: KaTeX_Fraktur;
  font-style: normal;
  font-weight: 700;
  src: url('../../assets/fonts/KaTeX_Fraktur-Bold.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Fraktur-Bold.woff2') format('woff2'),
       local('serif');
}

@font-face {
  font-family: KaTeX_Main;
  font-style: italic;
  font-weight: 700;
  src: url('../../assets/fonts/KaTeX_Main-BoldItalic.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Main-BoldItalic.woff2') format('woff2'),
       local('serif');
}

@font-face {
  font-family: KaTeX_Math;
  font-style: italic;
  font-weight: 700;
  src: url('../../assets/fonts/KaTeX_Math-BoldItalic.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_Math-BoldItalic.woff2') format('woff2'),
       local('serif');
}

@font-face {
  font-family: KaTeX_SansSerif;
  font-style: normal;
  font-weight: 400;
  src: url('../../assets/fonts/KaTeX_SansSerif-Regular.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_SansSerif-Regular.woff2') format('woff2'),
       local('sans-serif');
}

@font-face {
  font-family: KaTeX_SansSerif;
  font-style: normal;
  font-weight: 700;
  src: url('../../assets/fonts/KaTeX_SansSerif-Bold.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_SansSerif-Bold.woff2') format('woff2'),
       local('sans-serif');
}

@font-face {
  font-family: KaTeX_SansSerif;
  font-style: italic;
  font-weight: 400;
  src: url('../../assets/fonts/KaTeX_SansSerif-Italic.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/fonts/KaTeX_SansSerif-Italic.woff2') format('woff2'),
       local('sans-serif');
}

// 数学公式样式
.notion-equation {
  &-inline {
    display: inline-block;
    vertical-align: middle;
    margin: 0 0.2em;
    font-size: 1.05em;
  }

  &-block {
    display: block;
    margin: $spacing-3 0;
    text-align: center;
    font-size: 1.1em;
    line-height: 1.4;
    
    // 确保公式在容器中居中
    .katex-display {
      margin: 0;
      text-align: center;
    }
  }
}

// 化学公式特殊样式
.notion-chem-equation {
  display: inline-block;
  vertical-align: middle;
  font-size: 1.05em;
  margin: 0 2px;
}

// Mermaid图表样式
.mermaid {
  display: block;
  margin: $spacing-4 auto;
  padding: $spacing-3;
  background: $bg-primary;
  border: 1px solid $border-color;
  border-radius: $border-radius-base;
  box-shadow: $shadow-sm;
  max-width: 100%;
  overflow: auto;
  text-align: center;
  position: relative;

  // 图表控制按钮
  &-controls {
    position: absolute;
    top: $spacing-2;
    right: $spacing-2;
    display: flex;
    gap: $spacing-1;
    z-index: 10;
  }

  &-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: $border-radius-sm;
    background: rgba($gray-800, 0.8);
    color: $white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: $transition-base;

    &:hover {
      background: rgba($gray-800, 0.9);
      transform: scale(1.05);
    }

    // 工具提示
    &::after {
      content: attr(title);
      position: absolute;
      bottom: -30px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba($gray-900, 0.9);
      color: $white;
      padding: $spacing-1 $spacing-2;
      border-radius: $border-radius-sm;
      font-size: $font-size-xs;
      white-space: nowrap;
      opacity: 0;
      pointer-events: none;
      transition: opacity $transition-fast;
    }

    &:hover::after {
      opacity: 1;
    }
  }

  // SVG样式优化
  svg {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;

    text {
      font-family: $font-family-base;
      font-size: 14px;
    }
  }
}

// 响应式设计
@media (max-width: $breakpoint-md) {
  .notion-equation-block {
    font-size: 1em;
    margin: $spacing-2 0;
  }

  .mermaid {
    margin: $spacing-3 auto;
    padding: $spacing-2;
    font-size: 14px;

    svg text {
      font-size: 12px;
    }

    &-controls {
      top: $spacing-1;
      right: $spacing-1;
    }

    &-btn {
      width: 28px;
      height: 28px;
      font-size: 14px;

      &::after {
        display: none; // 在小屏幕上隐藏工具提示
      }
    }
  }
}

@media (max-width: $breakpoint-sm) {
  .mermaid {
    margin: $spacing-2 auto;
    padding: $spacing-1;
    font-size: 12px;

    svg text {
      font-size: 10px;
    }

    &-btn {
      width: 24px;
      height: 24px;
      font-size: 12px;
    }
  }
}

// KaTeX错误样式
.katex-error {
  color: $error-color;
  background: rgba($error-color, 0.1);
  padding: $spacing-1 $spacing-2;
  border-radius: $border-radius-sm;
  font-family: $font-family-mono;
  font-size: $font-size-sm;
}

// 数学公式加载状态
.notion-equation-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid $border-color;
  border-top: 2px solid $primary-color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}