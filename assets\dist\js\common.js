"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunknotion_to_wordpress"] = self["webpackChunknotion_to_wordpress"] || []).push([["common"],{

/***/ "./src/shared/core/EventBus.ts":
/*!*************************************!*\
  !*** ./src/shared/core/EventBus.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventBusImpl: () => (/* binding */ EventBusImpl),\n/* harmony export */   WordPressHooks: () => (/* binding */ WordPressHooks),\n/* harmony export */   emit: () => (/* binding */ emit),\n/* harmony export */   eventBus: () => (/* binding */ eventBus),\n/* harmony export */   off: () => (/* binding */ off),\n/* harmony export */   on: () => (/* binding */ on),\n/* harmony export */   once: () => (/* binding */ once),\n/* harmony export */   wpHooks: () => (/* binding */ wpHooks)\n/* harmony export */ });\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.symbol.to-primitive.js */ \"./node_modules/core-js/modules/es.symbol.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.symbol.js */ \"./node_modules/core-js/modules/es.symbol.js\");\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */ \"./node_modules/core-js/modules/es.symbol.description.js\");\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.symbol.iterator.js */ \"./node_modules/core-js/modules/es.symbol.iterator.js\");\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ \"./node_modules/core-js/modules/es.array.concat.js\");\n/* harmony import */ var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var core_js_modules_es_array_find_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.array.find-index.js */ \"./node_modules/core-js/modules/es.array.find-index.js\");\n/* harmony import */ var core_js_modules_es_array_find_index_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_find_index_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var core_js_modules_es_array_from_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.array.from.js */ \"./node_modules/core-js/modules/es.array.from.js\");\n/* harmony import */ var core_js_modules_es_array_from_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_from_js__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"./node_modules/core-js/modules/es.array.iterator.js\");\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.array.slice.js */ \"./node_modules/core-js/modules/es.array.slice.js\");\n/* harmony import */ var core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var core_js_modules_es_array_splice_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.array.splice.js */ \"./node_modules/core-js/modules/es.array.splice.js\");\n/* harmony import */ var core_js_modules_es_array_splice_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_splice_js__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.date.to-primitive.js */ \"./node_modules/core-js/modules/es.date.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core-js/modules/es.map.js */ \"./node_modules/core-js/modules/es.map.js\");\n/* harmony import */ var core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.number.constructor.js */ \"./node_modules/core-js/modules/es.number.constructor.js\");\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ \"./node_modules/core-js/modules/es.object.to-string.js\");\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ \"./node_modules/core-js/modules/es.string.iterator.js\");\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core-js/modules/web.dom-collections.for-each.js */ \"./node_modules/core-js/modules/web.dom-collections.for-each.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_16__);\nvar _wp;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 事件总线系统\n */\n\n// 本地类型定义\n\n/**\n * 事件监听器接口\n */\n\n/**\n * 事件总线实现\n */\nvar EventBusImpl = /*#__PURE__*/function () {\n  function EventBusImpl() {\n    _classCallCheck(this, EventBusImpl);\n    _defineProperty(this, \"listeners\", new Map());\n    _defineProperty(this, \"maxListeners\", 100);\n    _defineProperty(this, \"debug\", false);\n  }\n  return _createClass(EventBusImpl, [{\n    key: \"setDebug\",\n    value:\n    /**\n     * 设置调试模式\n     */\n    function setDebug(debug) {\n      this.debug = debug;\n    }\n\n    /**\n     * 设置最大监听器数量\n     */\n  }, {\n    key: \"setMaxListeners\",\n    value: function setMaxListeners(max) {\n      this.maxListeners = max;\n    }\n\n    /**\n     * 添加事件监听器\n     */\n  }, {\n    key: \"on\",\n    value: function on(event, callback) {\n      var priority = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 10;\n      this.addListener(event, callback, false, priority);\n    }\n\n    /**\n     * 添加一次性事件监听器\n     */\n  }, {\n    key: \"once\",\n    value: function once(event, callback) {\n      var priority = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 10;\n      this.addListener(event, callback, true, priority);\n    }\n\n    /**\n     * 移除事件监听器\n     */\n  }, {\n    key: \"off\",\n    value: function off(event, callback) {\n      if (!this.listeners.has(event)) {\n        return;\n      }\n      var listeners = this.listeners.get(event);\n      if (!callback) {\n        // 移除所有监听器\n        this.listeners.delete(event);\n        this.log(\"Removed all listeners for event: \".concat(event));\n        return;\n      }\n\n      // 移除特定监听器\n      var index = listeners.findIndex(function (listener) {\n        return listener.callback === callback;\n      });\n      if (index !== -1) {\n        listeners.splice(index, 1);\n        this.log(\"Removed listener for event: \".concat(event));\n        if (listeners.length === 0) {\n          this.listeners.delete(event);\n        }\n      }\n    }\n\n    /**\n     * 触发事件\n     */\n  }, {\n    key: \"emit\",\n    value: function emit(event) {\n      var _this = this;\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      if (!this.listeners.has(event)) {\n        this.log(\"No listeners for event: \".concat(event));\n        return;\n      }\n      var listeners = this.listeners.get(event).slice(); // 复制数组避免修改原数组\n      var customEvent = {\n        type: event,\n        detail: args[0],\n        timestamp: Date.now()\n      };\n      this.log(\"Emitting event: \".concat(event, \" with \").concat(listeners.length, \" listeners\"));\n      listeners.forEach(function (listener) {\n        try {\n          listener.callback.apply(listener, [customEvent].concat(args));\n\n          // 如果是一次性监听器，移除它\n          if (listener.once) {\n            _this.off(event, listener.callback);\n          }\n        } catch (error) {\n          console.error(\"Error in event listener for \".concat(event, \":\"), error);\n        }\n      });\n    }\n\n    /**\n     * 获取事件的监听器数量\n     */\n  }, {\n    key: \"listenerCount\",\n    value: function listenerCount(event) {\n      var _this$listeners$get;\n      return ((_this$listeners$get = this.listeners.get(event)) === null || _this$listeners$get === void 0 ? void 0 : _this$listeners$get.length) || 0;\n    }\n\n    /**\n     * 获取所有事件名称\n     */\n  }, {\n    key: \"eventNames\",\n    value: function eventNames() {\n      return Array.from(this.listeners.keys());\n    }\n\n    /**\n     * 移除所有监听器\n     */\n  }, {\n    key: \"removeAllListeners\",\n    value: function removeAllListeners(event) {\n      if (event) {\n        this.listeners.delete(event);\n        this.log(\"Removed all listeners for event: \".concat(event));\n      } else {\n        this.listeners.clear();\n        this.log('Removed all listeners for all events');\n      }\n    }\n\n    /**\n     * 检查是否有监听器\n     */\n  }, {\n    key: \"hasListeners\",\n    value: function hasListeners(event) {\n      return this.listeners.has(event) && this.listeners.get(event).length > 0;\n    }\n\n    /**\n     * 添加监听器的内部方法\n     */\n  }, {\n    key: \"addListener\",\n    value: function addListener(event, callback, once, priority) {\n      if (!this.listeners.has(event)) {\n        this.listeners.set(event, []);\n      }\n      var listeners = this.listeners.get(event);\n\n      // 检查最大监听器数量\n      if (listeners.length >= this.maxListeners) {\n        console.warn(\"Maximum listeners (\".concat(this.maxListeners, \") exceeded for event: \").concat(event));\n      }\n\n      // 创建监听器对象\n      var listener = {\n        callback: callback,\n        once: once,\n        priority: priority\n      };\n\n      // 按优先级插入（优先级越小越先执行）\n      var insertIndex = listeners.length;\n      for (var i = 0; i < listeners.length; i++) {\n        if (listeners[i].priority > priority) {\n          insertIndex = i;\n          break;\n        }\n      }\n      listeners.splice(insertIndex, 0, listener);\n      this.log(\"Added \".concat(once ? 'once' : 'on', \" listener for event: \").concat(event, \" (priority: \").concat(priority, \")\"));\n    }\n\n    /**\n     * 调试日志\n     */\n  }, {\n    key: \"log\",\n    value: function log(message) {\n      if (this.debug) {\n        console.log(\"[EventBus] \".concat(message));\n      }\n    }\n  }]);\n}();\n\n/**\n * 全局事件总线实例\n */\nvar eventBus = new EventBusImpl();\n\n/**\n * 便捷的全局函数\n */\nvar on = eventBus.on.bind(eventBus);\nvar once = eventBus.once.bind(eventBus);\nvar off = eventBus.off.bind(eventBus);\nvar emit = eventBus.emit.bind(eventBus);\n\n/**\n * WordPress钩子系统集成\n */\nvar WordPressHooks = /*#__PURE__*/function () {\n  function WordPressHooks(eventBus) {\n    _classCallCheck(this, WordPressHooks);\n    _defineProperty(this, \"eventBus\", void 0);\n    this.eventBus = eventBus;\n  }\n\n  /**\n   * 添加WordPress动作钩子\n   */\n  return _createClass(WordPressHooks, [{\n    key: \"addAction\",\n    value: function addAction(tag, callback) {\n      var priority = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 10;\n      this.eventBus.on(\"action:\".concat(tag), callback, priority);\n    }\n\n    /**\n     * 执行WordPress动作钩子\n     */\n  }, {\n    key: \"doAction\",\n    value: function doAction(tag) {\n      var _this$eventBus;\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n      (_this$eventBus = this.eventBus).emit.apply(_this$eventBus, [\"action:\".concat(tag)].concat(args));\n    }\n\n    /**\n     * 添加WordPress过滤器钩子\n     */\n  }, {\n    key: \"addFilter\",\n    value: function addFilter(tag, callback) {\n      var priority = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 10;\n      this.eventBus.on(\"filter:\".concat(tag), function (event, value) {\n        for (var _len3 = arguments.length, args = new Array(_len3 > 2 ? _len3 - 2 : 0), _key3 = 2; _key3 < _len3; _key3++) {\n          args[_key3 - 2] = arguments[_key3];\n        }\n        var result = callback.apply(void 0, [value].concat(args));\n        // 将结果存储在事件对象中\n        event.result = result;\n      }, priority);\n    }\n\n    /**\n     * 应用WordPress过滤器钩子\n     */\n  }, {\n    key: \"applyFilters\",\n    value: function applyFilters(tag, value) {\n      var _this$eventBus2;\n      var event = {\n        type: \"filter:\".concat(tag),\n        detail: value,\n        timestamp: Date.now(),\n        result: value\n      };\n      for (var _len4 = arguments.length, args = new Array(_len4 > 2 ? _len4 - 2 : 0), _key4 = 2; _key4 < _len4; _key4++) {\n        args[_key4 - 2] = arguments[_key4];\n      }\n      (_this$eventBus2 = this.eventBus).emit.apply(_this$eventBus2, [\"filter:\".concat(tag), event, value].concat(args));\n      return event.result;\n    }\n  }]);\n}();\n\n/**\n * WordPress钩子实例\n */\nvar wpHooks = new WordPressHooks(eventBus);\n\n// 如果WordPress钩子系统存在，创建兼容层而不是直接修改\nif (typeof window !== 'undefined' && (_wp = window.wp) !== null && _wp !== void 0 && _wp.hooks) {\n  // 创建一个兼容层，不直接修改WordPress的hooks对象\n  console.log('🔗 [WordPress钩子] 检测到WordPress钩子系统，创建兼容层');\n\n  // 将WordPress钩子系统的功能映射到我们的事件系统\n  // 但不直接修改WordPress的hooks对象，避免权限错误\n\n  // 可以通过监听我们的事件来与WordPress钩子系统交互\n  eventBus.on('wp:addAction', function (_event, tag, callback) {\n    var _wp2;\n    var priority = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 10;\n    if ((_wp2 = window.wp) !== null && _wp2 !== void 0 && (_wp2 = _wp2.hooks) !== null && _wp2 !== void 0 && _wp2.addAction) {\n      window.wp.hooks.addAction(tag, callback, priority);\n    }\n  });\n  eventBus.on('wp:doAction', function (_event, tag) {\n    var _wp3;\n    if ((_wp3 = window.wp) !== null && _wp3 !== void 0 && (_wp3 = _wp3.hooks) !== null && _wp3 !== void 0 && _wp3.doAction) {\n      var _wp$hooks;\n      for (var _len5 = arguments.length, args = new Array(_len5 > 2 ? _len5 - 2 : 0), _key5 = 2; _key5 < _len5; _key5++) {\n        args[_key5 - 2] = arguments[_key5];\n      }\n      (_wp$hooks = window.wp.hooks).doAction.apply(_wp$hooks, [tag].concat(args));\n    }\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/shared/core/EventBus.ts\n\n}");

/***/ }),

/***/ "./src/shared/utils/dom.ts":
/*!*********************************!*\
  !*** ./src/shared/utils/dom.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addClass: () => (/* binding */ addClass),\n/* harmony export */   addEventListener: () => (/* binding */ addEventListener),\n/* harmony export */   createElement: () => (/* binding */ createElement),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   delegate: () => (/* binding */ delegate),\n/* harmony export */   getAttribute: () => (/* binding */ getAttribute),\n/* harmony export */   getComputedStyle: () => (/* binding */ getComputedStyle),\n/* harmony export */   getOffset: () => (/* binding */ getOffset),\n/* harmony export */   getSize: () => (/* binding */ getSize),\n/* harmony export */   hasClass: () => (/* binding */ hasClass),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   isInViewport: () => (/* binding */ isInViewport),\n/* harmony export */   querySelector: () => (/* binding */ querySelector),\n/* harmony export */   querySelectorAll: () => (/* binding */ querySelectorAll),\n/* harmony export */   ready: () => (/* binding */ ready),\n/* harmony export */   removeAttribute: () => (/* binding */ removeAttribute),\n/* harmony export */   removeClass: () => (/* binding */ removeClass),\n/* harmony export */   removeEventListener: () => (/* binding */ removeEventListener),\n/* harmony export */   scrollToElement: () => (/* binding */ scrollToElement),\n/* harmony export */   setAttribute: () => (/* binding */ setAttribute),\n/* harmony export */   setStyle: () => (/* binding */ setStyle),\n/* harmony export */   show: () => (/* binding */ show),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   toggle: () => (/* binding */ toggle),\n/* harmony export */   toggleClass: () => (/* binding */ toggleClass)\n/* harmony export */ });\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.symbol.js */ \"./node_modules/core-js/modules/es.symbol.js\");\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */ \"./node_modules/core-js/modules/es.symbol.description.js\");\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.symbol.iterator.js */ \"./node_modules/core-js/modules/es.symbol.iterator.js\");\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var core_js_modules_es_array_from_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.array.from.js */ \"./node_modules/core-js/modules/es.array.from.js\");\n/* harmony import */ var core_js_modules_es_array_from_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_from_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"./node_modules/core-js/modules/es.array.iterator.js\");\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.array.slice.js */ \"./node_modules/core-js/modules/es.array.slice.js\");\n/* harmony import */ var core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ \"./node_modules/core-js/modules/es.function.name.js\");\n/* harmony import */ var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var core_js_modules_es_object_assign_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.object.assign.js */ \"./node_modules/core-js/modules/es.object.assign.js\");\n/* harmony import */ var core_js_modules_es_object_assign_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_assign_js__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var core_js_modules_es_object_entries_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.object.entries.js */ \"./node_modules/core-js/modules/es.object.entries.js\");\n/* harmony import */ var core_js_modules_es_object_entries_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_entries_js__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ \"./node_modules/core-js/modules/es.object.to-string.js\");\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.regexp.exec.js */ \"./node_modules/core-js/modules/es.regexp.exec.js\");\n/* harmony import */ var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core-js/modules/es.regexp.to-string.js */ \"./node_modules/core-js/modules/es.regexp.to-string.js\");\n/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ \"./node_modules/core-js/modules/es.string.iterator.js\");\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! core-js/modules/web.dom-collections.for-each.js */ \"./node_modules/core-js/modules/web.dom-collections.for-each.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_14__);\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * DOM操作工具函数\n */\n\n/**\n * 查询单个元素\n */\nfunction querySelector(selector) {\n  var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : document;\n  return context.querySelector(selector);\n}\n\n/**\n * 查询多个元素\n */\nfunction querySelectorAll(selector) {\n  var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : document;\n  return context.querySelectorAll(selector);\n}\n\n/**\n * 创建元素\n */\nfunction createElement(tagName, attributes, textContent) {\n  var element = document.createElement(tagName);\n  if (attributes) {\n    Object.entries(attributes).forEach(function (_ref) {\n      var _ref2 = _slicedToArray(_ref, 2),\n        key = _ref2[0],\n        value = _ref2[1];\n      element.setAttribute(key, value);\n    });\n  }\n  if (textContent) {\n    element.textContent = textContent;\n  }\n  return element;\n}\n\n/**\n * 添加CSS类\n */\nfunction addClass(element) {\n  var _element$classList;\n  for (var _len = arguments.length, classNames = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    classNames[_key - 1] = arguments[_key];\n  }\n  (_element$classList = element.classList).add.apply(_element$classList, classNames);\n}\n\n/**\n * 移除CSS类\n */\nfunction removeClass(element) {\n  var _element$classList2;\n  for (var _len2 = arguments.length, classNames = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    classNames[_key2 - 1] = arguments[_key2];\n  }\n  (_element$classList2 = element.classList).remove.apply(_element$classList2, classNames);\n}\n\n/**\n * 切换CSS类\n */\nfunction toggleClass(element, className, force) {\n  return element.classList.toggle(className, force);\n}\n\n/**\n * 检查是否包含CSS类\n */\nfunction hasClass(element, className) {\n  return element.classList.contains(className);\n}\n\n/**\n * 设置元素属性\n */\nfunction setAttribute(element, name, value) {\n  element.setAttribute(name, value);\n}\n\n/**\n * 获取元素属性\n */\nfunction getAttribute(element, name) {\n  return element.getAttribute(name);\n}\n\n/**\n * 移除元素属性\n */\nfunction removeAttribute(element, name) {\n  element.removeAttribute(name);\n}\n\n/**\n * 设置元素样式\n */\nfunction setStyle(element, styles) {\n  Object.assign(element.style, styles);\n}\n\n/**\n * 获取计算样式\n */\nfunction getComputedStyle(element, property) {\n  var computed = window.getComputedStyle(element);\n  return property ? computed.getPropertyValue(property) : computed;\n}\n\n/**\n * 显示元素\n */\nfunction show(element) {\n  var display = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'block';\n  element.style.display = display;\n}\n\n/**\n * 隐藏元素\n */\nfunction hide(element) {\n  element.style.display = 'none';\n}\n\n/**\n * 切换元素显示状态\n */\nfunction toggle(element) {\n  var display = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'block';\n  if (element.style.display === 'none') {\n    show(element, display);\n  } else {\n    hide(element);\n  }\n}\n\n/**\n * 添加事件监听器\n */\nfunction addEventListener(element, type, listener, options) {\n  element.addEventListener(type, listener, options);\n}\n\n/**\n * 移除事件监听器\n */\nfunction removeEventListener(element, type, listener, options) {\n  element.removeEventListener(type, listener, options);\n}\n\n/**\n * 委托事件监听\n */\nfunction delegate(container, selector, eventType, callback) {\n  addEventListener(container, eventType, function (event) {\n    var target = event.target;\n    var delegateTarget = target.closest(selector);\n    if (delegateTarget && container.contains(delegateTarget)) {\n      callback.call(delegateTarget, event);\n    }\n  });\n}\n\n/**\n * 获取元素位置信息\n */\nfunction getOffset(element) {\n  var rect = element.getBoundingClientRect();\n  return {\n    top: rect.top + window.pageYOffset,\n    left: rect.left + window.pageXOffset\n  };\n}\n\n/**\n * 获取元素尺寸信息\n */\nfunction getSize(element) {\n  var rect = element.getBoundingClientRect();\n  return {\n    width: rect.width,\n    height: rect.height\n  };\n}\n\n/**\n * 滚动到元素\n */\nfunction scrollToElement(element) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    behavior: 'smooth',\n    block: 'start'\n  };\n  element.scrollIntoView(options);\n}\n\n/**\n * 检查元素是否在视口中\n */\nfunction isInViewport(element) {\n  var rect = element.getBoundingClientRect();\n  return rect.top >= 0 && rect.left >= 0 && rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) && rect.right <= (window.innerWidth || document.documentElement.clientWidth);\n}\n\n/**\n * 等待DOM准备就绪\n */\nfunction ready(callback) {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback);\n  } else {\n    // 使用setTimeout确保在当前执行栈完成后执行\n    setTimeout(callback, 0);\n  }\n}\n\n/**\n * 防抖函数\n */\nfunction debounce(func, wait) {\n  var immediate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var timeout = null;\n  return function executedFunction() {\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    var later = function later() {\n      timeout = null;\n      if (!immediate) func.apply(void 0, args);\n    };\n    var callNow = immediate && !timeout;\n    if (timeout) clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n    if (callNow) func.apply(void 0, args);\n  };\n}\n\n/**\n * 节流函数\n */\nfunction throttle(func, limit) {\n  var inThrottle;\n  return function executedFunction() {\n    if (!inThrottle) {\n      func.apply(void 0, arguments);\n      inThrottle = true;\n      setTimeout(function () {\n        return inThrottle = false;\n      }, limit);\n    }\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/shared/utils/dom.ts\n\n}");

/***/ })

}]);