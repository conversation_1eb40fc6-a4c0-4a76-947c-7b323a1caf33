<?php
declare(strict_types=1);

namespace NTWP\Infrastructure\Cache;

/**
 * 统一缓存管理器
 *
 * 提供多层缓存策略和统一的缓存接口，
 * 整合内存缓存、持久化缓存和会话缓存。
 *
 * 缓存层级：
 * 1. 内存缓存（最快）- 进程内缓存
 * 2. 会话缓存（中等）- WordPress对象缓存
 * 3. 持久化缓存（最慢）- 数据库/文件缓存
 *
 * 缓存策略：
 * - 智能缓存键生成
 * - 自动过期管理
 * - 缓存预热和失效
 * - 性能监控和统计
 *
 * @since      2.0.0-beta.2
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-<PERSON>ong/Notion-to-WordPress
 */

// 如果直接访问此文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

class UnifiedCacheManager {

    /**
     * 缓存层级常量
     */
    const LAYER_MEMORY = 'memory';
    const LAYER_SESSION = 'session';
    const LAYER_PERSISTENT = 'persistent';

    /**
     * 缓存类型常量
     */
    const TYPE_API_RESPONSE = 'api_response';
    const TYPE_PAGE_CONTENT = 'page_content';
    const TYPE_DATABASE_QUERY = 'database_query';
    const TYPE_USER_DATA = 'user_data';
    const TYPE_CONFIGURATION = 'configuration';

    /**
     * 内存缓存存储
     * 
     * @since 2.0.0-beta.2
     */
    private static array $memory_cache = [];

    /**
     * 缓存配置
     * 
     * @since 2.0.0-beta.2
     */
    private static array $cache_config = [
        self::TYPE_API_RESPONSE => [
            'default_ttl' => 300,
            'layers' => [self::LAYER_MEMORY, self::LAYER_SESSION],
            'max_size' => 1000
        ],
        self::TYPE_PAGE_CONTENT => [
            'default_ttl' => 600,
            'layers' => [self::LAYER_MEMORY, self::LAYER_SESSION, self::LAYER_PERSISTENT],
            'max_size' => 500
        ],
        self::TYPE_DATABASE_QUERY => [
            'default_ttl' => 180,
            'layers' => [self::LAYER_MEMORY, self::LAYER_SESSION],
            'max_size' => 200
        ],
        self::TYPE_USER_DATA => [
            'default_ttl' => 3600,
            'layers' => [self::LAYER_SESSION, self::LAYER_PERSISTENT],
            'max_size' => 100
        ],
        self::TYPE_CONFIGURATION => [
            'default_ttl' => 1800,
            'layers' => [self::LAYER_MEMORY, self::LAYER_PERSISTENT],
            'max_size' => 50
        ]
    ];

    /**
     * 缓存统计
     * 
     * @since 2.0.0-beta.2
     */
    private static array $stats = [
        'hits' => 0,
        'misses' => 0,
        'sets' => 0,
        'deletes' => 0,
        'layer_hits' => [
            self::LAYER_MEMORY => 0,
            self::LAYER_SESSION => 0,
            self::LAYER_PERSISTENT => 0
        ]
    ];

    /**
     * 获取缓存值
     *
     * @since 2.0.0-beta.2
     * @param string $key 缓存键
     * @param string $type 缓存类型
     * @param mixed $default 默认值
     * @return mixed 缓存值
     */
    public static function get(string $key, string $type = self::TYPE_API_RESPONSE, $default = null) {
        $cache_key = self::generateCacheKey($key, $type);
        $config = self::getCacheConfig($type);

        // 按层级顺序查找缓存
        foreach ($config['layers'] as $layer) {
            $value = self::getFromLayer($cache_key, $layer);
            
            if ($value !== null) {
                self::$stats['hits']++;
                self::$stats['layer_hits'][$layer]++;
                
                // 将值写入更快的缓存层（缓存提升）
                self::promoteToFasterLayers($cache_key, $value, $layer, $config);
                
                return $value;
            }
        }

        self::$stats['misses']++;
        return $default;
    }

    /**
     * 设置缓存值
     *
     * @since 2.0.0-beta.2
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param string $type 缓存类型
     * @param int|null $ttl 过期时间（秒），null使用默认值
     * @return bool 是否设置成功
     */
    public static function set(string $key, $value, string $type = self::TYPE_API_RESPONSE, ?int $ttl = null): bool {
        $cache_key = self::generateCacheKey($key, $type);
        $config = self::getCacheConfig($type);
        $ttl = $ttl ?? $config['default_ttl'];

        $success = true;

        // 写入所有配置的缓存层
        foreach ($config['layers'] as $layer) {
            if (!self::setToLayer($cache_key, $value, $layer, $ttl)) {
                $success = false;
            }
        }

        if ($success) {
            self::$stats['sets']++;
            
            // 检查缓存大小限制
            self::enforceMaxSize($type, $config);
        }

        return $success;
    }

    /**
     * 删除缓存值
     *
     * @since 2.0.0-beta.2
     * @param string $key 缓存键
     * @param string $type 缓存类型
     * @return bool 是否删除成功
     */
    public static function delete(string $key, string $type = self::TYPE_API_RESPONSE): bool {
        $cache_key = self::generateCacheKey($key, $type);
        $config = self::getCacheConfig($type);

        $success = true;

        // 从所有缓存层删除
        foreach ($config['layers'] as $layer) {
            if (!self::deleteFromLayer($cache_key, $layer)) {
                $success = false;
            }
        }

        if ($success) {
            self::$stats['deletes']++;
        }

        return $success;
    }

    /**
     * 清除指定类型的所有缓存
     *
     * @since 2.0.0-beta.2
     * @param string $type 缓存类型
     * @return bool 是否清除成功
     */
    public static function flush(string $type): bool {
        $prefix = self::generateCacheKey('', $type);
        $config = self::getCacheConfig($type);

        $success = true;

        foreach ($config['layers'] as $layer) {
            if (!self::flushLayer($prefix, $layer)) {
                $success = false;
            }
        }

        return $success;
    }

    /**
     * 清除所有缓存
     *
     * @since 2.0.0-beta.2
     * @return bool 是否清除成功
     */
    public static function flushAll(): bool {
        $success = true;

        // 清除内存缓存
        self::$memory_cache = [];

        // 清除WordPress对象缓存
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        // 清除持久化缓存
        $success = self::flushPersistentCache();

        // 重置统计
        self::resetStats();

        return $success;
    }

    /**
     * 生成缓存键
     *
     * @since 2.0.0-beta.2
     * @param string $key 原始键
     * @param string $type 缓存类型
     * @return string 生成的缓存键
     */
    private static function generateCacheKey(string $key, string $type): string {
        return 'ntwp_' . $type . '_' . md5($key);
    }

    /**
     * 获取缓存配置
     *
     * @since 2.0.0-beta.2
     * @param string $type 缓存类型
     * @return array 缓存配置
     */
    private static function getCacheConfig(string $type): array {
        return self::$cache_config[$type] ?? self::$cache_config[self::TYPE_API_RESPONSE];
    }

    /**
     * 从指定层获取缓存
     *
     * @since 2.0.0-beta.2
     * @param string $cache_key 缓存键
     * @param string $layer 缓存层
     * @return mixed 缓存值或null
     */
    private static function getFromLayer(string $cache_key, string $layer) {
        switch ($layer) {
            case self::LAYER_MEMORY:
                return self::$memory_cache[$cache_key] ?? null;

            case self::LAYER_SESSION:
                if (function_exists('wp_cache_get')) {
                    return wp_cache_get($cache_key, 'ntwp_cache');
                }
                return null;

            case self::LAYER_PERSISTENT:
                return get_transient($cache_key);

            default:
                return null;
        }
    }

    /**
     * 设置缓存到指定层
     *
     * @since 2.0.0-beta.2
     * @param string $cache_key 缓存键
     * @param mixed $value 缓存值
     * @param string $layer 缓存层
     * @param int $ttl 过期时间
     * @return bool 是否设置成功
     */
    private static function setToLayer(string $cache_key, $value, string $layer, int $ttl): bool {
        switch ($layer) {
            case self::LAYER_MEMORY:
                self::$memory_cache[$cache_key] = $value;
                return true;

            case self::LAYER_SESSION:
                if (function_exists('wp_cache_set')) {
                    return wp_cache_set($cache_key, $value, 'ntwp_cache', $ttl);
                }
                return false;

            case self::LAYER_PERSISTENT:
                return set_transient($cache_key, $value, $ttl);

            default:
                return false;
        }
    }

    /**
     * 从指定层删除缓存
     *
     * @since 2.0.0-beta.2
     * @param string $cache_key 缓存键
     * @param string $layer 缓存层
     * @return bool 是否删除成功
     */
    private static function deleteFromLayer(string $cache_key, string $layer): bool {
        switch ($layer) {
            case self::LAYER_MEMORY:
                unset(self::$memory_cache[$cache_key]);
                return true;

            case self::LAYER_SESSION:
                if (function_exists('wp_cache_delete')) {
                    return wp_cache_delete($cache_key, 'ntwp_cache');
                }
                return false;

            case self::LAYER_PERSISTENT:
                return delete_transient($cache_key);

            default:
                return false;
        }
    }

    /**
     * 将缓存提升到更快的层级
     *
     * @since 2.0.0-beta.2
     * @param string $cache_key 缓存键
     * @param mixed $value 缓存值
     * @param string $current_layer 当前层级
     * @param array $config 缓存配置
     */
    private static function promoteToFasterLayers(string $cache_key, $value, string $current_layer, array $config): void {
        $layer_priority = [
            self::LAYER_MEMORY => 0,
            self::LAYER_SESSION => 1,
            self::LAYER_PERSISTENT => 2
        ];

        $current_priority = $layer_priority[$current_layer];

        foreach ($config['layers'] as $layer) {
            if ($layer_priority[$layer] < $current_priority) {
                self::setToLayer($cache_key, $value, $layer, $config['default_ttl']);
            }
        }
    }

    /**
     * 强制执行最大缓存大小限制
     *
     * @since 2.0.0-beta.2
     * @param string $type 缓存类型
     * @param array $config 缓存配置
     */
    private static function enforceMaxSize(string $type, array $config): void {
        if (in_array(self::LAYER_MEMORY, $config['layers'])) {
            $prefix = self::generateCacheKey('', $type);
            $matching_keys = array_filter(
                array_keys(self::$memory_cache),
                function($key) use ($prefix) {
                    return strpos($key, $prefix) === 0;
                }
            );

            if (count($matching_keys) > $config['max_size']) {
                // 删除最旧的缓存项（简单LRU）
                $to_remove = array_slice($matching_keys, 0, count($matching_keys) - $config['max_size']);
                foreach ($to_remove as $key) {
                    unset(self::$memory_cache[$key]);
                }
            }
        }
    }

    /**
     * 清除持久化缓存
     *
     * @since 2.0.0-beta.2
     * @return bool 是否清除成功
     */
    private static function flushPersistentCache(): bool {
        global $wpdb;

        // 删除所有ntwp_开头的transient
        $result = $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
                '_transient_ntwp_%',
                '_transient_timeout_ntwp_%'
            )
        );

        return $result !== false;
    }

    /**
     * 清除指定层的缓存
     *
     * @since 2.0.0-beta.2
     * @param string $prefix 缓存键前缀
     * @param string $layer 缓存层
     * @return bool 是否清除成功
     */
    private static function flushLayer(string $prefix, string $layer): bool {
        switch ($layer) {
            case self::LAYER_MEMORY:
                foreach (array_keys(self::$memory_cache) as $key) {
                    if (strpos($key, $prefix) === 0) {
                        unset(self::$memory_cache[$key]);
                    }
                }
                return true;

            case self::LAYER_SESSION:
                if (function_exists('wp_cache_flush_group')) {
                    return wp_cache_flush_group('ntwp_cache');
                }
                return false;

            case self::LAYER_PERSISTENT:
                return self::flushPersistentCache();

            default:
                return false;
        }
    }

    /**
     * 获取缓存统计
     *
     * @since 2.0.0-beta.2
     * @return array 统计数据
     */
    public static function getStats(): array {
        $total_requests = self::$stats['hits'] + self::$stats['misses'];
        $hit_rate = $total_requests > 0 ? round((self::$stats['hits'] / $total_requests) * 100, 2) : 0;

        return array_merge(self::$stats, [
            'hit_rate' => $hit_rate,
            'total_requests' => $total_requests,
            'memory_cache_size' => count(self::$memory_cache)
        ]);
    }

    /**
     * 重置统计数据
     *
     * @since 2.0.0-beta.2
     */
    public static function resetStats(): void {
        self::$stats = [
            'hits' => 0,
            'misses' => 0,
            'sets' => 0,
            'deletes' => 0,
            'layer_hits' => [
                self::LAYER_MEMORY => 0,
                self::LAYER_SESSION => 0,
                self::LAYER_PERSISTENT => 0
            ]
        ];
    }
}
