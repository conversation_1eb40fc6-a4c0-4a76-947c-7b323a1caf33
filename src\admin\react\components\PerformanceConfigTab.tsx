/**
 * 性能配置标签页组件
 */

import { useState } from 'react';
import { useSettings, useAppActions } from '../context/AppContext';
import { useWordPressAjax, useI18n } from '../hooks/useWordPress';
import { Select, Checkbox, FormRow } from './Input';
import { Button } from './Button';

export function PerformanceConfigTab() {
  const settings = useSettings();
  const { updateSettings, showToast } = useAppActions();
  const { request } = useWordPressAjax();
  const { __ } = useI18n();

  const [isTestingPerformance, setIsTestingPerformance] = useState(false);

  // 处理设置更新
  const handleSettingChange = (key: string, value: string | boolean | number) => {
    updateSettings({ [key]: value });
  };

  // 性能测试
  const handlePerformanceTest = async () => {
    setIsTestingPerformance(true);
    try {
      const response = await request('notion_to_wordpress_performance_test');
      
      if (response.success) {
        showToast({
          type: 'success',
          title: '性能测试完成',
          message: `平均响应时间: ${response.data.avg_response_time}ms`,
        });
      } else {
        showToast({
          type: 'error',
          message: response.message || '性能测试失败',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: '性能测试失败：' + (error instanceof Error ? error.message : '未知错误'),
      });
    } finally {
      setIsTestingPerformance(false);
    }
  };

  // 重置为推荐设置
  const handleResetToRecommended = () => {
    const recommendedSettings = {
      api_page_size: 50,
      concurrent_requests: 3,
      request_timeout: 30,
      retry_attempts: 3,
      cache_enabled: true,
      cache_duration: 3600,
      batch_processing: true,
      memory_limit_check: true,
    };

    Object.entries(recommendedSettings).forEach(([key, value]) => {
      updateSettings({ [key]: value });
    });

    showToast({
      type: 'success',
      message: '已重置为推荐的性能设置',
    });
  };

  // 配置选项
  const pageSizeOptions = [
    { value: '10', label: '10 (保守)' },
    { value: '25', label: '25 (平衡)' },
    { value: '50', label: '50 (推荐)' },
    { value: '100', label: '100 (激进)' },
  ];

  const concurrentOptions = [
    { value: '1', label: '1 (最安全)' },
    { value: '2', label: '2 (保守)' },
    { value: '3', label: '3 (推荐)' },
    { value: '5', label: '5 (激进)' },
    { value: '10', label: '10 (高性能)' },
  ];

  const timeoutOptions = [
    { value: '15', label: '15秒 (快速)' },
    { value: '30', label: '30秒 (推荐)' },
    { value: '60', label: '60秒 (保守)' },
    { value: '120', label: '120秒 (极保守)' },
  ];

  const retryOptions = [
    { value: '0', label: '0 (不重试)' },
    { value: '1', label: '1次' },
    { value: '3', label: '3次 (推荐)' },
    { value: '5', label: '5次' },
  ];

  const cacheDurationOptions = [
    { value: '300', label: '5分钟' },
    { value: '1800', label: '30分钟' },
    { value: '3600', label: '1小时 (推荐)' },
    { value: '7200', label: '2小时' },
    { value: '86400', label: '24小时' },
  ];

  return (
    <div className="notion-wp-settings-section">
      <h2>{__('⚡ 性能配置', '⚡ 性能配置')}</h2>
      <p className="description">
        {__('调整同步性能参数以适应您的服务器配置。建议先使用默认设置，然后根据实际情况进行调整。', '调整同步性能参数以适应您的服务器配置。建议先使用默认设置，然后根据实际情况进行调整。')}
      </p>

      {/* API请求配置 */}
      <div className="notion-wp-settings-section">
        <h3>{__('🔗 API请求配置', '🔗 API请求配置')}</h3>
        
        <table className="form-table">
          <tbody>
            <FormRow
              label={__('API分页大小', 'API分页大小')}
              description={__('每次从Notion API获取的页面数量。较大的值可以减少请求次数，但可能增加内存使用。', '每次从Notion API获取的页面数量。较大的值可以减少请求次数，但可能增加内存使用。')}
            >
              <Select
                value={String(settings.api_page_size || 50)}
                options={pageSizeOptions}
                onChange={(value) => handleSettingChange('api_page_size', parseInt(value))}
              />
            </FormRow>

            <FormRow
              label={__('并发请求数', '并发请求数')}
              description={__('同时进行的API请求数量。增加并发可以提高速度，但可能触发API限制。', '同时进行的API请求数量。增加并发可以提高速度，但可能触发API限制。')}
            >
              <Select
                value={String(settings.concurrent_requests || 3)}
                options={concurrentOptions}
                onChange={(value) => handleSettingChange('concurrent_requests', parseInt(value))}
              />
            </FormRow>

            <FormRow
              label={__('请求超时时间', '请求超时时间')}
              description={__('单个API请求的最大等待时间。网络较慢时建议增加此值。', '单个API请求的最大等待时间。网络较慢时建议增加此值。')}
            >
              <Select
                value={String(settings.request_timeout || 30)}
                options={timeoutOptions}
                onChange={(value) => handleSettingChange('request_timeout', parseInt(value))}
              />
            </FormRow>

            <FormRow
              label={__('重试次数', '重试次数')}
              description={__('请求失败时的重试次数。适当的重试可以提高成功率。', '请求失败时的重试次数。适当的重试可以提高成功率。')}
            >
              <Select
                value={String(settings.retry_attempts || 3)}
                options={retryOptions}
                onChange={(value) => handleSettingChange('retry_attempts', parseInt(value))}
              />
            </FormRow>
          </tbody>
        </table>
      </div>

      {/* 缓存配置 */}
      <div className="notion-wp-settings-section">
        <h3>{__('💾 缓存配置', '💾 缓存配置')}</h3>
        
        <table className="form-table">
          <tbody>
            <FormRow
              label={__('启用缓存', '启用缓存')}
              description={__('缓存API响应以减少重复请求，提高同步速度。', '缓存API响应以减少重复请求，提高同步速度。')}
            >
              <Checkbox
                checked={settings.cache_enabled !== false}
                onChange={(checked) => handleSettingChange('cache_enabled', checked)}
              />
            </FormRow>

            {settings.cache_enabled !== false && (
              <FormRow
                label={__('缓存持续时间', '缓存持续时间')}
                description={__('缓存数据的有效期。较长的缓存时间可以提高性能，但可能导致数据不够实时。', '缓存数据的有效期。较长的缓存时间可以提高性能，但可能导致数据不够实时。')}
              >
                <Select
                  value={String(settings.cache_duration || 3600)}
                  options={cacheDurationOptions}
                  onChange={(value) => handleSettingChange('cache_duration', parseInt(value))}
                />
              </FormRow>
            )}
          </tbody>
        </table>
      </div>

      {/* 处理优化 */}
      <div className="notion-wp-settings-section">
        <h3>{__('🚀 处理优化', '🚀 处理优化')}</h3>
        
        <table className="form-table">
          <tbody>
            <FormRow
              label={__('批量处理', '批量处理')}
              description={__('将多个操作合并为批量处理，提高数据库操作效率。', '将多个操作合并为批量处理，提高数据库操作效率。')}
            >
              <Checkbox
                checked={settings.batch_processing !== false}
                onChange={(checked) => handleSettingChange('batch_processing', checked)}
              />
            </FormRow>

            <FormRow
              label={__('内存限制检查', '内存限制检查')}
              description={__('监控内存使用情况，防止内存溢出。建议在共享主机上启用。', '监控内存使用情况，防止内存溢出。建议在共享主机上启用。')}
            >
              <Checkbox
                checked={settings.memory_limit_check !== false}
                onChange={(checked) => handleSettingChange('memory_limit_check', checked)}
              />
            </FormRow>

            <FormRow
              label={__('智能限流', '智能限流')}
              description={__('根据API响应时间自动调整请求频率，避免触发限制。', '根据API响应时间自动调整请求频率，避免触发限制。')}
            >
              <Checkbox
                checked={settings.smart_throttling !== false}
                onChange={(checked) => handleSettingChange('smart_throttling', checked)}
              />
            </FormRow>

            <FormRow
              label={__('增量同步优化', '增量同步优化')}
              description={__('优化增量同步算法，只处理真正有变化的内容。', '优化增量同步算法，只处理真正有变化的内容。')}
            >
              <Checkbox
                checked={settings.incremental_optimization !== false}
                onChange={(checked) => handleSettingChange('incremental_optimization', checked)}
              />
            </FormRow>
          </tbody>
        </table>
      </div>

      {/* 操作按钮 */}
      <div className="notion-wp-button-row">
        <Button
          variant="primary"
          loading={isTestingPerformance}
          onClick={handlePerformanceTest}
        >
          <span className="dashicons dashicons-performance" style={{ marginRight: '4px' }} />
          {__('性能测试', '性能测试')}
        </Button>
        
        <Button
          variant="secondary"
          onClick={handleResetToRecommended}
        >
          {__('重置为推荐设置', '重置为推荐设置')}
        </Button>
      </div>

      {/* 性能建议 */}
      <div className="notion-wp-settings-section">
        <h3>{__('💡 性能优化建议', '💡 性能优化建议')}</h3>
        <div className="performance-tips">
          <div className="tip-card">
            <h4>{__('🏃‍♂️ 提高速度', '🏃‍♂️ 提高速度')}</h4>
            <ul>
              <li>{__('增加API分页大小和并发请求数', '增加API分页大小和并发请求数')}</li>
              <li>{__('启用缓存和批量处理', '启用缓存和批量处理')}</li>
              <li>{__('使用增量同步而非完全同步', '使用增量同步而非完全同步')}</li>
            </ul>
          </div>
          
          <div className="tip-card">
            <h4>{__('🛡️ 提高稳定性', '🛡️ 提高稳定性')}</h4>
            <ul>
              <li>{__('减少并发请求数', '减少并发请求数')}</li>
              <li>{__('增加请求超时时间和重试次数', '增加请求超时时间和重试次数')}</li>
              <li>{__('启用内存限制检查和智能限流', '启用内存限制检查和智能限流')}</li>
            </ul>
          </div>
          
          <div className="tip-card">
            <h4>{__('⚖️ 平衡配置', '⚖️ 平衡配置')}</h4>
            <ul>
              <li>{__('使用推荐的默认设置', '使用推荐的默认设置')}</li>
              <li>{__('根据服务器性能逐步调整', '根据服务器性能逐步调整')}</li>
              <li>{__('定期进行性能测试', '定期进行性能测试')}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
