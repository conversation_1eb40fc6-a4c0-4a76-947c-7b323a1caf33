<?php
declare(strict_types=1);

namespace NTWP\Utils;

use NTWP\Core\Task\ModernAsyncEngine;

/**
 * 异步处理助手类
 *
 * 提供异步任务处理的便捷方法，封装ModernAsyncEngine的复杂性
 * 支持页面导入、图片处理、增量同步等常见异步操作
 *
 * 职责范围：
 * - 异步任务的便捷启动方法
 * - 任务进度查询和状态管理
 * - 系统状态检查和资源评估
 * - 批次大小智能推荐
 *
 * @since      2.0.0-beta.1
 * @version    2.0.0-beta.2
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-Loong/Notion-to-WordPress
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class AsyncHelper {
    
    /**
     * 异步导入页面
     *
     * @param array $pages 页面数据数组
     * @param array $options 选项配置
     * @return string 任务ID
     */
    public static function importPagesAsync(array $pages, array $options = []): string {
        if (class_exists('NTWP\\Core\\Task\\ModernAsyncEngine')) {
            // 使用现代异步引擎
            return ModernAsyncEngine::execute('import_pages', $pages, [
                'batch_size' => $options['batch_size'] ?? 20,
                'timeout' => $options['timeout'] ?? 300,
                'priority' => ModernAsyncEngine::PRIORITY_NORMAL
            ]);
        } else {
            // 回退到同步处理
            return self::processSynchronously('import_pages', $pages, $options);
        }
    }
    
    /**
     * 异步更新页面
     * 
     * @param array $pages 页面数据数组
     * @param array $options 选项配置
     * @return string 任务ID
     */
    public static function updatePagesAsync(array $pages, array $options = []): string {
        if (class_exists('NTWP\\Core\\Task\\ModernAsyncEngine')) {
            return ModernAsyncEngine::execute('update_pages', $pages, [
                'batch_size' => $options['batch_size'] ?? 15,
                'timeout' => $options['timeout'] ?? 300,
                'priority' => ModernAsyncEngine::PRIORITY_HIGH
            ]);
        } else {
            return self::processSynchronously('update_pages', $pages, $options);
        }
    }
    
    /**
     * 异步处理图片
     * 
     * @param array $images 图片数据数组
     * @param array $options 选项配置
     * @return string 任务ID
     */
    public static function processImagesAsync(array $images, array $options = []): string {
        if (class_exists('NTWP\\Core\\Task\\ModernAsyncEngine')) {
            return ModernAsyncEngine::execute('process_images', $images, [
                'batch_size' => $options['batch_size'] ?? 10,
                'timeout' => $options['timeout'] ?? 600,
                'priority' => ModernAsyncEngine::PRIORITY_LOW
            ]);
        } else {
            return self::processSynchronously('process_images', $images, $options);
        }
    }
    
    /**
     * 异步删除文章
     * 
     * @param array $posts 文章数据数组
     * @param array $options 选项配置
     * @return string 任务ID
     */
    public static function deletePostsAsync(array $posts, array $options = []): string {
        if (class_exists('NTWP\\Core\\Task\\ModernAsyncEngine')) {
            return ModernAsyncEngine::execute('delete_posts', $posts, [
                'batch_size' => $options['batch_size'] ?? 25,
                'timeout' => $options['timeout'] ?? 180,
                'priority' => ModernAsyncEngine::PRIORITY_NORMAL
            ]);
        } else {
            return self::processSynchronously('delete_posts', $posts, $options);
        }
    }
    
    /**
     * 增量同步
     * 
     * @param array $data 同步数据
     * @param array $options 选项配置
     * @return string 任务ID
     */
    public static function syncIncrementalAsync(array $data, array $options = []): string {
        if (class_exists('NTWP\\Core\\Task\\ModernAsyncEngine')) {
            return ModernAsyncEngine::execute('sync_incremental', $data, [
                'batch_size' => $options['batch_size'] ?? 30,
                'timeout' => $options['timeout'] ?? 300,
                'priority' => ModernAsyncEngine::PRIORITY_HIGH
            ]);
        } else {
            return self::processSynchronously('sync_incremental', $data, $options);
        }
    }
    
    /**
     * 获取任务进度
     *
     * @param string $taskId 任务ID
     * @return array 进度信息
     */
    public static function getTaskProgress(string $taskId): array {
        // 优先使用ModernAsyncEngine（推荐方式）
        if (class_exists('NTWP\\Core\\Task\\ModernAsyncEngine')) {
            return ModernAsyncEngine::getProgress($taskId);
        }

        // 回退到直接使用ProgressTracker
        if (class_exists('NTWP\\Core\\Performance\\ProgressTracker')) {
            $tracker = new \NTWP\Core\Performance\ProgressTracker();
            return $tracker->getProgress($taskId);
        }

        // 最后的回退
        return [
            'status' => 'unavailable',
            'error' => '进度跟踪系统不可用',
            'progress' => ['percentage' => 0]
        ];
    }
    
    /**
     * 取消任务
     * 
     * @param string $taskId 任务ID
     * @return bool 是否成功取消
     */
    public static function cancelTask(string $taskId): bool {
        if (class_exists('NTWP\\Core\\Task\\ModernAsyncEngine')) {
            return ModernAsyncEngine::cancel($taskId);
        } else {
            return false;
        }
    }
    
    /**
     * 获取系统状态
     *
     * @return array 系统状态
     */
    public static function getSystemStatus(): array {
        // 优先使用ModernAsyncEngine的状态接口
        if (class_exists('NTWP\\Core\\Task\\ModernAsyncEngine')) {
            return \NTWP\Core\Task\ModernAsyncEngine::getStatus();
        }

        // 回退到CacheManager状态
        if (class_exists('NTWP\\Infrastructure\\Cache\\CacheManager')) {
            return \NTWP\Infrastructure\Cache\CacheManager::getStatus();
        }

        // 最后的回退
        return [
            'queue_size' => 0,
            'active_tasks' => [],
            'system_load' => 0.0,
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'engine_available' => false
        ];
    }
    
    /**
     * 检查是否使用现代异步引擎
     * 
     * @return bool 是否使用现代引擎
     */
    public static function isModernEngineAvailable(): bool {
        return class_exists('NTWP\\Core\\Task\\ModernAsyncEngine');
    }
    
    /**
     * 获取推荐的批次大小
     * 
     * @param string $operation 操作类型
     * @param int $dataCount 数据数量
     * @return int 推荐的批次大小
     */
    public static function getRecommendedBatchSize(string $operation, int $dataCount): int {
        // 基于操作类型和系统资源的智能批次大小计算
        $memoryLimit = self::getMemoryLimitInBytes();
        $availableMemory = $memoryLimit - memory_get_usage(true);
        
        $baseSizes = [
            'import_pages' => 20,
            'update_pages' => 15,
            'process_images' => 10,
            'delete_posts' => 25,
            'sync_incremental' => 30,
            'cleanup_data' => 50
        ];
        
        $baseSize = $baseSizes[$operation] ?? 20;
        
        // 根据可用内存调整
        if ($availableMemory < 64 * 1024 * 1024) { // 小于64MB
            $baseSize = max(5, intval($baseSize * 0.5));
        } elseif ($availableMemory > 256 * 1024 * 1024) { // 大于256MB
            $baseSize = intval($baseSize * 1.5);
        }
        
        // 确保不超过数据总量
        return min($baseSize, $dataCount);
    }
    

    
    /**
     * 同步处理（回退方案）
     */
    private static function processSynchronously(string $operation, array $data, array $options): string {
        $taskId = sprintf('sync_%s_%s', $operation, uniqid());

        \NTWP\Core\Foundation\Logger::warningLog(
            sprintf('现代异步引擎不可用，使用同步处理: %s, 数据量: %d', $operation, count($data)),
            'Async Helper'
        );

        // 这里可以实现基础的同步处理逻辑作为最后的回退方案
        // 实际项目中应该调用相应的同步处理方法

        return $taskId;
    }
    
    /**
     * 获取内存限制（字节）
     */
    private static function getMemoryLimitInBytes(): int {
        $limit = ini_get('memory_limit');
        if ($limit === '-1') {
            return PHP_INT_MAX;
        }
        
        $value = (int) $limit;
        $unit = strtolower(substr($limit, -1));
        
        switch($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return $value;
        }
    }
}
