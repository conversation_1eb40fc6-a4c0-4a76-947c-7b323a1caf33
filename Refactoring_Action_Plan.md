# Notion-to-WordPress 重构行动计划

## 🎯 重构目标

基于代码质量分析报告，制定具体的重构行动计划，分阶段实施代码优化和架构改进。

## 📅 第一阶段：清理和统一 (第1-3周)

### 🔧 任务1: 移除废弃的SmartCache类

#### 步骤1: 分析SmartCache使用情况
```bash
# 搜索SmartCache的所有引用
grep -r "SmartCache" includes/ admin/ --include="*.php"
grep -r "Smart_Cache" includes/ admin/ --include="*.php"
```

#### 步骤2: 替换SmartCache调用
**目标文件**: `includes/services/Api/NotionApi.php`
- 第843行: `$use_smart_cache = false;` 
- 第858-870行: SmartCache::get() 调用
- 第920-930行: SmartCache::set() 调用
- 第2855-2870行: get_cache_strategy() 方法

**替换方案**:
```php
// 原代码
$cached_response = \NTWP\Utils\SmartCache::get($cache_strategy['type'], $cache_key);

// 新代码  
$cached_response = \NTWP\Infrastructure\Cache\CacheManager::get($cache_key, $cache_strategy['type']);
```

#### 步骤3: 更新Container服务注册
**目标文件**: `includes/core/Foundation/Container.php`
- 移除第220-223行的smart_cache_adapter注册
- 确保cache服务正确注册CacheManager

#### 步骤4: 删除SmartCache文件
- 删除 `includes/utils/SmartCache.php`
- 更新相关的require语句

### 🔧 任务2: 简化ImportHandler职责

#### 步骤1: 分析ImportHandler当前职责
**当前问题**:
- ImportHandler既是处理器又是适配器
- 包含大量兼容性方法 (第217-262行)
- 与ImportService功能重叠

#### 步骤2: 重构ImportHandler
**新的职责定义**:
```php
class ImportHandler {
    private ImportServiceInterface $importService;
    
    // 仅保留HTTP请求处理功能
    public function handle_import_request(array $request): array;
    public function handle_sync_request(array $request): array;
    public function handle_status_request(string $taskId): array;
    
    // 移除所有兼容性方法
    // 移除直接的业务逻辑处理
}
```

#### 步骤3: 更新Main.php依赖注入
**目标文件**: `includes/framework/Main.php`
- 第188行: 简化ImportHandler构造函数
- 移除不必要的依赖注入

### 🔧 任务3: 统一异常处理机制

#### 步骤1: 创建异常类层次结构
**新增文件**: `includes/core/Foundation/Exceptions/`

```php
// NotionException.php
<?php
declare(strict_types=1);

namespace NTWP\Core\Foundation\Exceptions;

abstract class NotionException extends \Exception {
    protected array $context = [];
    protected string $errorCode = '';
    
    public function __construct(string $message = '', string $errorCode = '', array $context = [], \Throwable $previous = null) {
        parent::__construct($message, 0, $previous);
        $this->errorCode = $errorCode;
        $this->context = $context;
    }
    
    public function getContext(): array {
        return $this->context;
    }
    
    public function getErrorCode(): string {
        return $this->errorCode;
    }
    
    public function toArray(): array {
        return [
            'error' => true,
            'message' => $this->getMessage(),
            'code' => $this->getErrorCode(),
            'context' => $this->getContext(),
            'file' => $this->getFile(),
            'line' => $this->getLine()
        ];
    }
}
```

#### 步骤2: 创建具体异常类
```php
// ApiException.php
class ApiException extends NotionException {
    private ?int $httpCode = null;
    private ?array $apiResponse = null;
    
    public function setHttpCode(int $code): self {
        $this->httpCode = $code;
        return $this;
    }
    
    public function setApiResponse(array $response): self {
        $this->apiResponse = $response;
        return $this;
    }
}

// ImportException.php  
class ImportException extends NotionException {
    private ?string $pageId = null;
    private ?string $operation = null;
    
    public function setPageId(string $pageId): self {
        $this->pageId = $pageId;
        return $this;
    }
    
    public function setOperation(string $operation): self {
        $this->operation = $operation;
        return $this;
    }
}

// ValidationException.php
class ValidationException extends NotionException {
    private array $validationErrors = [];
    
    public function setValidationErrors(array $errors): self {
        $this->validationErrors = $errors;
        return $this;
    }
    
    public function getValidationErrors(): array {
        return $this->validationErrors;
    }
}
```

#### 步骤3: 更新现有代码使用新异常
**目标文件**: `includes/services/Api/NotionApi.php`
- 第756-830行: send_request_with_merging() 方法
- 第841-950行: send_request() 方法
- 替换所有 `throw new \Exception()` 为具体的异常类型

### 🔧 任务4: 统一命名规范

#### 步骤1: 类名规范化
**需要重命名的类**:
- `Notion_To_WordPress` → `NotionToWordPress`
- `Notion_To_WordPress_Loader` → `NotionToWordPressLoader`  
- `Notion_To_WordPress_I18n` → `NotionToWordPressI18n`

#### 步骤2: 方法名规范化
**目标文件**: `includes/handlers/ImportHandler.php`
- `import_notion_page()` → `importNotionPage()`
- `import_pages()` → `importPages()`
- `updateProgressStatus()` → `updateProgressStatus()` (已符合规范)

#### 步骤3: 常量规范化
**目标文件**: `notion-to-wordpress.php`
- 确保所有常量使用UPPER_SNAKE_CASE
- 统一常量前缀为NOTION_TO_WORDPRESS_

## 📅 第二阶段：核心类重构 (第4-6周)

### 🔧 任务5: 拆分NotionApi类

#### 步骤1: 创建ApiClient基础类
**新增文件**: `includes/services/Api/ApiClient.php`

```php
<?php
declare(strict_types=1);

namespace NTWP\Services\Api;

use NTWP\Core\Foundation\Exceptions\ApiException;

class ApiClient implements ApiClientInterface {
    private string $apiKey;
    private string $apiBase = 'https://api.notion.com/v1/';
    
    public function __construct(string $apiKey = '') {
        $this->apiKey = $apiKey;
    }
    
    public function sendRequest(string $endpoint, string $method = 'GET', array $data = []): array {
        $url = $this->apiBase . ltrim($endpoint, '/');
        
        $args = [
            'method' => $method,
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'Notion-Version' => '2022-06-28'
            ],
            'timeout' => 30
        ];
        
        if (!empty($data) && $method !== 'GET') {
            $args['body'] = json_encode($data);
        }
        
        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            throw new ApiException(
                'HTTP request failed: ' . $response->get_error_message(),
                'HTTP_ERROR'
            );
        }
        
        $body = wp_remote_retrieve_body($response);
        $decoded = json_decode($body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new ApiException(
                'Invalid JSON response: ' . json_last_error_msg(),
                'JSON_ERROR'
            );
        }
        
        $httpCode = wp_remote_retrieve_response_code($response);
        if ($httpCode >= 400) {
            throw (new ApiException(
                $decoded['message'] ?? 'API request failed',
                $decoded['code'] ?? 'API_ERROR'
            ))->setHttpCode($httpCode)->setApiResponse($decoded);
        }
        
        return $decoded;
    }
    
    public function setApiKey(string $apiKey): void {
        $this->apiKey = $apiKey;
    }
    
    public function getApiKey(): string {
        return $this->apiKey;
    }
}
```

#### 步骤2: 创建ApiCache类
**新增文件**: `includes/services/Api/ApiCache.php`

```php
<?php
declare(strict_types=1);

namespace NTWP\Services\Api;

use NTWP\Infrastructure\Cache\CacheManager;

class ApiCache {
    private CacheManager $cacheManager;
    
    public function __construct(CacheManager $cacheManager) {
        $this->cacheManager = $cacheManager;
    }
    
    public function getCachedResponse(string $endpoint, array $params = []): ?array {
        $cacheKey = $this->generateCacheKey($endpoint, $params);
        $strategy = $this->getCacheStrategy($endpoint);
        
        if (!$strategy['cacheable']) {
            return null;
        }
        
        return $this->cacheManager->get($cacheKey, $strategy['type']);
    }
    
    public function setCachedResponse(string $endpoint, array $params, array $response, ?int $ttl = null): bool {
        $cacheKey = $this->generateCacheKey($endpoint, $params);
        $strategy = $this->getCacheStrategy($endpoint);
        
        if (!$strategy['cacheable']) {
            return false;
        }
        
        $effectiveTtl = $ttl ?? $strategy['ttl'];
        return $this->cacheManager->set($cacheKey, $response, $effectiveTtl, $strategy['type']);
    }
    
    public function getCacheStrategy(string $endpoint): array {
        // 用户信息 - 长期缓存
        if (strpos($endpoint, '/users/') !== false) {
            return ['type' => 'user_info', 'cacheable' => true, 'ttl' => 3600];
        }
        
        // 数据库结构 - 中期缓存
        if (strpos($endpoint, '/databases/') !== false) {
            return ['type' => 'database_structure', 'cacheable' => true, 'ttl' => 1800];
        }
        
        // 页面内容 - 短期缓存
        if (strpos($endpoint, '/pages/') !== false) {
            return ['type' => 'page_content', 'cacheable' => true, 'ttl' => 300];
        }
        
        // 默认策略
        return ['type' => 'api_response', 'cacheable' => true, 'ttl' => 60];
    }
    
    private function generateCacheKey(string $endpoint, array $params): string {
        return 'api_' . md5($endpoint . serialize($params));
    }
}
```

#### 步骤3: 逐步迁移NotionApi功能
1. 将基础HTTP通信迁移到ApiClient
2. 将缓存逻辑迁移到ApiCache  
3. 将重试逻辑迁移到ApiRetry
4. 将合并逻辑迁移到ApiMerger
5. 保留NotionApi作为高级服务门面

## 📅 第三阶段：架构优化 (第7-9周)

### 🔧 任务6: 定义核心接口

#### 步骤1: 创建接口文件
**新增目录**: `includes/contracts/`

#### 步骤2: 实现服务工厂
**新增文件**: `includes/core/Foundation/ServiceFactory.php`

#### 步骤3: 更新依赖注入
修改Container.php以使用接口而非具体实现

## 📊 进度跟踪

### 第一阶段检查点
- [ ] SmartCache完全移除
- [ ] ImportHandler职责简化
- [ ] 异常处理统一
- [ ] 命名规范统一
- [ ] 所有测试通过

### 第二阶段检查点  
- [ ] NotionApi成功拆分
- [ ] AdminController重构完成
- [ ] 性能指标改善
- [ ] 代码复杂度降低

### 第三阶段检查点
- [ ] 接口定义完成
- [ ] 服务工厂实现
- [ ] 依赖注入优化
- [ ] 架构文档更新

## 🔍 质量保证

### 每个任务完成后检查
1. **功能测试**: 确保现有功能正常工作
2. **性能测试**: 确保性能没有退化
3. **代码审查**: 确保代码质量提升
4. **文档更新**: 更新相关文档

### 回滚计划
每个阶段开始前创建代码备份，如果出现问题可以快速回滚到稳定状态。

---

## 📝 注意事项

1. **向后兼容**: 重构过程中保持API兼容性
2. **渐进式改进**: 分小步骤进行，避免大规模破坏性更改
3. **测试驱动**: 每个更改都要有相应的测试验证
4. **文档同步**: 及时更新文档和注释

这个行动计划提供了具体的实施步骤和检查点，确保重构过程可控、可测试、可回滚。
